from core.admin import AbstractBaseAdmin


class WorkShiftAdmin(AbstractBaseAdmin):
    list_display = (
        "service_center",
        "first_shift_start",
        "first_shift_end",
        "second_shift_start",
        "second_shift_end",
    ) + AbstractBaseAdmin.default_list_display
    search_fields = ("service_center__user__username", "service_center__name")
    list_filter = ("service_center__name",)
    raw_id_fields = ("service_center",)
    list_editable = ()
    fieldsets = (
        (
            "First shifts",
            {
                "fields": (
                    ("first_shift_start", "first_shift_end"),
                    (
                        "week_days_first_shift",
                        "first_weekend_first_shift",
                        "second_weekend_first_shift",
                        "holidays_first_shift",
                    ),
                )
            },
        ),
        (
            "Second shifts",
            {
                "fields": (
                    ("second_shift_start", "second_shift_end"),
                    (
                        "week_days_second_shift",
                        "first_weekend_second_shift",
                        "second_weekend_second_shift",
                        "holidays_second_shift",
                    ),
                )
            },
        ),
    )
