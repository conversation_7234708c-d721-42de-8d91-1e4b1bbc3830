from django.contrib import admin

from translation.utils.admin_form import AdminTranslationInline
from ..models import ServiceCenterShiftTranslation


class ServiceCenterShiftTranslationInline(AdminTranslationInline):
    model = ServiceCenterShiftTranslation


class ServiceCenterShiftAdmin(admin.ModelAdmin):
    list_display = ('id', 'name')
    inlines = [ServiceCenterShiftTranslationInline]

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.prefetch_related('translations')
