from core import admin
from service_center.models.car_type_percentage import CarTypePercentage


class CarTypePercentageInline(admin.StackedInline):
    model = CarTypePercentage
    extra = 0
    fieldsets = (
        (
            None,
            {"fields": (
                "german_cars_percent",
                "japanese_cars_percent",
                "korean_cars_percent",
                "chinese_cars_percent",
                "iranian_cars_percent",
            )},
        ),
    )
    ordering = ("-priority",)