from core import admin
from service_center.models.car_type_percentage import CarTypePercentage


class CarTypePercentageAdmin(admin.AbstractBaseAdmin):
    fieldsets = (
        (
            None,
            {"fields": (
                "german_cars_percent",
                "japanese_cars_percent",
                "korean_cars_percent",
                "chinese_cars_percent",
                "iranian_cars_percent",
                "service_center",

            )},
        ),
    )
    search_fields = ("service_center__name",)
    raw_id_fields = (
        "service_center",
    )
    ordering = ("-priority",)