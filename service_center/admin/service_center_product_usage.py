from core.admin import AbstractBaseAdmin


class ServiceCenterProductUsageAdmin(AbstractBaseAdmin):
    list_display = (
        "service_center",
        "category",
        "type",
        "product",
        "shelves_count",
    ) + AbstractBaseAdmin.default_list_display
    list_editable = ("is_active",)
    readonly_fields = ("created_at",)
    list_filter = ("category", "type")
    raw_id_fields = ("service_center",)
    search_fields = ("service_center__user__username",)
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "is_active",
                    "service_center",
                    ("category", "type", "product"),
                    ("shelves_count", "priority"),
                    "created_at",
                )
            },
        ),
    )
