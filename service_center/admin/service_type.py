from core.admin import AbstractBaseAdmin
from translation.utils.admin_form import  AdminTranslationInline
from ..models import ServiceTypeTranslation


class ServiceTypeTranslationInline(AdminTranslationInline):
    model = ServiceTypeTranslation



class ServiceTypeAdmin(AbstractBaseAdmin):
    list_display = ("id", "name", "type")
    list_editable = ()
    fieldsets = ((None, {"fields": ("is_active", "name", ("category", "type"), ("created_at", "updated_at"))}),)
    inlines = [ServiceTypeTranslationInline]
    raw_id_fields = ("category",)

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.prefetch_related("translations")
