from core.admin import AbstractBaseAdmin


class ServiceCenterServiceCategoryAdmin(AbstractBaseAdmin):
    list_display = ("service_center", "service_category", "part", "service", "count")
    list_editable = ("part", "service", "count")
    raw_id_fields = ("service_center", "service_category")
    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("created_at", "updated_at"),
                    ("is_active", "priority"),
                    "service_center",
                    "service_category",
                    "part",
                    "service",
                    "count"
                )
            },
        ),
    )
