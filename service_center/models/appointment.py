import datetime

from core import models


class AppointmentQuerySet(models.QuerySet):
    def is_active(self):
        return self.filter(date__lt=(datetime.datetime.now() + datetime.timedelta(hours=3, minutes=30)))

    def is_deactive(self):
        return self.filter(date__gt=(datetime.datetime.now() + datetime.timedelta(hours=3, minutes=30)))


class Appointment(models.AbstractBaseModel):
    user = models.OneToOneField("accounts.User", on_delete=models.PROTECT, related_name="appointments")
    service_center = models.ForeignKey(
        "service_center.ServiceCenter", on_delete=models.PROTECT, related_name="appointments"
    )
    date = models.DateTimeField()

    objects = AppointmentQuerySet.as_manager()

    class Meta:
        db_table = "matrix_service_center_appointment"

    def __str__(self):
        return "{}".format(self.user.username)
