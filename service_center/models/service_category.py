from core import models

from service_center.models import ServiceType


class ServiceCategory(models.AbstractBaseModel):
    name = models.CharField(max_length=250)
    part_types = models.ManyToManyField(ServiceType, related_name="part_categories", blank=True)
    service_types = models.ManyToManyField(ServiceType, related_name="service_categories", blank=True)

    class Meta:
        db_table = "matrix_service_center_service_category"

    def __str__(self) -> str:
        return self.name
