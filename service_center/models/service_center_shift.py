from core import models
from translation.models import Translation


class ServiceCenterShift(models.AbstractBaseModel):
    name = models.CharField(max_length=10)
    start_time = models.TimeField()
    end_time = models.TimeField()

    def __str__(self):
        return "{}|{}|{}".format(self.name, self.start_time, self.end_time)


class ServiceCenterShiftTranslation(Translation):
    service_center_shift = models.ForeignKey(ServiceCenterShift, on_delete=models.CASCADE, related_name="translations")
    name = models.CharField(max_length=10, null=True, blank=True)

    class Meta:
        db_table = "matrix_service_center_service_center_shift_translations"
        unique_together = (("lang_code", "service_center_shift"),)

    def __str__(self):
        return "{}|{}".format(self.name, self.service_center_shift_id)

    @property
    def get_translation_fields(self):
        return {
            "name": self.name
        }
