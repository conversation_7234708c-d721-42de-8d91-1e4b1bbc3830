from core import models


class ServiceCenterConfig(models.AbstractBaseModel):
    service_center = models.OneToOneField(
        "service_center.ServiceCenter",
        on_delete=models.CASCADE,
        related_name="config", blank=True
    )
    off_days = models.ManyToManyField("content.Date", blank=True)
    max_capacity = models.PositiveIntegerField(default=100)
    sat = models.ManyToManyField("service_center.ServiceCenterShift", related_name="service_center_config_sat",
                                 blank=True)
    sun = models.ManyToManyField("service_center.ServiceCenterShift", related_name="service_center_config_sun",
                                 blank=True)
    mon = models.ManyToManyField("service_center.ServiceCenterShift", related_name="service_center_config_mon",
                                 blank=True)
    tue = models.ManyToManyField("service_center.ServiceCenterShift", related_name="service_center_config_tue",
                                 blank=True)
    wed = models.ManyToManyField("service_center.ServiceCenterShift", related_name="service_center_config_wed",
                                 blank=True)
    thu = models.ManyToManyField("service_center.ServiceCenterShift", related_name="service_center_config_thu",
                                 blank=True)
    fri = models.ManyToManyField("service_center.ServiceCenterShift", related_name="service_center_config_fri",
                                 blank=True)

    rush_hour = models.ManyToManyField("service_center.ServiceCenterShift", related_name="service_center_config_rush",
                                       blank=True)
    happy_hour = models.ManyToManyField("service_center.ServiceCenterShift", related_name="service_center_config_happy",
                                        blank=True)

    def __str__(self):
        return "{}|{}".format(self.id, self.service_center)
