from core import models


class WorkShift(models.AbstractBaseModel):
    service_center = models.OneToOneField(
        "service_center.ServiceCenter", on_delete=models.PROTECT, related_name="work_shift"
    )

    cars_per_hour = models.IntegerField(default=10)

    first_shift_start = models.TimeField()
    first_shift_end = models.TimeField()
    second_shift_start = models.TimeField()
    second_shift_end = models.TimeField()
    week_days_first_shift = models.BooleanField(default=False)
    week_days_second_shift = models.BooleanField(default=False)
    first_weekend_first_shift = models.BooleanField(default=False)
    first_weekend_second_shift = models.BooleanField(default=False)
    second_weekend_first_shift = models.BooleanField(default=False)
    second_weekend_second_shift = models.BooleanField(default=False)
    holidays_first_shift = models.BooleanField(default=False)
    holidays_second_shift = models.BooleanField(default=False)

    class Meta:
        db_table = "matrix_service_center_work_shift"

    def __str__(self):
        return self.service_center.name
