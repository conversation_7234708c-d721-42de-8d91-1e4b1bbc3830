from core import models


class ServiceCenterServiceTypeCapacity(models.AbstractBaseModel):
    service_center_service_type = models.ForeignKey(
        "service_center.ServiceCenterServiceType",
        on_delete=models.CASCADE,
        related_name="capacities"
    )
    capacity = models.PositiveIntegerField(default=10)
    shift = models.ForeignKey(
        "service_center.ServiceCenterShift",
        on_delete=models.CASCADE,
        related_name="service_center_service_type_capacities"
    )

    class Meta:
        unique_together = ("service_center_service_type", "shift")

    def __str__(self):
        return "{}|{}".format(self.service_center_service_type.service_type, self.shift)
