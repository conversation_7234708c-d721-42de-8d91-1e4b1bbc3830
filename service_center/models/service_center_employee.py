from core import models
from django.core.exceptions import ValidationError


class ServiceCenterEmployee(models.AbstractBaseModel):
    user = models.OneToOneField("accounts.User", on_delete=models.PROTECT, related_name="employee", primary_key=False)
    service_center = models.ForeignKey(
        "service_center.ServiceCenter", on_delete=models.PROTECT, related_name="employees"
    )

    class Meta:
        db_table = "matrix_service_center_service_center_employee"

    def __str__(self):
        return self.user.username

    def save(self, *args, **kwargs):
        from service_center.models import ServiceCenter

        if ServiceCenter.objects.filter(user=self.user).exists():
            raise ValidationError("This phone number cannot be added as an employee.")
        super().save(*args, **kwargs)
