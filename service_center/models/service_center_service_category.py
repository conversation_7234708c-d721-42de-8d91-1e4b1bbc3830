from core import models

from service_center.models import ServiceCategory, ServiceCenter


class ServiceCenterServiceCategory(models.AbstractBaseModel):
    service_center = models.ForeignKey(ServiceCenter, on_delete=models.CASCADE, related_name="service_categories")
    service_category = models.ForeignKey(ServiceCategory, on_delete=models.CASCADE, related_name="service_centers")
    part = models.BooleanField(default=False)
    service = models.BooleanField(default=False)
    count = models.PositiveIntegerField(default=0)

    class Meta:
        unique_together = ("service_center", "service_category")
        db_table = "matrix_service_center_service_center_service_category"

    def __str__(self):
        return "[{}] {} {}".format(self.id, self.service_center, self.service_category)
