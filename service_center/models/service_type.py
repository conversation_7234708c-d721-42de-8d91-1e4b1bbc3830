from constants import ServiceDeliveryType
from core import models
from translation.models import Translation


class ServiceType(models.AbstractBaseModel):
    name = models.CharField(max_length=250)
    type = models.CharField(max_length=50, choices=ServiceDeliveryType.choices, default=ServiceDeliveryType.SERVICE)
    category = models.ForeignKey(
        "quick_book.Category",
        related_name="service_types",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "matrix_service_center_service_type"

    def __str__(self):
        return self.name


class ServiceTypeTranslation(Translation):
    service_type = models.ForeignKey(ServiceType, on_delete=models.CASCADE, related_name="translations")
    name = models.Char<PERSON>ield(max_length=250, null=True, blank=True)

    def __str__(self):
        return "{}|{}".format(self.name, self.service_type_id)

    class Meta:
        db_table = "matrix_service_center_service_type_translation"
        unique_together = (("lang_code", "service_type"),)

    @property
    def get_translation_fields(self):
        return {
            "name": self.name,
        }
