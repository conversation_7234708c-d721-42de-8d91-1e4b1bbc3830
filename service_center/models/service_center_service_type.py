from core import models


class ServiceCenterServiceType(models.AbstractBaseModel):
    service_center = models.ForeignKey(
        "service_center.ServiceCenter",
        on_delete=models.CASCADE,
        related_name="service_types"
    )
    service_type = models.ForeignKey(
        "service_center.ServiceType",
        on_delete=models.PROTECT,
        related_name="service_center_service_types"
    )

    def __str__(self):
        return "{}|{}".format(self.service_center, self.service_type)
