from core import models
from django.core.validators import MaxValueValidator, MinValueValidator

class CarTypePercentage(models.AbstractBaseModel):
    service_center = models.OneToOneField('service_center.ServiceCenter', related_name='car_type_percentages', null=True, blank=True, on_delete=models.PROTECT)

    german_cars_percent = models.IntegerField(
        default=0,
        validators=[
            MaxValueValidator(100),
            MinValueValidator(0)
        ]
     )
    japanese_cars_percent = models.IntegerField(
        default=0,
        validators=[
            MaxValueValidator(100),
            MinValueValidator(0)
        ]
     )
    korean_cars_percent = models.IntegerField(
        default=0,
        validators=[
            MaxValueValidator(100),
            MinValueValidator(0)
        ]
     )
    chinese_cars_percent = models.IntegerField(
        default=0,
        validators=[
            MaxValueValidator(100),
            MinValueValidator(0)
        ]
     )
    iranian_cars_percent = models.IntegerField(
        default=0,
        validators=[
            MaxValueValidator(100),
            MinValueValidator(0)
        ]
     )

    class Meta:
        db_table = "matrix_service_center_car_type_percentage"

    def __str__(self) -> str:
        return f"{self.service_center.name} [{self.service_center.id}]"
