from dataclasses import dataclass
from typing import List

from dataclasses_json import dataclass_json, LetterCase, Undefined


@dataclass_json(letter_case=LetterCase.PASCAL, undefined=Undefined.EXCLUDE)
@dataclass
class CustomerInfo:
    national_code: str
    mobile_no: str
    chassis_no: str
    engine_no: str


@dataclass_json(letter_case=LetterCase.PASCAL, undefined=Undefined.EXCLUDE)
@dataclass
class SaleInfo:
    seller_national_code: str
    seller_commercial_code: str
    product_code: str
    product_price: int
    sale_amount: float


@dataclass_json(letter_case=LetterCase.PASCAL, undefined=Undefined.EXCLUDE)
@dataclass
class EngineOilAllocationInput:
    customer_info: List[CustomerInfo]
    sale_info: List[SaleInfo]
