from dataclasses import dataclass

from dataclasses_json import dataclass_json, LetterCase, Undefined

from content.models import Config as ConfigModel


@dataclass_json(letter_case=LetterCase.SNAKE, undefined=Undefined.EXCLUDE)
@dataclass
class ConfigItem:
    enabled: bool
    required: bool
    retries: int


@dataclass_json(letter_case=LetterCase.SNAKE, undefined=Undefined.EXCLUDE)
@dataclass
class CixConfig:
    get_allocation: ConfigItem
    get_sale_order: ConfigItem

    @classmethod
    def set(cls):
        config = ConfigModel.objects.get(key="cix_config").value
        return cls.from_dict(config)
