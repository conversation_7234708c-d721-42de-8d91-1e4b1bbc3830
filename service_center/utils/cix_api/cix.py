from ast import literal_eval
from time import sleep
from typing import List

from requests.auth import HTT<PERSON><PERSON>asic<PERSON>uth
from zeep import Client
from zeep.transports import Transport

from .types.config import CixConfig
from .types.sale_order import SaleInfo, CustomerInfo, EngineOilAllocationInput


class CixAPI:
    url = "https://sr-cix.ntsw.ir/services/TireEngineOilService?wsdl"

    header_credentials = {"UserName": "snapp_cix", "Password": "D6h4^46J"}
    body_credentials = {"UserName": "Snapp_User", "Password": "L*8@th!0%"}

    national_code = "14008347146"
    commercial_code = "2454353"

    @classmethod
    def set_body_credentials(cls, data: dict):
        data.update(cls.body_credentials)
        return data

    @classmethod
    def as_dict(cls, data):
        return literal_eval(str(data))

    @staticmethod
    def config() -> CixConfig:
        return CixConfig.set()

    @classmethod
    def client(cls) -> Client:
        transport = Transport(timeout=10)
        auth = HTTPBasicAuth(
            cls.header_credentials["UserName"],
            cls.header_credentials["Password"],
        )
        transport.session.auth = auth
        return Client(cls.url, transport=transport)

    @classmethod
    def get_sale_order(
        cls,
        customer_info: List[CustomerInfo],
        sale_info: List[SaleInfo],
    ):

        if not isinstance(customer_info, (list, tuple, set)):
            customer_info = [customer_info]

        if not isinstance(sale_info, (list, tuple, set)):
            sale_info = [sale_info]

        data = EngineOilAllocationInput(
            sale_info=sale_info, customer_info=customer_info
        ).to_dict()

        data = cls.set_body_credentials(data)

        result = None
        exception = False
        for _ in range(cls.config().get_allocation.retries):
            try:
                # _result = cls.client().service.GetSaleOrder(data)  # Deprecated
                _result = cls.client().service.GetOilSaleOrder(data)
                _result = cls.as_dict(_result)
                result = _result

                if _result and _result["ResultCode"] == 0:
                    break

            except Exception as e:
                exception = True
                result = e

            sleep(1)

        if result is None:
            raise ConnectionError("Error in connecting to cix API")

        elif exception:
            raise result

        return result

    @classmethod
    def get_allocation(
        cls,
        national_code: str,
        mobile_no: str,
        chassis_no: str,
        engine_no: str,
    ):

        data = {
            "CustomerInfo": {
                "NationalCode": national_code,
                "MobileNo": mobile_no
            },
            "FleetInfoInput": {
                "ChassisNo": chassis_no,
                "EngineNo": engine_no,
            }
        }

        data = cls.set_body_credentials(data)

        result = None
        exception = False
        for _ in range(cls.config().get_sale_order.retries):
            try:
                _result = cls.client().service.GetOilAllocation(data)
                _result = cls.as_dict(_result)
                result = _result

                if _result and _result["ResultCode"] == 0:
                    break

            except Exception as e:
                exception = True
                result = e

            sleep(1)

        if result is None:
            raise ConnectionError("Error in connecting to cix API")

        elif exception:
            raise result

        return result
