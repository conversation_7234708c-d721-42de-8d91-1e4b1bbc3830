import datetime

from core.models import F
from orders.models import OrderLine
from service_center.models import Bill<PERSON><PERSON>


def points_calculator(user):
    start_date = datetime.datetime(2021, 2, 25)
    end_date = datetime.datetime(2021, 3, 12)
    try:
        total = (
            BillItem.objects.filter(
                bill__car__user=user,
                bill__is_verified=True,
                content_type=OrderLine.get_content_type(),
                created_at__gte=start_date,
                created_at__lte=end_date,
                order_line__created_at__gte=start_date,
                order_line__created_at__lte=end_date,
            )
            .annotate(
                category_id=F("order_line__product__category_id"),
                order_line_amount=F("order_line__amount"),
            )
            .exclude(category_id=21)
            .get_sum("order_line_amount")
        )

        return total // 100000
    except Exception as e:
        return 0
