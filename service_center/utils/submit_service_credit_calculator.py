from service_center.utils.submit_service_credit_types import SubmitServiceCreditTypes


class SubmitServiceCreditCalculator:
    @staticmethod
    def bill(has_coupon):
        if has_coupon:
            return SubmitServiceCreditTypes.SUBMIT_COUPON_SERVICE
        else:
            return SubmitServiceCreditTypes.SUBMIT_REGULAR_SERVICE

    @staticmethod
    def new_user(has_coupon):
        if has_coupon:
            return SubmitServiceCreditTypes.NEW_COUPON_USER
        else:
            return SubmitServiceCreditTypes.NEW_REGULAR_USER
