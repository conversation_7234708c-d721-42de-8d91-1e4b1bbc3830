import logging

from accounts.models import Credit
from constants import CreditType
from service_center.utils import SubmitServiceCreditCalculator

logger = logging.getLogger("service_center")


def new_user_credits(bill, user, service_center):
    credit, message, title = SubmitServiceCreditCalculator.new_user(has_coupon=bill.is_coupon)
    try:
        Credit.increase(
            user=user,
            title=title.format(bill.car.user_id),
            amount=credit,
            credit_type=CreditType.SERVICE_CENTER_NEW_USER,
            content_type_model="user",
            object_id=bill.car.user_id,
            site=7,
            desc="SYSTEM NEW_USER {} {}".format(bill.car.user_id, message),
        )
    except Exception as e:
        logger.exception("service center bill={}".format(bill.id),
                         extra={"user": user})
    try:
        Credit.increase(
            user=service_center.agent.user,
            title=title.format(bill.car.user_id),
            amount=int((credit * 0.2) * 500),
            credit_type=CreditType.AGENT_NEW_USER,
            content_type_model="user",
            object_id=bill.car.user_id,
            site=6,
            desc="SYSTEM NEW_USER {} {}".format(bill.car.user_id, message),
        )
    except Exception as e:
        logger.exception("agent bill={} service_center={}".format(bill.id, service_center.id))
