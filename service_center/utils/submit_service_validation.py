import logging

from constants import ServiceType as ST, SupplyType
from orders.models import OrderLine
from service_center.models import ServiceType


logger = logging.getLogger("service_center")


class SubmitServiceValidation:
    @staticmethod
    def validate_data(data, service_center):
        bill_items = []
        order_line_ids = []
        order_types = service_center.get_order_types()
        user = None

        for item in data["items"]:
            if item.get("order_line_id"):
                _id = item.get("order_line_id")
                if _id in order_line_ids:
                    continue
                order_line_ids.append(_id.split("-")[0])

        # note: this print is to evaluate query and force it to hit the db
        print(OrderLine.objects.filter(id__in=order_line_ids).select_for_update())
        order_line_ids = []

        for item in data["items"]:
            if item.get("order_line_id"):
                _id = item.get("order_line_id")
                if _id in order_line_ids:
                    continue
                order_line_ids.append(_id)
                order_line = OrderLine.objects.select_related("product", "order__user").get_coupon(id=_id)
                if order_line.type not in order_types:
                    raise Exception("can not submit order type {}".format(order_line.type))
                if service_center.user.username == "09876543210" and order_line.order.user.username != "09876543210":
                    raise Exception("can not submit real orders for fake service center")
                if not order_line.is_usable:
                    raise Exception("order line {} could not be used more than quantity".format(order_line.id))

                # fixme: this is for products with cash_payment
                if not order_line.cash_payment:
                    product_id = order_line.product_id
                    city = order_line.order.city

                    parent_city = SubCity.objects.get(related_cities=city).parent_city

                    # note: bc we only make the prices updated on that site
                    seller_id = 1
                    site_id = 4
                    if order_line.order.site.id in [14]:
                        site_id = 14
                    try:
                        if not order_line.supply_type == SupplyType.SNAPPCARFIX:
                            charge_price = SellerProduct.objects.get(
                                product_id=product_id,
                                city=parent_city,
                                seller_id=seller_id,
                                site_id=site_id,
                            ).charge_price
                        else:
                            charge_price = 0
                    except Exception as e:
                        raise Exception(
                            "seller product not found. product={} city={} site={} seller={}".format(
                                product_id, city.pk, site_id, seller_id
                            )
                        )
                    if charge_price is None:
                        raise Exception(
                            "charge price not found. product={} city={} site={} seller={}".format(
                                product_id, city.id, site_id, seller_id
                            )
                        )
                else:
                    charge_price = 0

                _data = {
                    "content_type": OrderLine.get_content_type(),
                    "object_id": order_line.id,
                    "charge_price": charge_price,
                    "extra_charge": order_line.extra_charge,
                    "amount": item.get("amount"),
                    "wage": item.get("wage"),
                    "usage_limit": item.get("usage_limit"),
                    "usage_limit_type": item.get("usage_limit_type"),
                    "state": ST.CHANGE,
                }
                if order_line.product.single_coupon:
                    for _ in range(order_line.unusable_sum, order_line.quantity):
                        bill_items.append(_data)
                else:
                    bill_items.append(_data)
                user = order_line.order.user

            elif item.get("product_id"):
                bill_items.append(
                    {
                        "content_type": Product.get_content_type(),
                        "object_id": item.get("product_id"),
                        "charge_price": 0,
                        "extra_charge": 0,
                        "amount": item.get("amount"),
                        "wage": item.get("wage"),
                        "usage_limit": item.get("usage_limit"),
                        "usage_limit_type": item.get("usage_limit_type"),
                        "state": ST.CHANGE,
                    }
                )

            elif item.get("service_type_id"):
                bill_items.append(
                    {
                        "content_type": ServiceType.get_content_type(),
                        "object_id": item.get("service_type_id"),
                        "charge_price": 0,
                        "extra_charge": 0,
                        "amount": item.get("amount"),
                        "wage": item.get("wage"),
                        "state": ST.CHECK if item.get("state") == "check" else ST.CHANGE,
                    }
                )

            else:
                raise Exception("item type is not valid")
        if not bill_items:
            raise Exception("could not generate bill items list")
        return bill_items, user
