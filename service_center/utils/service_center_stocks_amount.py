from service_center.models import ServiceCenter, ServiceCenterStock
from products.models import Product


def service_center_stocks_amount(service_center, product, source_type):
    if type(service_center) == int:
        try:
            service_center = ServiceCenter.objects.get(id=service_center)
        except Exception as e:
            return str(e)
    if type(product) == int:
        try:
            product = ServiceCenter.objects.get(id=product)
        except Exception as e:
            return str(e)

    created_stock = ServiceCenterStock.objects.filter(
        service_center=service_center,
        type="created",
        source=source_type,
        status__in=["return", "create"],
    )
    delivered_stock = ServiceCenterStock.objects.filter(
        type="delivered", source=source_type, status__in=["return", "create"]
    )
