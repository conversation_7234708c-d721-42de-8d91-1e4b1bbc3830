from content.models import Config


def filter_service_center_based_on_domain(service_centers, site_id):
    #driver_sites = Config.objects.get(key="site_config").value["driver"]
    #passenger_sites = Config.objects.get(key="site_config").value["passenger"]
    driver_sites = [11, 2]
    passenger_sites = [4, 13, 10, 14]
    if site_id in driver_sites:
        return service_centers.filter(driver=True)
    if site_id in passenger_sites:
        return service_centers.filter(passenger=True)
