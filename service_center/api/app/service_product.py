import json

from django.db.models import F
from rest_framework.response import Response
from rest_framework.views import APIView

from quick_book.models import Product
from saleor.utils import Saleor
from service_center.models import ServiceCenter
from service_center.serializers.service_product import ServiceProductSerializer


class ServiceProductApi(APIView):
    def post(self, request):
        result = []
        products_data = request.data.get('products')
        if products_data is None:
            return Response({'error': 'products field is required'}, status=400)
        products_data = json.loads(products_data)
        saleor = Saleor(site='hig')
        for product_datum in products_data:
            service_center_id = product_datum['service_center_id']
            service_type = product_datum['service_type']
            product_id = product_datum['product_id']
            quantity = product_datum['quantity']
            service_center_shift_id = int(product_datum['service_center_shift_id'])

            product = Product.objects.get(saleor_id=product_id)
            if product.is_bundle:
                product_items = list(product.items.values(
                    'item_id', 'quantity',
                ).annotate(
                    product_id=F('item_id'),
                ))
            else:
                product_items = [{
                    'product_id': product.id,
                    'quantity': 1,
                }]

            service_fee_amount = 0
            for product_item in product_items:
                service_center = ServiceCenter.objects.select_related(
                    'config',
                ).get(id=service_center_id)
                product_service_center_config = service_center.product_service_center_configs.filter(
                    product_selling_config__product_id=product_item['product_id'],
                    product_selling_config__selling_config__type=service_type,
                    product_selling_config__is_active=True,
                    is_active=True
                ).first()
                service_center_config = service_center.config
                is_rush_hour = service_center_shift_id in service_center_config.rush_hour.values_list('id', flat=True)
                service_fee_amount += product_service_center_config.service_fee(happy_hour=not is_rush_hour) * \
                                      product_item['quantity']

            result.append({
                'product_id': product_id,
                'service_fee_amount': service_fee_amount,
                'service_fee_saleor_product_variant_id': saleor.service_fee_variant(service_fee_amount),
                'quantity': quantity,
            })

        serializer_data = ServiceProductSerializer(result, many=True).data

        return Response(serializer_data)
