import logging
from rest_framework.views import APIView
from translation.utils import translation_checker as tc, get_object_translation
from service_center.models import ServiceCenterServiceType, ServiceCenterConfig
from core.http import Response

logger = logging.getLogger("service_center")


class ServiceCenterDetailAPI(APIView):
    authentication_classes = ()

    def get(self, request):
        self.exception_data = {"message": "service center id is required"}
        sc_id = request.query_params["service_center_id"]
        service_center_type = ServiceCenterServiceType.objects.select_related(
            "service_type"
        ).filter(service_center_id=sc_id)
        service_types_name = []
        for sc_type in service_center_type:
            translated_name = get_object_translation(sc_type.service_type, request.lang)
            service_types_name.append(
                translated_name.get("name", sc_type.service_type.name)
            )
        self.exception_data = {"message": "ServiceCenter config not found"}
        service_center_config = ServiceCenterConfig.objects.get(service_center_id=sc_id)

        schedules = [
            {
                "day": tc("Saturday", request.lang),
                "shifts": [
                    {"start": sc_config.start_time.strftime("%H:%M"), "end": sc_config.end_time.strftime("%H:%M")}
                    for sc_config in
                    service_center_config.sat.all()]
            },
            {
                "day": tc("Sunday", request.lang),
                "shifts": [
                    {"start": sc_config.start_time.strftime("%H:%M"), "end": sc_config.end_time.strftime("%H:%M")}
                    for sc_config in
                    service_center_config.sun.all()]
            },
            {
                "day": tc("Monday", request.lang),
                "shifts": [
                    {"start": sc_config.start_time.strftime("%H:%M"), "end": sc_config.end_time.strftime("%H:%M")}
                    for sc_config in
                    service_center_config.mon.all()],
            },
            {
                "day": tc("Tuesday", request.lang),
                "shifts": [
                    {"start": sc_config.start_time.strftime("%H:%M"), "end": sc_config.end_time.strftime("%H:%M")}
                    for sc_config in
                    service_center_config.tue.all()]
            },
            {
                "day": tc("Wednesday", request.lang),
                "shifts": [
                    {"start": sc_config.start_time.strftime("%H:%M"), "end": sc_config.end_time.strftime("%H:%M")}
                    for sc_config in
                    service_center_config.wed.all()]
            },
            {
                "day": tc("Thursday", request.lang),
                "shifts": [
                    {"start": sc_config.start_time.strftime("%H:%M"), "end": sc_config.end_time.strftime("%H:%M")}
                    for sc_config in
                    service_center_config.thu.all()]
            },
            {
                "day": tc("Friday", request.lang),
                "shifts": [
                    {"start": sc_config.start_time.strftime("%H:%M"), "end": sc_config.end_time.strftime("%H:%M")}
                    for sc_config in
                    service_center_config.fri.all()]
            }
        ]

        return Response(
            data={
                "service_types_name": service_types_name,
                "schedules": schedules
            }
        )
