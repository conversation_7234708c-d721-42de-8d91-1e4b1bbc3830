from unidecode import unidecode
import logging
from django.utils.translation import gettext
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from accounts.models import User
from core.authentication import CustomAuthentication
from core.http import Response
from core.validators import mobile_number_validator
from saleor.utils.mobile_number_to_saleor_email import format_mobile_number
from service_center.models import ServiceCenterServiceType
from service_center.permissions import IsServiceCenter
from saleor.models import OrderLine
from service_center.serializers import LoadCustomerInfoSerializer
from saleor.utils import get_email

logger = logging.getLogger("service_center")


class LoadCustomerInfoAPI(APIView):
    authentication_classes = (CustomAuthentication,)
    permission_classes = (IsAuthenticated, IsServiceCenter)

    def get(self, request):

        self.exception_data = {"message": gettext("Invalid_mobile_number"), "code": 400, "status": 400}
        mobile_number = format_mobile_number(str(request.query_params["mobile_number"]))
        user, created = User.objects.get_or_create(username=mobile_number)
        has_order = OrderLine.objects.available_order_lines(
            get_email(mobile_number), request.service_center.id
        ).filter(product__category_id__in=list(
            ServiceCenterServiceType.objects.filter(service_center_id=request.service_center.id,
                                                    service_type__category__site__name="hig").values_list(
                "service_type__category_id"))
        ).exists()

        return Response(
            data=LoadCustomerInfoSerializer(user, has_order=has_order).data,
            message=gettext("order_found") if has_order else gettext("order_not_found")
        )

    def post(self, request):
        mobile_number = request.data["mobile_number"]
        first_name = request.data.get("first_name")
        last_name = request.data.get("last_name")

        if first_name and last_name is None:
            return Response(
                message=gettext("first_name_and_last_name_is_required"),
                status=400
            )
        self.exception_data = {"message": gettext("Invalid_mobile_number"), "code": 400, "status": 400}
        user = User.objects.only("first_name", "last_name").get(username=mobile_number)
        self.exception_data = {"message": gettext("Error while updating user"), "code": 400, "status": 400}
        if (user.first_name and user.last_name) is None:
            user.first_name = first_name
            user.last_name = last_name
            user.save()
            return Response(
                message=gettext("first_name_and_last_name_submitted")
            )

        else:
            return Response(
                message="Can't update the first name and last name",
                code=400
            )
