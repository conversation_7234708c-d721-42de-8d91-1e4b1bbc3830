import logging

from django.db.models import Value as V, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from django.db.models.functions import <PERSON><PERSON><PERSON>, Cast, Now
from django.utils.translation import gettext
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.pagination import CentersCursorPagination
from core.authentication import CustomAuthentication
from core.http import Response, JsonResponse
from saleor.models import OrderLine
from service_center.permissions import IsServiceCenter
from service_center.serializers.appointments import LoadAppointmentsSerializer
from service_center.utils.calculate_charge_price import get_order_line_base_on_category

logger = logging.getLogger("service_center")


class LoadAppointmentsAPI(APIView):
    authentication_classes = (CustomAuthentication,)
    permission_classes = (IsAuthenticated, IsServiceCenter)
    pagination_class = CentersCursorPagination()

    def get(self, request, order_line_id=None):
        if request.query_params.get('sort_by_expired') is None:
            sort_by_expired = None
        else:
            sort_by_expired = request.query_params.get('sort_by_expired') in ['1', 'true']

        if order_line_id:
            self.exception_data = {"message": gettext("Appointment not found")}
            order_line_obj = OrderLine.objects.usable_order_lines().select_related("order", "product").get(
                id=order_line_id,
                service_center_id=request.service_center.id)
            return Response(data=LoadAppointmentsSerializer(order_line_obj).data)
        self.exception_data = {"message": gettext("category did not send")}
        category, end, start = request.query_params.get("category"), request.query_params.get(
            "end"), request.query_params.get("start")
        q = request.query_params.get('q', None)
        _filter = {}

        if start and end:
            _filter["delivery_date__range"] = (start, end)
        elif start:
            _filter["delivery_date__gte"] = start
        elif end:
            _filter["delivery_date__lte"] = end

        if q:
            _filter["order__saleor_user_email__icontains"] = q

        self.exception_data = {"message": gettext("error occurred while showing appointments")}

        order_lines = OrderLine.objects.usable_order_lines().select_related("order", "product").filter(
            service_center_id=request.service_center.id,
            **_filter
        )

        if category:
            order_lines = get_order_line_base_on_category(order_lines, category)

        if sort_by_expired is None:
            order_lines = order_lines.annotate(
                non_null_delivery_date=Coalesce(
                    Cast(Now(), DateField()) - F('delivery_date'),
                    Cast(V('00:00:00'), DurationField())
                ),
            )
            self.pagination_class.ordering = 'non_null_delivery_date'
        else:
            order_lines = order_lines.annotate(
                non_null_expired_at=Coalesce('expired_at', Cast(V('2000-01-01 00:00:00'), DateTimeField())),
            )
            self.pagination_class.ordering = '-non_null_expired_at'

        paginated_queryset = self.pagination_class.paginate_queryset(order_lines, request)
        serializer = LoadAppointmentsSerializer(
            paginated_queryset,
            many=True,
            fields=['id', 'mobile', 'delivery_date', 'product_name', "quantity", "fullname", "expired_at"]).data

        response = self.pagination_class.get_paginated_response(serializer).data

        return JsonResponse(
            data=response
        )
