import logging

from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.db.models.functions import Coalesce
from django.utils.translation import gettext
from rest_framework.permissions import IsAuthenticated

from django.db.models import Q, F, Subquery, OuterRef, Count, Value

from core.utils import paginate
from core.validators import mobile_number_validator
from service_center.permissions.is_service_center import IsServiceCenterOwner
from service_center.models import ServiceCenterEmployee, ServiceCenter, Bill
from rest_framework.views import APIView

from core.authentication import CustomAuthentication
from accounts.models import User
from core.http import Response
from service_center.serializers import ServiceCenterEmployeeSerializer

logger = logging.getLogger("service_center")


class ServiceCenterEmployeeAPI(APIView):
    authentication_classes = (CustomAuthentication,)
    permission_classes = (IsAuthenticated, IsServiceCenterOwner)

    def get(self, request, service_center_employee_id=None):
        if service_center_employee_id:
            service_center_employee = ServiceCenterEmployee.objects.get(
                id=service_center_employee_id,
                service_center=request.service_center
            )
            return ServiceCenterEmployeeSerializer(service_center_employee).data
        q = request.query_params.get("q")
        page, limit = request.query_params.get("page", 1), request.query_params.get("limit", 20)
        service_center_employee_qs = ServiceCenterEmployee.objects.filter(
            service_center=request.service_center
        ).annotate(
            number_of_bills=Coalesce(
                Subquery(
                    Bill.objects.filter(
                        service_center_id=OuterRef("service_center_id"),
                        verifier_id=OuterRef("user_id")
                    )
                    .values("service_center_id")
                    .annotate(count=Count("id"))
                    .values("count")
                ),
                Value(0)
            )
        )
        if q:
            service_center_employee_qs = service_center_employee_qs.filter(
                Q(user__first_name__icontains=q) |
                Q(user__last_name__icontains=q) |
                Q(user__username__icontains=q)
            )

        return Response(**paginate(
            data=service_center_employee_qs.order_by("-id"),
            serializer=ServiceCenterEmployeeSerializer,
            page=page,
            limit=limit,
        ))

    def post(self, request):
        self.exception_data = {"message": gettext("phone number is required"), "status": 400}
        phone_number, first_name, last_name = request.data["phone_number"], request.data.get(
            "first_name"), request.data.get("last_name")

        self.exception_data = {"message": gettext("phone number is not valid"), "status": 400}
        mobile_number_validator(phone_number)
        self.exception_data = {"message": gettext("user did not created"), "status": 400}
        user, _ = User.objects.get_or_create(
            username=phone_number,
            defaults={
                "first_name": first_name,
                "last_name": last_name,
            }
        )
        self.exception_data = {"message": gettext("Error in saving Service center employee"), "status": 400}
        try:
            ServiceCenterEmployee.objects.create(
                user=user,
                service_center=request.service_center,
            )
        except IntegrityError:
            return Response(message=gettext("An employee with this phone number already exists."), status=400)
        except ValidationError as e:
            return Response(message=str(e), status=400)
        return Response(message=gettext("Employee created successfully."), status=201)

    def put(self, request, service_center_employee_id):
        self.exception_data = {"message": gettext("service center employee did not found"), "status": 400}
        service_center_employee = ServiceCenterEmployee.objects.get(
            id=service_center_employee_id,
        )
        self.exception_data = {"message": gettext("is active did not send correctly"), "status": 400}
        service_center_employee.is_active = request.data.get("is_active", service_center_employee.is_active)
        service_center_employee.save(update_fields=["is_active"])
        self.exception_data = {"message": gettext("first name did not send correctly"), "status": 400}
        service_center_employee.user.first_name = request.data.get("first_name",
                                                                   service_center_employee.user.first_name)
        self.exception_data = {"message": gettext("last name did not send correctly"), "status": 400}
        service_center_employee.user.last_name = request.data.get("last_name", service_center_employee.user.last_name)

        service_center_employee.user.save(update_fields=["first_name", "last_name"])
        return Response(message=gettext("Employee Updated successfully."), status=200)
