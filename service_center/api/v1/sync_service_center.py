from rest_framework.views import APIView
from core.http import JsonResponse
from service_center.serializers import SyncServiceCenterSerializer
from service_center.models import ServiceCenter
from rest_framework.permissions import IsAuthenticated


class SyncServiceCenterAPI(APIView):
    permission_classes = ()

    def get(self, request):
        scs = ServiceCenter.objects.filter(default_location_address__region__city__state__country__name__icontains="oman")
        return JsonResponse(SyncServiceCenterSerializer(scs, many=True).data)
