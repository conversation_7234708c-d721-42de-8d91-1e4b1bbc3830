import logging

from rest_framework.views import APIView

from core.http import Response
from core.utils import paginate
from service_center.models import ServiceType
from service_center.serializers import ServiceTypeSerializer

logger = logging.getLogger("service_center")


class ServiceTypesAPI(APIView):
    permission_classes = ()
    authentication_classes = ()

    def get(self, request):
        page, limit = request.query_params.get('page', 1), request.query_params.get('limit', 10)
        q = request.query_params.get('q')
        service_types = ServiceType.objects.all().select_related('category')
        if q:
            service_types = service_types.filter(name__icontains=q)

        return Response(
            **paginate(
                data=service_types,
                serializer=ServiceTypeSerializer,
                page=page,
                limit=limit,
            )
        )
