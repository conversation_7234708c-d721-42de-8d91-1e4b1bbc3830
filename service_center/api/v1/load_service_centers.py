import logging

from rest_framework.views import APIView

from constants import VisitStatus
from core.http import Response
from core.utils import paginate
from service_center.models import ServiceCenter
from service_center.serializers import ServiceCenterSerializer

logger = logging.getLogger("service_center")


class LoadServiceCentersAPI(APIView):
    permission_classes = ()
    authentication_classes = ()

    def get(self, request):
        page, limit = request.query_params.get('page', 1), request.query_params.get('limit', 10)
        state, service_type = request.query_params.get('state'), request.query_params.get('service_type')
        q = request.query_params.get('q')
        service_centers = ServiceCenter.objects.filter(is_active=True, visit_status=VisitStatus.FOCUS)
        if q:
            service_centers = service_centers.filter(name__icontains=q)
        if service_type:
            service_centers = service_centers.get_by_service_type(service_type)

        if state:
            service_centers = service_centers.get_by_state(state)

        return Response(
            **paginate(
                data=service_centers,
                serializer=ServiceCenterSerializer,
                serializer_kwargs={"fields": ["user", "address", "name", "is_car_rental"]},
                page=page,
                limit=limit,
            )
        )
