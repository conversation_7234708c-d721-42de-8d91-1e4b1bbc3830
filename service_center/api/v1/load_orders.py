import logging

from django.db.models import Count, Q, F
from django.utils import timezone
from django.utils.translation import gettext
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from core.authentication import CustomAuthentication
from core.http import Response
from saleor.utils import get_email
from saleor.utils.mobile_number_to_saleor_email import format_mobile_number
from service_center.models import ServiceCenterServiceType
from service_center.permissions import IsServiceCenter
from service_center.serializers import ServiceCategorySerializer

logger = logging.getLogger("service_center")


class LoadOrdersAPI(APIView):
    authentication_classes = (CustomAuthentication,)
    permission_classes = (IsAuthenticated, IsServiceCenter)

    def get(self, request):
        self.exception_data = {"message": gettext("mobile number did not send"), "code": 400, "status": 400}
        mobile_number = format_mobile_number(str(request.query_params["mobile_number"]))
        self.exception_data = {"message": gettext("error occurred while showing orders"), "code": 400, "status": 400}


        service_center_types = (ServiceCenterServiceType.objects.select_related(
            "service_center",
            "service_type__category"
        ).filter(service_center_id=request.service_center.id,
                 service_type__category__site__name="hig").prefetch_related(
            "service_center__order_lines"
        ))
        lst_cat, lst_excluded_ids = [], []
        for i in service_center_types:
            if i.service_type.category_id in lst_cat:
                lst_excluded_ids.append(i.pk)
                continue
            lst_cat.append(i.service_type.category_id)

        serializer = ServiceCategorySerializer(
            service_center_types.exclude(id__in=lst_excluded_ids),
            mobile_number=mobile_number,
            lang=request.lang,
            many=True,
        ).data

        sorted_list = sorted(serializer, key=lambda x: x['service_count'], reverse=True)

        return Response(
            data=sorted_list
        )
