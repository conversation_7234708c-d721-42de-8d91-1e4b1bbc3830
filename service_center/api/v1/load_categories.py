import logging

from django.utils.translation import gettext
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from core.http import Response
from core.authentication import CustomAuthentication
from service_center.permissions import IsServiceCenter
from quick_book.models import Category
from translation.utils.translate import get_object_translation

logger = logging.getLogger("service_center")


class LoadCategoriesAPI(APIView):
    authentication_classes = (CustomAuthentication,)
    permission_classes = (IsAuthenticated, IsServiceCenter)

    def get(self, request):
        self.exception_data = {"message": gettext("Error occurred while showing categories")}

        categories = Category.objects.filter(
            service_types__service_center_service_types__service_center_id=request.service_center.id,
            site__name="hig"
        ).distinct("id").prefetch_related("translations")
        response_data = []
        for category in categories:
            category_resp = {
                "id": category.id,
                "image": str(category.image) if category.image else None,
                "name": category.name,
            }
            category_resp.update(get_object_translation(category, request.lang))
            response_data.append(
                category_resp
            )
        return Response(
            data=response_data
        )
