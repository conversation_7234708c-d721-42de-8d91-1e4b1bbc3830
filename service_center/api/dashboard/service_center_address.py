import logging

from core.http import JsonResponse
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import Coo<PERSON>ieAuthentication
from locations.models import Address
from service_center.models import ServiceCenter

logger = logging.getLogger("dashboard")


class ServiceAddressAPI(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def post(self, request):
        self.exception_data = {"message": "invalid data.", "status": 400}
        address_id, service_center_id, field = request.data["address"], request.data[
            "service_center"], request.data["field"]
        self.exception_data = {"message": "invalid service center ", "status": 400}
        service_center = ServiceCenter.objects.get(pk=service_center_id)

        self.exception_data = {"message": "invalid address", "status": 400}
        address = Address.objects.get(pk=address_id)

        if address.pk not in list(service_center.addresses.values_list("pk", flat=True)):
            return JsonResponse(message="This address has not set for this service center", status=400)

        self.exception_data = {"message": "invalid field for address", "status": 400}
        setattr(service_center, field, address)
        service_center.save()
        return JsonResponse(status=200)
