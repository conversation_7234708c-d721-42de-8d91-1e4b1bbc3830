import logging

from django.contrib.contenttypes.models import ContentType
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff

from core.authentication.cookie_auth import Coo<PERSON><PERSON><PERSON>uthentication
from core.http import JsonResponse
from files.models import Image
from files.serializers import ImageSerializer

logger = logging.getLogger("dashboard")


class ServiceCenterImage(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def post(self, request):

        service_center_id = request.data.get('service_center_id')
        try:
            Image.objects.create(
                image=request.data["image"],
                content_type=ContentType.objects.get(model="servicecenter"),
                object_id=service_center_id,
                name=request.data["name"]
            )
            images = Image.objects.filter(content_type__model="servicecenter",
                                          object_id=service_center_id).all().order_by("id")
            values_list = images.values_list("name", flat=True).distinct()
            image_ids = []
            names = []

            for value in values_list:
                if value in names:
                    continue
                names.append(value)
                image_id = images.filter(name=value).first().pk
                if image_id not in image_ids:
                    image_ids.append(images.filter(name=value).last().pk)

            return JsonResponse(data=ImageSerializer(images.filter(id__in=image_ids), many=True).data)
        except Exception as e:
            logger.error("IMAGE-API| error {}".format(str(e)))
            return JsonResponse(message=str(e), status=405)

    def delete(self, request):
        print(request.data)
        service_center_id, image_id = request.data.get("service_center_id"), request.data.get(
            "image_id")
        Image.objects.get(content_type__model="servicecenter", object_id=service_center_id, pk=image_id).delete()
        return JsonResponse(message="image deleted successfully.")
