from django.urls import path
from .service_center import ServiceCenterAPI
from .image import ServiceCenterImage
from .service_center_address import ServiceAddressAPI
from .service_types import ServiceTypeAP<PERSON>
from .shift import ServiceCenterShiftsDashboardAPI
from .agents import AgentDashboardAPI
from .service_center_transaction_stocks import ServiceCenterDashboardTransactionStockAPI
from .service_center_stock import ServiceCenterDashboardStockAPI

app_name = "service_center"

urlpatterns = [
    path("service_center/", ServiceCenterAPI.as_view(), name="service_centers"),
    path("service_center_image/", ServiceCenterImage.as_view(), name="service_centers_image"),
    path("service_types/", ServiceTypeAPI.as_view(), name="service_types"),
    path("shifts/", ServiceCenterShiftsDashboardAPI.as_view(), name="shifts"),
    path("service_center/<int:service_center_id>/", ServiceCenterAPI.as_view(), name="service_centers"),
    path("service_center_address/", ServiceAddressAPI.as_view(), name="service_centers_address"),
    path("agent/", AgentDashboardAPI.as_view(), name="agent"),
    path("service_center_transaction_stock/", ServiceCenterDashboardTransactionStockAPI.as_view(), name="service_center_transaction_stock"),
    path("service_center_stock/", ServiceCenterDashboardStockAPI.as_view(), name="service_center_stock"),
]
