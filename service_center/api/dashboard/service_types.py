import logging

from core.http import JsonResponse
from rest_framework.views import APIView

from service_center.models import ServiceType
from service_center.serializers import ServiceTypeSerializer
from service_center.serializers.dashboard.service_types import ServiceTypeDashboardSerializer

logger = logging.getLogger("dashboard")


class ServiceTypeAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        return JsonResponse(
            data=ServiceTypeDashboardSerializer(
                ServiceType.objects.filter(is_active=True).order_by("-priority"), many=True
            ).data
        )
