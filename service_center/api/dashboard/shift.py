import logging

from core.http import JsonResponse
from rest_framework.views import APIView

from service_center.models import ServiceCenterShift
from service_center.serializers import ServiceCenterShiftSerializer

logger = logging.getLogger("dashboard")


class ServiceCenterShiftsDashboardAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        return JsonResponse(
            data=ServiceCenterShiftSerializer(
                ServiceCenterShift.objects.filter(is_active=True), many=True
            ).data
        )
