import logging

from django.db.models import Q, F

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import CooKieAuthentication
from core.http import JsonResponse
from core.utils import paginate
from products.models import ProductServiceCenterStockTransaction
from service_center.serializers.dashboard.service_center_transaction_stock import \
    ServiceCenterTransactionStockDashboardSerializer

logger = logging.getLogger("dashboard")


class ServiceCenterDashboardTransactionStockAPI(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request):
        page, limit = request.query_params.get('page', 1), request.query_params.get('limit', 10)
        q = request.query_params.get('q')
        stock_type = request.query_params.get('stock_type')
        service_center = request.query_params.get('service_center')
        product = request.query_params.get('product')
        qs = ProductServiceCenterStockTransaction.objects.all().annotate(
            service_center_name=F("service_center__name"),
            product_name=F("product__name"),
        )
        if q:
            qs = qs.filter(
                Q(product__name__icontains=q) |
                Q(product__id__icontains=q) |
                Q(product__sku__icontains=q) |
                Q(service_center__name__icontains=q) |
                Q(service_center__id__icontains=q) |
                Q(service_center__user__username__icontains=q)
            )
        if stock_type:
            qs = qs.filter(stock_type=stock_type)

        if service_center:
            qs = qs.filter(service_center_id=service_center)

        if product:
            qs = qs.filter(product_id=product)
        return JsonResponse(
            **paginate(
                data=qs,
                serializer=ServiceCenterTransactionStockDashboardSerializer,
                page=page,
                limit=limit,
            )
        )
