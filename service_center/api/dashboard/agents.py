import logging

from django.db.models import F
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from accounts.permissions.is_staff import IsStaff

from agent.models import Agent
from core.authentication.cookie_auth import CooKieAuthentication
from core.http import JsonResponse
from service_center.serializers.dashboard.service_center_dashboard import ServiceCenterDashboardSerializer
from core.utils import paginate

logger = logging.getLogger("dashboard")


class AgentDashboardAPI(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request):
        self.exception_data = {"message": "Error in showing centers"}
        agents = Agent.objects.all().annotate(
            name=F("user__username")
        ).order_by("-id").values("name", "id")

        return JsonResponse(
            data=list(agents)
        )
