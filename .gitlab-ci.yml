before_script:
  - echo ${CI_COMMIT_REF_NAME}
  - echo ${GIT_URL}
  - echo ${APP_DEBUG}

stages:
  - build
  - prepare
  - deploy
  - cleanup

variables:
  GIT_URL: "***************:saleor/backend/matrix.git"

build_dev:
  stage: build
  tags:
    - dev_runner
  script:
    - cp .env.develop .env
    - docker-compose -f ./docker-compose.yml build

  only:
    - develop

prepare_dev:
  stage: prepare
  tags:
    - dev_runner
  script:
    - cp .env.develop .env
    - docker-compose -f ./docker-compose.yml run backend_matrix python manage.py collectstatic --noinput
    - docker-compose -f ./docker-compose.yml run backend_matrix python manage.py migrate

  only:
    - develop

deploy_dev:
  stage: deploy
  tags:
    - dev_runner
  script:
    - cp .env.develop .env
    - docker-compose -f ./docker-compose.yml up -d
  only:
    - develop

cleanup_dev:
  stage: cleanup
  tags:
    - dev_runner
  script:
    - docker ps -a -f name=matrix-backend_matrix-run- -f status=exited -q | xargs -r docker rm
  only:
    - develop

build_prod:
  stage: build
  tags:
    - prod_runner
  script:
    - cp .env.master .env
    - docker-compose -f ./docker-compose.yml build

  only:
    - master

prepare_prod:
  stage: prepare
  tags:
    - prod_runner
  script:
    - cp .env.master .env
    - docker-compose -f ./docker-compose.yml run backend_matrix python manage.py collectstatic --noinput
    - docker-compose -f ./docker-compose.yml run backend_matrix python manage.py migrate

  only:
    - master

deploy_prod:
  stage: deploy
  tags:
    - prod_runner
  script:
    - cp .env.master .env
    - docker-compose -f ./docker-compose.yml up -d

  only:
    - master

cleanup_prod:
  stage: cleanup
  tags:
    - prod_runner
  script:
    - docker ps -a -f name=matrix-backend_matrix-run- -f status=exited -q | xargs -r docker rm
  only:
    - master