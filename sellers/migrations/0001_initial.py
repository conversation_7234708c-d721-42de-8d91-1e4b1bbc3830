# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('locations', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Seller',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_name', models.CharField(max_length=100)),
                ('seller_role', models.CharField(choices=[('b2b', 'B2B'), ('b2c', 'B2C')], default='b2c', max_length=10)),
                ('contact_number', models.CharField(max_length=15)),
                ('business_registration_number', models.CharField(max_length=50)),
                ('address', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='seller', to='locations.address')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='seller', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'matrix_sellers_seller',
            },
        ),
    ]
