from core import models
from constants import SellerRole


class Seller(models.AbstractBaseModel):
    user = models.OneToOneField("accounts.User", on_delete=models.PROTECT, related_name="seller")
    business_name = models.CharField(max_length=100)
    address = models.ForeignKey("locations.Address", on_delete=models.PROTECT, related_name="seller")
    seller_role = models.CharField(max_length=10, choices=SellerRole.choices, default=SellerRole.B2C)
    contact_number = models.CharField(max_length=15)
    business_registration_number = models.Char<PERSON>ield(max_length=50)

    class Meta:
        db_table = "matrix_sellers_seller"

    def __str__(self):
        return "[{}]/{}".format(self.pk, self.business_name)
