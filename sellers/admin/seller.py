from django.contrib import admin


class SellerAdmin(admin.ModelAdmin):
    list_display = ('business_name', 'seller_role', 'contact_number', 'business_registration_number', "is_active")
    list_filter = ('seller_role', "created_at", "updated_at")
    search_fields = ('business_name', 'business_registration_number')
    raw_id_fields = ("user", "address")
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('', {
            'fields': (('created_at', 'updated_at'), "is_active")
        }),
        ('Seller Information', {
            'fields': ('user', 'business_name', 'seller_role', 'business_registration_number')
        }),
        ('Contact Information', {
            'fields': ('contact_number', 'address')
        }),
    )