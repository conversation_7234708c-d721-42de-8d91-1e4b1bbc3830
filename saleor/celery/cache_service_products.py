import logging

from django.core.cache import cache

from saleor.utils import Saleor
from settings.celery_app import celery_app

logger = logging.getLogger('saleor')


@celery_app.task(name='cache_service_products', autoregister=True)
def cache_service_products():
    saleor = Saleor(site='hig')
    _, product = saleor.get_product_by_slug('service_fee')
    for variant in product['variants']:
        if len(variant['channelListings']) < 1:
            continue

        _id = variant['id']
        sku = float(variant['sku'])
        amount = variant['channelListings'][0]['price']['amount']

        if sku != amount:
            continue

        cache.set(f'service_fee_{amount}', _id, 1 * 60 * 60)
        logger.info(f'Service fee {amount} cached with id {_id}')
        print(f'Service fee {amount} cached with id {_id}')
