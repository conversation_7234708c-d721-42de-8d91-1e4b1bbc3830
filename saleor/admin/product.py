from django.contrib import admin


class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'parent', 'is_active')
    raw_id_fields = ("parent",)

    prepopulated_fields = {'slug': ('name', 'is_active')}


class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'product_type', 'category', 'is_active', 'created_at', 'updated_at',)
    raw_id_fields = ("category",)
    list_filter = ('product_type', 'category',)
    search_fields = ('name', 'description',)
    prepopulated_fields = {'slug': ('name', 'is_active')}



