from django.contrib.postgres.search import SearchV<PERSON><PERSON>ield
from django.db import models
from django.db.models import TextField
from django_measurement.models import MeasurementField
from measurement.measures import Weight
from mptt.models import MPTTModel

from saleor.core.db.fields import SanitizedJ<PERSON><PERSON>ield
from saleor.core.utils.editorjs import clean_editor_js
from saleor.core.models import (
    ModelWithExternalReference,
    ModelWithMetadata,
)
from saleor.core.units import WeightUnits
from saleor.core.weight import zero_weight
from saleor.constants import ProductTypeKind


class Category(ModelWithMetadata, MPTTModel):
    name = models.CharField(max_length=250)
    slug = models.SlugField(max_length=255, allow_unicode=True)
    description = SanitizedJ<PERSON>NField(blank=True, null=True, sanitizer=clean_editor_js)
    description_plaintext = TextField(blank=True)
    parent = models.ForeignKey(
        "self", null=True, blank=True, related_name="children", on_delete=models.CASCADE
    )
    background_image = models.ImageField(
        upload_to="category-backgrounds", blank=True, null=True
    )
    background_image_alt = models.CharField(max_length=128, blank=True)
    saleor_id = models.CharField(max_length=100, unique=True)
    is_active = models.BooleanField(default=True)
    webhook_description = SanitizedJSONField(blank=True, null=True, sanitizer=clean_editor_js)
    is_deleted = models.BooleanField(default=False)
    quickbooks_id = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self) -> str:
        return self.name

    class Meta:
        db_table = "product_category"


class ProductType(ModelWithMetadata):
    name = models.CharField(max_length=250)
    slug = models.SlugField(max_length=255, allow_unicode=True)
    kind = models.CharField(max_length=32, choices=ProductTypeKind.CHOICES)
    has_variants = models.BooleanField(default=True)
    is_shipping_required = models.BooleanField(default=True)
    is_digital = models.BooleanField(default=False)
    weight = MeasurementField(
        measurement=Weight,
        unit_choices=WeightUnits.CHOICES,
        default=zero_weight,
    )
    saleor_id = models.CharField(max_length=100, unique=True)
    is_active = models.BooleanField(default=True)
    webhook_description = SanitizedJSONField(blank=True, null=True, sanitizer=clean_editor_js)
    is_deleted = models.BooleanField(default=False)


    class Meta:
        db_table = "product_product_type"

    def __str__(self) -> str:
        return self.name


class Product(ModelWithMetadata, ModelWithExternalReference):
    product_type = models.ForeignKey(
        ProductType, related_name="products", on_delete=models.CASCADE
    )
    name = models.CharField(max_length=250)
    slug = models.SlugField(max_length=255, allow_unicode=True)
    description = SanitizedJSONField(blank=True, null=True, sanitizer=clean_editor_js)
    description_plaintext = TextField(blank=True)
    search_document = models.TextField(blank=True, default="")
    search_vector = SearchVectorField(blank=True, null=True)
    search_index_dirty = models.BooleanField(default=False, db_index=True)

    category = models.ForeignKey(
        Category,
        related_name="products",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)
    weight = MeasurementField(
        measurement=Weight,
        unit_choices=WeightUnits.CHOICES,
        blank=True,
        null=True,
    )
    rating = models.FloatField(null=True, blank=True)
    saleor_id = models.CharField(max_length=100, unique=True)
    quickbooks_id = models.CharField(max_length=100, null=True, blank=True)
    is_active = models.BooleanField(default=True)
    webhook_description = SanitizedJSONField(blank=True, null=True, sanitizer=clean_editor_js)
    is_deleted = models.BooleanField(default=False)
    cost = models.JSONField(null=True, blank=True)

    def __str__(self) -> str:
        return self.name

    class Meta:
        db_table = "product_product"

