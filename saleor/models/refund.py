from django.db import transaction

from core import models
from core.models import AbstractBaseModel


class Refund(AbstractBaseModel):
    order_line = models.ForeignKey('saleor.OrderLine', on_delete=models.CASCADE, related_name='refunds')
    quantity = models.PositiveIntegerField()
    amount = models.DecimalField(decimal_places=5, max_digits=20)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f'{self.order_line} - {self.quantity}'

    @transaction.atomic
    def save(self, *args, **kwargs):
        from constants import TransactionState
        from products.models import ProductServiceCenterStockTransaction
        from saleor.models import OrderLine

        OrderLine.objects.filter(id=self.order_line_id).update(
            refunded_quantity=models.F('refunded_quantity') + self.quantity,
        )

        if self.order_line.service_center_id is not None:  # GarajMall order lines' service_center_id are None
            product = self.order_line.product
            if product.is_bundle:
                for item in product.items.all():
                    ProductServiceCenterStockTransaction.objects.create(
                        service_center_id=self.order_line.service_center_id,
                        product_id=item.item_id,
                        quantity=item.quantity * self.quantity,
                        stock_type=self.order_line.stock_type,
                        order_line_id=self.order_line_id,
                        state=TransactionState.INCREASE,
                        service_center_balance_percentage=100,
                    )
            else:
                ProductServiceCenterStockTransaction.objects.create(
                    service_center_id=self.order_line.service_center_id,
                    product_id=self.order_line.product_id,
                    quantity=self.quantity,
                    stock_type=self.order_line.stock_type,
                    order_line_id=self.order_line_id,
                    state=TransactionState.INCREASE,
                    service_center_balance_percentage=100,
                )

        super().save(*args, **kwargs)
