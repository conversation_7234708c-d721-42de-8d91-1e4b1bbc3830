
class JobStatus:
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    DELETED = "deleted"

    CHOICES = [
        (PENDING, "Pending"),
        (SUCCESS, "Success"),
        (FAILED, "Failed"),
        (DELETED, "Deleted"),
    ]


class TimePeriodType:
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    YEAR = "year"

    CHOICES = [(DAY, "Day"), (WEEK, "Week"), (MONTH, "Month"), (YEAR, "Year")]


class EventDeliveryStatus:
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"

    CHOICES = [
        (PENDING, "Pending"),
        (SUCCESS, "Success"),
        (FAILED, "Failed"),
    ]

class ProductMediaTypes:
    IMAGE = "IMAGE"
    VIDEO = "VIDEO"

    CHOICES = [
        (IMAGE, "An uploaded image or an URL to an image"),
        (VIDEO, "A URL to an external video"),
    ]


class ProductTypeKind:
    NORMAL = "normal"
    GIFT_CARD = "gift_card"

    CHOICES = [
        (NORMAL, "A standard product type."),
        (GIFT_CARD, "A gift card product type."),
    ]


class AllocationStrategy:
    """Determine the allocation strategy for the channel.

    PRIORITIZE_SORTING_ORDER - allocate stocks according to the warehouses' order
    within the channel

    PRIORITIZE_HIGH_STOCK - allocate stock in a warehouse with the most stock
    """

    PRIORITIZE_SORTING_ORDER = "prioritize-sorting-order"
    PRIORITIZE_HIGH_STOCK = "prioritize-high-stock"

    CHOICES = [
        (PRIORITIZE_SORTING_ORDER, "Prioritize sorting order"),
        (PRIORITIZE_HIGH_STOCK, "Prioritize high stock"),
    ]


class MarkAsPaidStrategy:
    TRANSACTION_FLOW = "transaction_flow"
    PAYMENT_FLOW = "payment_flow"

    CHOICES = [
        (TRANSACTION_FLOW, "Use transaction"),
        (PAYMENT_FLOW, "Use payment"),
    ]


class TransactionFlowStrategy:
    AUTHORIZATION = "authorization"
    CHARGE = "charge"

    CHOICES = [(AUTHORIZATION, "Authorize"), (CHARGE, "Charge")]


