import logging

from rest_framework.views import APIView
from core.http import JsonResponse
from saleor.webhooks import SaleorWebhookHandler

logger = logging.getLogger("saleor")


class SaleorWebhookApi(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request):
        logger.info("[SaleorWebhookApi] data={}, headers={}".format(request.data, request.headers))
        # self.exception_data = {"status": 400, "message": "Invalid validation key"}
        # is_verified = RsaVerify.verify_signature(request)
        # if not is_verified:
        #     return JsonResponse(status=400, message="Invalid validation signature")
        self.exception_data = {"status": 400, "message": "Invalid webhook data"}
        site = request.GET.get("site")
        webhook_handler = SaleorWebhookHandler(request.data, request.headers, site)
        webhook_handler.handle()
        return JsonResponse(status=200, message="done!")
