from unidecode import unidecode
from core.validators import mobile_number_validator


def format_mobile_number(mobile_number: str) -> str:
    """

    Parameters
    ----------
    mobile_number

    Returns
    -------
    check mobile number to be validated and add + at the first of the mobile number
    for query params
    """
    mobile_number = "+" + unidecode(mobile_number.strip())
    mobile_number_validator(mobile_number)
    return mobile_number


def get_email(mobile_number):
    return mobile_number.replace("+", "00") + "@hig.com"
