import requests
from jose import jwt, jwk


class RsaVerify:
    JWKS_URL = "http://127.0.0.1:8000/.well-known/jwks.json"

    @staticmethod
    def verify_jws(jws, jwks_uri):
        jwks = requests.get(jwks_uri).json()
        _jwks = jwks["keys"][0]
        key = jwk.construct(_jwks, algorithm="RS256")
        return jwt.decode(jws, key, algorithms=["RS256"])


    @staticmethod
    def verify_signature(request):
        jws = request.headers["Saleor-Signature"]
        try:
            RsaVerify.verify_jws(jws, RsaVerify.JWKS_URL)
        except Exception as e:
            print(e)