# Generated by Django 3.2.10 on 2024-07-21 15:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('sites', '0002_alter_domain_unique'),
        ('service_center', '0012_auto_20240721_1317'),
        ('quick_book', '0005_auto_20240422_0837'),
        ('saleor', '0010_alter_order_saleor_id'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='is_gift',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='order',
            name='lines_net_amount',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='order',
            name='net_amount',
            field=models.FloatField(default=0),
        ),
        migrations.AddField(
            model_name='order',
            name='site',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='saleor_orders', to='sites.site'),
        ),
        migrations.AlterUniqueTogether(
            name='order',
            unique_together={('saleor_id', 'site')},
        ),
        migrations.CreateModel(
            name='OrderLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.PositiveIntegerField(default=0)),
                ('price', models.FloatField(default=0)),
                ('tax_included', models.BooleanField(default=True)),
                ('is_canceled', models.BooleanField(default=False)),
                ('delivery_date', models.DateField(blank=True, null=True)),
                ('service_type', models.CharField(choices=[('service', 'service in center'), ('delivery', 'pickup in center')], default='service', max_length=50)),
                ('delivery_shift', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='order_lines', to='service_center.servicecentershift')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='saleor.order')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='order_lines', to='quick_book.product')),
                ('service_center', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='order_lines', to='service_center.servicecenter')),
            ],
            options={
                'unique_together': {('order', 'product')},
            },
        ),
    ]
