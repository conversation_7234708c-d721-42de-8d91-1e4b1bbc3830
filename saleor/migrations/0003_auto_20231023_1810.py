# Generated by Django 3.2.10 on 2023-10-23 18:10

from django.db import migrations, models
import saleor.core.db.fields
import saleor.core.utils.editorjs


class Migration(migrations.Migration):

    dependencies = [
        ('saleor', '0002_auto_20231016_0652'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='collection',
            name='products',
        ),
        migrations.RemoveField(
            model_name='collectionchannellisting',
            name='channel',
        ),
        migrations.RemoveField(
            model_name='collectionchannellisting',
            name='collection',
        ),
        migrations.RemoveField(
            model_name='collectionproduct',
            name='collection',
        ),
        migrations.RemoveField(
            model_name='collectionproduct',
            name='product',
        ),
        migrations.RemoveField(
            model_name='digitalcontent',
            name='product_variant',
        ),
        migrations.RemoveField(
            model_name='productchannellisting',
            name='channel',
        ),
        migrations.RemoveField(
            model_name='productchannellisting',
            name='product',
        ),
        migrations.RemoveField(
            model_name='productmedia',
            name='product',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='media',
        ),
        migrations.RemoveField(
            model_name='productvariant',
            name='product',
        ),
        migrations.RemoveField(
            model_name='productvariantchannellisting',
            name='channel',
        ),
        migrations.RemoveField(
            model_name='productvariantchannellisting',
            name='variant',
        ),
        migrations.RemoveField(
            model_name='variantmedia',
            name='media',
        ),
        migrations.RemoveField(
            model_name='variantmedia',
            name='variant',
        ),
        migrations.RemoveField(
            model_name='product',
            name='default_variant',
        ),
        migrations.AddField(
            model_name='category',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='category',
            name='is_deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='category',
            name='saleor_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='category',
            name='webhook_description',
            field=saleor.core.db.fields.SanitizedJSONField(blank=True, null=True, sanitizer=saleor.core.utils.editorjs.clean_editor_js),
        ),
        migrations.AddField(
            model_name='product',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='product',
            name='is_deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='product',
            name='saleor_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='product',
            name='webhook_description',
            field=saleor.core.db.fields.SanitizedJSONField(blank=True, null=True, sanitizer=saleor.core.utils.editorjs.clean_editor_js),
        ),
        migrations.AddField(
            model_name='producttype',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='producttype',
            name='is_deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='producttype',
            name='saleor_id',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='producttype',
            name='webhook_description',
            field=saleor.core.db.fields.SanitizedJSONField(blank=True, null=True, sanitizer=saleor.core.utils.editorjs.clean_editor_js),
        ),
        migrations.AlterField(
            model_name='category',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='category',
            name='slug',
            field=models.SlugField(allow_unicode=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='product',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='product',
            name='slug',
            field=models.SlugField(allow_unicode=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='producttype',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='producttype',
            name='slug',
            field=models.SlugField(allow_unicode=True, max_length=255),
        ),
        migrations.DeleteModel(
            name='Channel',
        ),
        migrations.DeleteModel(
            name='Collection',
        ),
        migrations.DeleteModel(
            name='CollectionChannelListing',
        ),
        migrations.DeleteModel(
            name='CollectionProduct',
        ),
        migrations.DeleteModel(
            name='DigitalContent',
        ),
        migrations.DeleteModel(
            name='ProductChannelListing',
        ),
        migrations.DeleteModel(
            name='ProductMedia',
        ),
        migrations.DeleteModel(
            name='ProductVariant',
        ),
        migrations.DeleteModel(
            name='ProductVariantChannelListing',
        ),
        migrations.DeleteModel(
            name='VariantMedia',
        ),
    ]
