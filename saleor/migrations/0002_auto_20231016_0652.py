# Generated by Django 3.2.10 on 2023-10-16 06:52

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('saleor', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelTable(
            name='category',
            table='product_category',
        ),
        migrations.AlterModelTable(
            name='channel',
            table='channel_channel',
        ),
        migrations.AlterModelTable(
            name='collection',
            table='product_collection',
        ),
        migrations.AlterModelTable(
            name='collectionchannellisting',
            table='product_collection_channel_listing',
        ),
        migrations.AlterModelTable(
            name='collectionproduct',
            table='product_collection_product',
        ),
        migrations.AlterModelTable(
            name='digitalcontent',
            table='product_digital_content',
        ),
        migrations.AlterModelTable(
            name='product',
            table='product_product',
        ),
        migrations.AlterModelTable(
            name='productchannellisting',
            table='product_product_channel_listing',
        ),
        migrations.AlterModelTable(
            name='productmedia',
            table='product_product_media',
        ),
        migrations.AlterModelTable(
            name='producttype',
            table='product_product_type',
        ),
        migrations.AlterModelTable(
            name='productvariant',
            table='product_product_variant',
        ),
        migrations.AlterModelTable(
            name='productvariantchannellisting',
            table='product_product_variant_channel_listing',
        ),
        migrations.AlterModelTable(
            name='variantmedia',
            table='product_variant_media',
        ),
    ]
