# Generated by Django 3.2.10 on 2023-09-17 09:18

import datetime
import django.contrib.postgres.search
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django_countries.fields
import django_measurement.models
import measurement.measures.mass
import saleor.core.db.fields
import saleor.core.utils.editorjs
import saleor.core.utils.json_serializer
import saleor.core.weight


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('private_metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('name', models.CharField(max_length=250)),
                ('slug', models.SlugField(allow_unicode=True, max_length=255, unique=True)),
                ('description', saleor.core.db.fields.SanitizedJSONField(blank=True, null=True, sanitizer=saleor.core.utils.editorjs.clean_editor_js)),
                ('description_plaintext', models.TextField(blank=True)),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='category-backgrounds')),
                ('background_image_alt', models.CharField(blank=True, max_length=128)),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('level', models.PositiveIntegerField(editable=False)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='saleor.category')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Channel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('private_metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('name', models.CharField(max_length=250)),
                ('is_active', models.BooleanField(default=False)),
                ('slug', models.SlugField(max_length=255, unique=True)),
                ('currency_code', models.CharField(max_length=20)),
                ('default_country', django_countries.fields.CountryField(max_length=2)),
                ('allocation_strategy', models.CharField(choices=[('prioritize-sorting-order', 'Prioritize sorting order'), ('prioritize-high-stock', 'Prioritize high stock')], default='prioritize-sorting-order', max_length=255)),
                ('order_mark_as_paid_strategy', models.CharField(choices=[('transaction_flow', 'Use transaction'), ('payment_flow', 'Use payment')], default='payment_flow', max_length=255)),
                ('default_transaction_flow_strategy', models.CharField(choices=[('authorization', 'Authorize'), ('charge', 'Charge')], default='charge', max_length=255)),
                ('automatically_confirm_all_new_orders', models.BooleanField(default=True, null=True)),
                ('allow_unpaid_orders', models.BooleanField(default=False)),
                ('automatically_fulfill_non_shippable_gift_card', models.BooleanField(default=True, null=True)),
                ('expire_orders_after', models.IntegerField(blank=True, default=None, null=True)),
                ('delete_expired_orders_after', models.DurationField(default=datetime.timedelta(days=60))),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Collection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('private_metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('name', models.CharField(max_length=250)),
                ('slug', models.SlugField(allow_unicode=True, max_length=255, unique=True)),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='collection-backgrounds')),
                ('background_image_alt', models.CharField(blank=True, max_length=128)),
                ('description', saleor.core.db.fields.SanitizedJSONField(blank=True, null=True, sanitizer=saleor.core.utils.editorjs.clean_editor_js)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('private_metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('external_reference', models.CharField(blank=True, db_index=True, max_length=250, null=True, unique=True)),
                ('name', models.CharField(max_length=250)),
                ('slug', models.SlugField(allow_unicode=True, max_length=255, unique=True)),
                ('description', saleor.core.db.fields.SanitizedJSONField(blank=True, null=True, sanitizer=saleor.core.utils.editorjs.clean_editor_js)),
                ('description_plaintext', models.TextField(blank=True)),
                ('search_document', models.TextField(blank=True, default='')),
                ('search_vector', django.contrib.postgres.search.SearchVectorField(blank=True, null=True)),
                ('search_index_dirty', models.BooleanField(db_index=True, default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True, db_index=True)),
                ('weight', django_measurement.models.MeasurementField(blank=True, measurement=measurement.measures.mass.Mass, null=True)),
                ('rating', models.FloatField(blank=True, null=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='saleor.category')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductMedia',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(db_index=True, editable=False, null=True)),
                ('private_metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('image', models.ImageField(blank=True, null=True, upload_to='products')),
                ('alt', models.CharField(blank=True, max_length=128)),
                ('type', models.CharField(choices=[('IMAGE', 'An uploaded image or an URL to an image'), ('VIDEO', 'A URL to an external video')], default='IMAGE', max_length=32)),
                ('external_url', models.CharField(blank=True, max_length=256, null=True)),
                ('oembed_data', models.JSONField(blank=True, default=dict)),
                ('to_remove', models.BooleanField(default=False)),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='media', to='saleor.product')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('private_metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('name', models.CharField(max_length=250)),
                ('slug', models.SlugField(allow_unicode=True, max_length=255, unique=True)),
                ('kind', models.CharField(choices=[('normal', 'A standard product type.'), ('gift_card', 'A gift card product type.')], max_length=32)),
                ('has_variants', models.BooleanField(default=True)),
                ('is_shipping_required', models.BooleanField(default=True)),
                ('is_digital', models.BooleanField(default=False)),
                ('weight', django_measurement.models.MeasurementField(default=saleor.core.weight.zero_weight, measurement=measurement.measures.mass.Mass)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductVariant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(db_index=True, editable=False, null=True)),
                ('private_metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('external_reference', models.CharField(blank=True, db_index=True, max_length=250, null=True, unique=True)),
                ('sku', models.CharField(blank=True, max_length=255, null=True, unique=True)),
                ('name', models.CharField(blank=True, max_length=255)),
                ('track_inventory', models.BooleanField(default=True)),
                ('is_preorder', models.BooleanField(default=False)),
                ('preorder_end_date', models.DateTimeField(blank=True, null=True)),
                ('preorder_global_threshold', models.IntegerField(blank=True, null=True)),
                ('quantity_limit_per_customer', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1)])),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True, db_index=True)),
                ('weight', django_measurement.models.MeasurementField(blank=True, measurement=measurement.measures.mass.Mass, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='VariantMedia',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('media', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variant_media', to='saleor.productmedia')),
                ('variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variant_media', to='saleor.productvariant')),
            ],
        ),
        migrations.CreateModel(
            name='ProductVariantChannelListing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('currency', models.CharField(max_length=20)),
                ('price_amount', models.DecimalField(blank=True, decimal_places=100, max_digits=100, null=True)),
                ('cost_price_amount', models.DecimalField(blank=True, decimal_places=100, max_digits=100, null=True)),
                ('discounted_price_amount', models.DecimalField(blank=True, decimal_places=100, max_digits=100, null=True)),
                ('preorder_quantity_threshold', models.IntegerField(blank=True, null=True)),
                ('channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variant_listings', to='saleor.channel')),
                ('variant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='channel_listings', to='saleor.productvariant')),
            ],
        ),
        migrations.AddField(
            model_name='productvariant',
            name='media',
            field=models.ManyToManyField(through='saleor.VariantMedia', to='saleor.ProductMedia'),
        ),
        migrations.AddField(
            model_name='productvariant',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='saleor.product'),
        ),
        migrations.CreateModel(
            name='ProductChannelListing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('is_published', models.BooleanField(default=False)),
                ('visible_in_listings', models.BooleanField(default=False)),
                ('available_for_purchase_at', models.DateTimeField(blank=True, null=True)),
                ('currency', models.CharField(max_length=20)),
                ('discounted_price_amount', models.DecimalField(blank=True, decimal_places=100, max_digits=100, null=True)),
                ('channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_listings', to='saleor.channel')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='channel_listings', to='saleor.product')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='product',
            name='default_variant',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='+', to='saleor.productvariant'),
        ),
        migrations.AddField(
            model_name='product',
            name='product_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='saleor.producttype'),
        ),
        migrations.CreateModel(
            name='DigitalContent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('private_metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('metadata', models.JSONField(blank=True, default=dict, encoder=saleor.core.utils.json_serializer.CustomJsonEncoder, null=True)),
                ('use_default_settings', models.BooleanField(default=True)),
                ('automatic_fulfillment', models.BooleanField(default=False)),
                ('content_type', models.CharField(choices=[('file', 'digital_product')], default='file', max_length=128)),
                ('content_file', models.FileField(blank=True, upload_to='digital_contents')),
                ('max_downloads', models.IntegerField(blank=True, null=True)),
                ('url_valid_days', models.IntegerField(blank=True, null=True)),
                ('product_variant', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='digital_content', to='saleor.productvariant')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CollectionProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(db_index=True, editable=False, null=True)),
                ('collection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collectionproduct', to='saleor.collection')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collectionproduct', to='saleor.product')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CollectionChannelListing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('is_published', models.BooleanField(default=False)),
                ('channel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='collection_listings', to='saleor.channel')),
                ('collection', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='channel_listings', to='saleor.collection')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='collection',
            name='products',
            field=models.ManyToManyField(blank=True, related_name='collections', through='saleor.CollectionProduct', to='saleor.Product'),
        ),
    ]
