# Generated by Django 3.2.10 on 2024-12-22 13:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('saleor', '0021_orderline_free_gift'),
    ]

    operations = [
        migrations.AddField(
            model_name='orderline',
            name='refunded_quantity',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.CreateModel(
            name='Refund',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('order_line', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='refunds', to='saleor.orderline')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
