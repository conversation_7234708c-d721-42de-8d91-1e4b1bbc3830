# Generated by Django 3.2.10 on 2024-01-15 15:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('saleor', '0004_auto_20231025_0757'),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('saleor_id', models.CharField(max_length=100)),
                ('saleor_user_email', models.CharField(max_length=50)),
                ('is_synced_with_quickbooks', models.BooleanField(default=False)),
                ('quick_books_id', models.CharField(blank=True, max_length=50, null=True)),
                ('quick_books_response', models.TextField(blank=True, null=True)),
                ('saleor_response', models.TextField(blank=True, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
