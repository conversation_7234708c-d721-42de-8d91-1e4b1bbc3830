version: "3.8"

services:
  prd-statics:
    image: nginx:alpine
    restart: always
    container_name: "prd-statics"
    ports:
      - 6000:80
    volumes:
      - /opt/1TB/matrix_volumes/production/static:/usr/share/nginx/html/static:ro
      - /opt/1TB/matrix_volumes/production/media:/usr/share/nginx/html/media:ro
      - ./nginx:/etc/nginx

  dev-statics:
    image: nginx:alpine
    restart: always
    container_name: "dev-statics"
    ports:
      - 6500:80
    volumes:
      - /opt/1TB/matrix_volumes/dev/static:/usr/share/nginx/html/static:ro
      - /opt/1TB/matrix_volumes/dev/media:/usr/share/nginx/html/media:ro
      - ./nginx:/etc/nginx
