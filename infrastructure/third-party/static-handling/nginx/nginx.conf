user  nginx;
worker_processes  4;
error_log  /var/log/nginx/error.log warn;
#error_log /var/log/nginx/error.log crit;
pid        /var/run/nginx.pid;

#worker_rlimit_nofile 100000;
worker_rlimit_nofile 2000;

events {
    #worker_connections 4000;
    worker_connections 1000;
    use epoll;
    multi_accept on;
}

http {
    open_file_cache max=200000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    gzip on;
    # gzip_static on;
    gzip_min_length 10240;
    gzip_comp_level 1;
    gzip_vary on;
    gzip_disable msie6;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types
        # text/html is always compressed by HttpGzipModule
        text/css
        text/javascript
        text/xml
        text/plain
        text/x-component
        application/javascript
        application/x-javascript
        application/json
        application/xml
        application/rss+xml
        application/atom+xml
        font/truetype
        font/opentype
        application/vnd.ms-fontobject
        image/svg+xml;

    reset_timedout_connection on;
    #client_body_timeout 60;
    #send_timeout 2;
    #keepalive_timeout 30;
    #keepalive_requests 100000;




    ###myconf
    proxy_connect_timeout 300;
    proxy_send_timeout 300;
    proxy_read_timeout 300;
    client_body_timeout 120;
    send_timeout 300;

    keepalive_timeout 120;
    keepalive_requests 200;
    keepalive_time 10m;
    keepalive_disable msie6;

    # limit the number of requests for a given session
    limit_req_zone $binary_remote_addr zone=req_limit_per_ip:10m rate=5r/s;
    limit_req_status 429;

    # limit the number of connections per single IP
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

    # maximum number and size of buffers for large headers to read from client request
    large_client_header_buffers 4 256k;


    # if the request body size is more than the buffer size, then the entire (or partial)
    # request body is written into a temporary file
    client_body_buffer_size  128k;
    ###





    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    log_format upstream_time 'remote_addr=$remote_addr'
                         ' | remote_user=$remote_user'
			 ' | time=$time_local'
			 ' | request="$request"'
			 ' | status=$status'
			 ' | body_bytes_sent=$body_bytes_sent'
			 ' | http_referer="$http_referer"'
			 ' | http_user_agent="$http_user_agent"'
			 ' | rt=$request_time'
			 ' | uct=$upstream_connect_time'
			 ' | uht=$upstream_header_time'
			 ' | urt=$upstream_response_time'
			 ' | X-Real-IP=$http_x_real_ip';

    access_log  /var/log/nginx/access.log upstream_time;

    include /etc/nginx/conf.d/*.conf;
}
