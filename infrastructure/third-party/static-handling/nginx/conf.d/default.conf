server {
    listen       80;
    server_name  _;

    charset utf8;
    access_log off;
    root   /usr/share/nginx/html;

    location / {
        index  index.html index.htm;
    }

    location ~* \.(?:manifest|appcache|html?|xml|json)$ {
      expires -1;
      # access_log logs/static.log; # I don't usually include a static log
    }
    
    location ~* \.(?:rss|atom)$ {
      expires 1h;
      add_header Cache-Control "public";
    }
    
    location ~* /media {
      expires 1M;
      access_log off;
      add_header Cache-Control "public";
    }

    location ~* /static {
      expires 1M;
      access_log off;
      add_header Cache-Control "public";
    }

    location ~* \.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm|htc)$ {
      expires 1M;
      access_log off;
      add_header Cache-Control "public";
    }
    
    location ~* \.(?:css|js)$ {
      expires 1y;
      access_log off;
      add_header Cache-Control "public";
    }


    ###myconf
    limit_conn conn_limit_per_ip 10;
    limit_req zone=req_limit_per_ip burst=10 nodelay;
    ###


}
