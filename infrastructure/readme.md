# Infrastructure

## Introduction

There are two types of stages in this project, Dev and Production. **Dev** urls start with _dev_.project.domain and **productions** urls are like project.domain.

It's good to know that all the individual site build is done parallel and each contains it's own image on the registry.
also you should know that the Dynamic environments that are different in each site will be changed in their own dockerfile.

>On the next sections we'll explain how Image name convention and Port allocation is done for each _dev_ and _production_ stages.

---

## Sites

**Dev:**

| ID  | Dockerfile                      | Site name                     | Domain URL                          |
| --- | ------------------------------- | ----------------------------- | ----------------------------------- |
| 1   | dev.matrix                      | DEV:Matrix                    | dev.matrix.scft.ir                  |
| 12  | dev.links                       | DEV:Links                     | dev.scfl.ir                         |
| 9   | dev.api.panel                   | DEV:API:Panel                 | dev.api.scft.ir                     |
| 6   | dev.api.agents                  | DEV:API:Agents                | dev.api.ag.snappcarfix.com          |
| 7   | dev.api.service.centers         | DEV:API:ServiceCenters        | dev.api.sc.snappcarfix.com          |
| 8   | dev.api.sellers                 | DEV:API:Sellers               | dev.api.se.snappcarfix.com          |
| 4   | dev.api.snappcarfix             | DEV:API:SnappCarFix           | dev.api.snappcarfix.com             |
| 10  | dev.api.org.snappcarfix         | DEV:API:OrgSnappCarFix        | dev.api.org.snappcarfix.com         |
| 2   | dev.api.drivers.snappcarfix     | DEV:API:DriversSnappCarFix    | dev.api.drivers.snappcarfix.com     |
| 11  | dev.api.app.drivers.snappcarfix | DEV:API:AppDriversSnappCarFix | dev.api.app.drivers.snappcarfix.com |

**Production:**

| ID  | Dockerfile                  | Site name                 | Domain URL                      |
| --- | --------------------------- | ------------------------- | ------------------------------- |
| 1   | matrix                      | Matrix                    | matrix.scft.ir                  |
| 12  | links                       | Links                     | scfl.ir                         |
| 9   | api.panel                   | API:Panel                 | api.scft.ir                     |
| 6   | api.agents                  | API:Agents                | api.ag.snappcarfix.com          |
| 7   | api.service.centers         | API:ServiceCenters        | api.sc.snappcarfix.com          |
| 8   | api.sellers                 | API:Sellers               | api.se.snappcarfix.com          |
| 4   | api.snappcarfix             | API:SnappCarFix           | api.snappcarfix.com             |
| 10  | api.org.snappcarfix         | API:OrgSnappCarFix        | api.org.snappcarfix.com         |
| 2   | api.drivers.snappcarfix     | API:DriversSnappCarFix    | api.drivers.snappcarfix.com     |
| 11  | api.app.drivers.snappcarfix | API:AppDriversSnappCarFix | api.app.drivers.snappcarfix.com |

## Databases

**Dev:** `dev_matrix`
**Production:** `matrix`

## Image Name Convention

Note:

- Image name: `registry.t2bclub.com/snappcarfix/backend/matrix`
- Tagging format: `[CONTAINER_NAME]-[VERSION]-[BUILD_ID]`

**Dev:**

| ID  | Dockerfile                      | Site/Container name             | Tagging sample                              |
| --- | ------------------------------- | ------------------------------- | ------------------------------------------- |
| 1   | dev.matrix                      | dev-matrix                      | `dev-matrix-0.0.0-458`                      |
| 12  | dev.links                       | dev-links                       | `dev-links-0.0.0-458`                       |
| 9   | dev.api.panel                   | dev-api-panel                   | `dev-api-panel-0.0.0-458`                   |
| 6   | dev.api.agents                  | dev-api-agents                  | `dev-api-agents-0.0.0-458`                  |
| 7   | dev.api.service.centers         | dev-api-service-centers         | `dev-api-service-centers-0.0.0-458`         |
| 8   | dev.api.sellers                 | dev-api-sellers                 | `dev-api-sellers-0.0.0-458`                 |
| 4   | dev.api.snappcarfix             | dev-api-snappcarfix             | `dev-api-snappcarfix-0.0.0-458`             |
| 10  | dev.api.org.snappcarfix         | dev-api-org-snappcarfix         | `dev-api-org-snappcarfix-0.0.0-458`         |
| 2   | dev.api.drivers.snappcarfix     | dev-api-drivers-snappcarfix     | `dev-api-drivers-snappcarfix-0.0.0-458`     |
| 11  | dev.api.app.drivers.snappcarfix | dev-api-app-drivers-snappcarfix | `dev-api-app-drivers-snappcarfix-0.0.0-458` |

**Production:**

| ID  | Dockerfile                  | Site/Container name         | Tagging sample                          |
| --- | --------------------------- | --------------------------- | --------------------------------------- |
| 1   | matrix                      | matrix                      | `matrix-0.0.0-458`                      |
| 12  | links                       | links                       | `links-0.0.0-458`                       |
| 9   | api.panel                   | api-panel                   | `api-panel-0.0.0-458`                   |
| 6   | api.agents                  | api-agents                  | `api-agents-0.0.0-458`                  |
| 7   | api.service.centers         | api-service-centers         | `api-service-centers-0.0.0-458`         |
| 8   | api.sellers                 | api-sellers                 | `api-sellers-0.0.0-458`                 |
| 4   | api.snappcarfix             | api-snappcarfix             | `api-snappcarfix-0.0.0-458`             |
| 10  | api.org.snappcarfix         | api-org-snappcarfix         | `api-org-snappcarfix-0.0.0-458`         |
| 2   | api.drivers.snappcarfix     | api-drivers-snappcarfix     | `api-drivers-snappcarfix-0.0.0-458`     |
| 11  | api.app.drivers.snappcarfix | api-app-drivers-snappcarfix | `api-app-drivers-snappcarfix-0.0.0-458` |


## Static Handling

Statics are not different in each site, so they can share it with each other, 
**matrix** provided the shared statics and media; placing is explaied in below table:

| Site name  | Container name     | Shared statics dir                          | Shared media dir                           |
| ---------- | ------------------ | ------------------------------------------- | ------------------------------------------ |
| dev-matrix | dev-statics        | `/opt/1TB/matrix_volumes/dev/static`        | `/opt/1TB/matrix_volumes/dev/media`        |
| matrix     | production-statics | `/opt/1TB/matrix_volumes/production/static` | `/opt/1TB/matrix_volumes/production/media` |


## Port Allocation

Another thing to remember is that *ports* differ in different stages, _Dev_ starts with `65XX` and _Production_ starts with `60XX`.
hopefully no changing is schedules, if so well change this doc :)

**Dev:**

| Container name                  | Port Number |
| ------------------------------- | ----------- |
| dev-statics                     | `6500`      |
| dev-matrix                      | `6501`      |
| dev-links                       | `6502`      |
| dev-api-panel                   | `6510`      |
| dev-api-agents                  | `6511`      |
| dev-api-service-centers         | `6512`      |
| dev-api-sellers                 | `6513`      |
| dev-api-snappcarfix             | `6520`      |
| dev-api-org-snappcarfix         | `6521`      |
| dev-api-drivers-snappcarfix     | `6522`      |
| dev-api-app-drivers-snappcarfix | `6523`      |

**Production:**

| Container name              | Port Number |
| --------------------------- | ----------- |
| production-statics          | `6000`      |
| matrix                      | `6001`      |
| links                       | `6002`      |
| api-panel                   | `6010`      |
| api-agents                  | `6011`      |
| api-service-centers         | `6012`      |
| api-sellers                 | `6013`      |
| api-snappcarfix             | `6020`      |
| api-org-snappcarfix         | `6021`      |
| api-drivers-snappcarfix     | `6022`      |
| api-app-drivers-snappcarfix | `6023`      |
