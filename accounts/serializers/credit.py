from accounts.models import Credit
from core import serializers
from core.utils.timezone import utc_to_iran_jalali_string


class CreditSerializer(serializers.ModelSerializer):
    username = serializers.CharField(source="user.username")
    date = serializers.SerializerMethodField()
    site = serializers.SerializerMethodField()

    class Meta:
        model = Credit
        fields = (
            "id", "site", "username", "date", "title", "amount", "state", "type", "wallet_id"
        )

    def get_date(self, obj):
        return utc_to_iran_jalali_string(obj.created_at, "%Y/%m/%d")

    def get_site(self, obj):
        return {
            "id": obj.site.id,
            "name": obj.site.name
        }
