<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Page Title</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        body {
            background-color: #ffffff;
            font-size: 12px;
            font-family: "Helvetica", "Arial", sans-serif;
            text-align: center;
            padding: 240px;
        }

        .container {
            max-width: 400px;
            margin: auto;
        }

        .form-container {
            background-color: #ffffff;
            border-radius: 4px;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .alert-container {
            max-width: 400px;
            margin: auto;
        }

        .alert {
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border-color: #ffeeba;
        }

        / Bubbly Button Styles /
        .bubbly-button {
            font-family: "Helvetica", "Arial", sans-serif;
            display: inline-block;
            font-size: 18px;
            padding: 1em 2em;
            margin-top: 20px;
            margin-bottom: 20px;
            -webkit-appearance: none;
            appearance: none;
            background-color: #0066ff;
            color: #fff;
            border-radius: 10px;
            border: none;
            cursor: pointer;
            position: relative;
            transition: transform ease-in 0.1s, box-shadow ease-in 0.25s;
            box-shadow: 0 2px 25px rgba(0, 34, 255, 0.5);
        }

        .bubbly-button:focus {
            outline: 0;
        }

        .bubbly-button:before,
        .bubbly-button:after {
            position: absolute;
            content: "";
            display: block;
            width: 180%;
            height: 100%;
            left: -20%;
            z-index: -1000;
            transition: all ease-in-out 0.5s;
            background-repeat: no-repeat;
        }

        .bubbly-button:before {
            display: none;
            top: -75%;
            background-image: radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, transparent 20%, #ff0081 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, transparent 10%, #ff0081 15%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%);
            background-size: 10% 10%, 20% 20%, 15% 15%, 20% 20%, 18% 18%, 10% 10%, 15% 15%, 10% 10%, 18% 18%;
        }

        .bubbly-button:after {
            display: none;
            bottom: -75%;
            background-image: radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, transparent 10%, #ff0081 15%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%),
                                radial-gradient(circle, #ff0081 20%, transparent 20%);
            background-size: 15% 15%, 20% 20%, 18% 18%, 20% 20%, 15% 15%, 10% 10%, 20% 20%;
        }

        .bubbly-button:active {
            transform: scale(0.9);
            background-color: #e60074;
            box-shadow: 0 2px 25px rgba(255, 0, 130, 0.2);
        }

        .bubbly-button.animate:before {
            display: block;
            animation: topBubbles ease-in-out 0.75s forwards;
        }

        .bubbly-button.animate:after {
            display: block;
            animation: bottomBubbles ease-in-out 0.75s forwards;
        }

        @keyframes topBubbles {
            0% {
                background-position: 5% 90%, 10% 90%, 10% 90%, 15% 90%, 25% 90%, 25% 90%, 40% 90%, 55% 90%, 70% 90%;
            }
            50% {
                background-position: 0% 80%, 0% 20%, 10% 40%, 20% 0%, 30% 30%, 22% 50%, 50% 50%, 65% 20%, 90% 30%;
            }
            100% {
                background-position: 0% 70%, 0% 10%, 10% 30%, 20% -10%, 30% 20%, 22% 40%, 50% 40%, 65% 10%, 90% 20%;
                background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
            }
        }

        @keyframes bottomBubbles {
            0% {
                background-position: 10% -10%, 30% 10%, 55% -10%, 70% -10%, 85% -10%, 70% -10%, 70% 0%;
            }
            50% {
                background-position: 0% 80%, 20% 80%, 45% 60%, 60% 100%, 75% 70%, 95% 60%, 105% 0%;
            }
            100% {
                background-position: 0% 90%, 20% 90%, 45% 70%, 60% 110%, 75% 80%, 95% 70%, 110% 10%;
                background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
            }
        }
    </style>
</head>
<body>

<div class="container">
    <div class="form-container">
        <form method="post">
            {% csrf_token %}
            <input type="hidden" name="sync_data" value="1">
            <button type="submit" class="bubbly-button btn btn-primary btn-block animate">Sync Data!</button>
        </form>
    </div>

    <div class="alert-container">
        {% if success %}
            <div dir="rtl" class="alert alert-success" role="alert">
                {{ success }}
            </div>
        {% endif %}
        {% if error %}
            <div dir="rtl" class="alert alert-warning" role="alert">
                {{ error }}
            </div>
        {% endif %}
    </div>
</div>

<!-- Bootstrap JS and Popper.js (if needed) -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

</body>
</html>
