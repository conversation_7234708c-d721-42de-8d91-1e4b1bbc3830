# Generated by Django 3.2.10 on 2023-09-17 08:58

import accounts.managers.user
import core.validators.ascii
import core.validators.credit_card
import core.validators.digit
import core.validators.mobile_number
import core.validators.national_id
import core.validators.phone_number
import core.validators.sheba_number
from django.conf import settings
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('locations', '0001_initial'),
        ('sites', '0002_alter_domain_unique'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.ASCIIUsernameValidator()])),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('mobile_number', models.CharField(blank=True, max_length=15, null=True, validators=[core.validators.mobile_number.MobileNumberValidator()])),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True, validators=[core.validators.phone_number.PhoneNumberValidator()])),
                ('first_name', models.CharField(blank=True, max_length=30, null=True)),
                ('last_name', models.CharField(blank=True, max_length=150, null=True)),
                ('first_name_en', models.CharField(blank=True, max_length=50, null=True, validators=[core.validators.ascii.ASCIIValidator()])),
                ('last_name_en', models.CharField(blank=True, max_length=150, null=True, validators=[core.validators.ascii.ASCIIValidator()])),
                ('father_name', models.CharField(blank=True, max_length=150, null=True)),
                ('id_card_number', models.CharField(blank=True, max_length=10, null=True, validators=[core.validators.digit.DigitValidator()])),
                ('id_card_serial', models.CharField(blank=True, max_length=6, null=True, validators=[core.validators.digit.DigitValidator()])),
                ('id_card_series', models.CharField(blank=True, max_length=10, null=True)),
                ('national_id', models.CharField(blank=True, max_length=10, null=True, validators=[core.validators.national_id.national_id_validator])),
                ('credit_card', models.CharField(blank=True, max_length=16, null=True, validators=[core.validators.credit_card.credit_card_validator])),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female')], default='male', max_length=10)),
                ('birthdate', models.DateField(blank=True, null=True)),
                ('marital_status', models.CharField(choices=[('single', 'Single'), ('married', 'Married'), ('widowed', 'Windowed'), ('divorced', 'Divorced')], default='single', max_length=100)),
                ('children_count', models.PositiveIntegerField(default=0)),
                ('education_stage', models.CharField(choices=[('none', 'None'), ('primary', 'Primary'), ('secondary', 'Secondary'), ('diploma', 'Diploma'), ('associate', 'Associate'), ('bachelor', 'Bachelor'), ('master', 'Master'), ('doctoral', 'Doctoral'), ('post_doctoral', 'Post Doctoral')], default='none', max_length=100)),
                ('is_staff', models.BooleanField(default=False, verbose_name='staff status')),
                ('t2bco_id', models.PositiveIntegerField(blank=True, null=True)),
                ('spider_id', models.PositiveIntegerField(blank=True, null=True, unique=True)),
                ('is_snapp_driver', models.BooleanField(default=False)),
                ('snapp_api_date', models.DateTimeField(blank=True, null=True)),
                ('snapp_id', models.PositiveIntegerField(blank=True, null=True)),
                ('snapp_token', models.TextField(blank=True)),
                ('snapp_response', models.JSONField(blank=True, null=True)),
                ('sheba_number', models.CharField(blank=True, max_length=26, null=True, validators=[core.validators.sheba_number.sheba_number_validator])),
                ('account_number', models.CharField(blank=True, max_length=30, null=True)),
                ('is_normal_customer', models.BooleanField(default=False)),
                ('cart_to_sheba_api', models.TextField(blank=True, null=True)),
                ('address', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='locations.address')),
            ],
            options={
                'db_table': 'matrix_accounts_user',
            },
            managers=[
                ('objects', accounts.managers.user.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='EducationMajor',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=250)),
            ],
            options={
                'db_table': 'matrix_accounts_education_major',
            },
        ),
        migrations.CreateModel(
            name='Proof',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('type', models.CharField(choices=[('id_card', 'ID Card'), ('education', 'Education'), ('other', 'Other')], default='id_card', max_length=100)),
                ('is_active', models.BooleanField(default=False, verbose_name='active')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='poof', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'matrix_accounts_proof',
            },
        ),
        migrations.CreateModel(
            name='Credit',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(max_length=255)),
                ('desc', models.CharField(blank=True, max_length=255, null=True)),
                ('amount', models.IntegerField()),
                ('type', models.CharField(choices=[('admin', 'Admin'), ('user_discount', 'User Discount'), ('user_payment', 'User Payment'), ('returned', 'Returned'), ('cash_out', 'Cash Out'), ('service_center_bill', 'Service Center Bill'), ('service_center_gift', 'Service Center Gift'), ('service_center_new_user', 'Service Center New User'), ('service_center_serial_number', 'Service Center Serial Number'), ('agent_bill', 'Agent Bill'), ('agent_checkout', 'Agent Checkout'), ('agent_new_user', 'Agent New User'), ('agent_service_center_factor', 'Agent Service Center Factor'), ('agent_service_center_factor_first_head', 'Agent Service Center Factor First Head'), ('agent_service_center_factor_second_head', 'Agent Service Center Factor Second Head'), ('agent_service_center_factor_third_head', 'Agent Service Center Factor Third Head')], default='admin', max_length=100)),
                ('object_id', models.CharField(blank=True, max_length=100, null=True)),
                ('content_type', models.ForeignKey(blank=True, limit_choices_to=models.Q(('model__in', ['Bill', 'User'])), null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_items', to='sites.site')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_items', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'matrix_accounts_credit',
            },
        ),
        migrations.AddField(
            model_name='user',
            name='education_major',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='accounts.educationmajor'),
        ),
        migrations.AddField(
            model_name='user',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups'),
        ),
        migrations.AddField(
            model_name='user',
            name='sites',
            field=models.ManyToManyField(blank=True, related_name='users', to='sites.Site'),
        ),
        migrations.AddField(
            model_name='user',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions'),
        ),
        migrations.CreateModel(
            name='UserAddress',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('address', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_addresses', to='locations.address')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_addresses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'matrix_accounts_user_address',
                'unique_together': {('user', 'address')},
            },
        ),
    ]
