from rest_framework.authtoken.models import Token
from rest_framework.authtoken.models import TokenProxy

from accounts.models import \
    EducationMajor, \
    Proof, \
    User, \
    UserAddress
from core import admin
from django.contrib.auth.models import Permission

from .token import TokenAdmin
from .education_major import EducationMajorAdmin
from .proof import ProofAdmin
from .user import UserAdmin
from .user_address import UserAddressAdmin

admin.site.unregister(TokenProxy)
admin.site.register(Token, TokenAdmin)
admin.site.register(Permission)
admin.site.register(User, UserAdmin)
admin.site.register(EducationMajor, EducationMajorAdmin)
admin.site.register(Proof, ProofAdmin)
admin.site.register(UserAdd<PERSON>, UserAddressAdmin)

