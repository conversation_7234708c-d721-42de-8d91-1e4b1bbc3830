# from django.utils.translation import gettext as _
# from django.urls import reverse
# from rest_framework.test import APITestCase
# from rest_framework import status
#
# from accounts.models import User
#
#
# class LoginDashboardApiTest(APITestCase):
#
#     def setUp(self):
#         self.super_user = User.objects.create_superuser(username='superuser', password='<PASSWORD>')
#         self.staff_user = User.objects.create_staff(username='staff', password='<PASSWORD>')
#         self.user = User.objects.create_user(username='user', password='<PASSWORD>')
#
#         self.login_url = reverse('accounts:dashboard_v1:login')
#
#     def test_no_data(self):
#         response = self.client.post(self.login_url, data={})
#         self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#         response = self.client.post(self.login_url, data={"password": "ss"})
#         self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#         response = self.client.post(self.login_url, data={"username": "ss"})
#         self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_wrong_data(self):
#         response = self.client.post(self.login_url, data={"password": "ss", "username": "user"})
#         self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
#         self.assertEqual(_("wrong information"), response.json()["message"])
#
#     def test_normal_user(self):
#         response = self.client.post(self.login_url, data={"password": "<PASSWORD>", "username": "user"})
#         self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
#         self.assertEqual(_("user not found"), response.json()["message"])
#
#     def test_staff_user(self):
#         response = self.client.post(self.login_url, data={"password": "<PASSWORD>", "username": "staff"})
#         self.assertEqual(status.HTTP_200_OK, response.status_code)
#
#     def test_super_user(self):
#         response = self.client.post(self.login_url, data={"password": "<PASSWORD>", "username": "superuser"})
#         self.assertEqual(status.HTTP_200_OK, response.status_code)
