from django import forms


class UserCreditForm(forms.Form):
    excel = forms.FileField(required=False)
    value = forms.CharField(max_length=200, required=False)
    charge_mode = forms.Select(choices=(("group", "Group"), ("organization", "Organization"), ("all", "All")))
    charge_amount = forms.IntegerField(required=True)
    charge_site = forms.IntegerField(required=True, initial="4")
    charge_title = forms.CharField(max_length=200, required=True)
    charge_desc = forms.CharField(max_length=200, required=True, initial="ADMIN CREDIT")
    charge_action = forms.Select(
        choices=(("normal", "Normal"), ("increase", "Increase"), ("decrease", "Decrease"), ("fix", "Fix"))
    )
