import logging

from django.contrib import messages
import threading
from django.db.models.signals import pre_save
from django.core.exceptions import ValidationError

from accounts.models import CreditCashOut

from django.dispatch import receiver

from constants import CreditType
from payments.models import Refund

logger = logging.getLogger("accounts")


# @receiver(pre_save, sender=CreditCashOut)
# def decrease_credit_cash_out(sender, instance, **kwargs):
    # if instance.id is None:
    #     return
    # pre_instance = CreditCashOut.objects.get(id=instance.id)
    # if instance.status == "paid" and pre_instance.status != instance.status:
    #     user = instance.credits.last().user
    #     site = instance.credits.last().site
    #     try:
    #         user.decrease_credit(
    #             title="کاهش اعتبار کاربر جهت عودت اعتبار به حساب.",
    #             amount=instance.payable_amount,
    #             credit_type=CreditType.CASH_OUT,
    #             content_type_model="creditcashout",
    #             object_id=instance.id,
    #             desc="SYSTEM CREDIT CASH OUT",
    #             site=site
    #         )
    #     except Exception as e:
    #         logger.error("[CREDIT CASH OUT]/ ERROR: {}".format(str(e)))


