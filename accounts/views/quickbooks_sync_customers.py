import time
import logging
import threading


from django.shortcuts import render, redirect
from django.views import View
from quick_book.utils import QuickBook
from quick_book.utils import saleor_quickbooks
from django.core.cache import cache

logger = logging.getLogger("accounts")


class QuickBooksSyncDataAdminView(View):
    template_name = "accounts/quickbooks_sync_customers.html"

    def get(self, request):
        return render(
            request,
            self.template_name,
        )

    def sync_orders_thread(self):
        try:
            saleor_quickbooks.sync_orders()
        except Exception as e:
            logger.error("QUICK-BOOKS-VIEW| ERROR:{}".format(str(e)))

    def post(self, request):
        sync_data = int(request.POST["sync_data"])
        if sync_data:
            try:
                thread = threading.Thread(target=self.sync_orders_thread, args=())
                thread.start()
            except Exception as e:
                return render(
                    request,
                    self.template_name,
                    {
                        "error": str(e)
                    }
                )
            if cache.get(QuickBook.CASH_KEY_ACCESS):
                return render(
                    request,
                    self.template_name,
                    {
                        "success": "Done"
                    }
                )

            tries = 900
            while not cache.get("quickbooks_redirect_url") and not cache.get(QuickBook.CASH_KEY_ACCESS) and tries > 0:
                tries -= 1
                time.sleep(0.1)
            return redirect(cache.get("quickbooks_redirect_url"))
        else:
            return render(
                request,
                self.template_name,
            )
