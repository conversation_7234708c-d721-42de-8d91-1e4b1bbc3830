from constants import ProofType
from core import models


class Proof(models.AbstractBaseModel):
    user = models.ForeignKey("accounts.User", on_delete=models.PROTECT, related_name="poof")
    type = models.CharField(max_length=100, choices=ProofType.choices, default=ProofType.ID_CARD)
    is_active = models.BooleanField("active", default=False)

    class Meta:
        db_table = "matrix_accounts_proof"
