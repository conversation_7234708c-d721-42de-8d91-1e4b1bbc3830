import logging

from constants import TransactionState, CreditType, GasType
from core import models
from django.contrib.contenttypes.fields import Generic<PERSON><PERSON>ign<PERSON>ey
from django.contrib.contenttypes.models import ContentType

logger = logging.getLogger("credit")


class CreditQuerySet(models.QuerySet):
    def agent_credits(self):
        return self.filter(site_id=6)

    def service_center_credits(self):
        return self.filter(site_id=7)


class Credit(models.AbstractBaseModel):
    user = models.ForeignKey("accounts.User", related_name="credit_items", on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    desc = models.CharField(max_length=255, null=True, blank=True)
    site = models.ForeignKey(models.Site, on_delete=models.CASCADE, related_name="credit_items")
    amount = models.IntegerField()
    type = models.CharField(max_length=100, choices=CreditType.choices, default=CreditType.ADMIN)
    content_object = GenericForeign<PERSON>ey()
    object_id = models.CharField(max_length=100, null=True, blank=True)
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        limit_choices_to=(
            models.Q(model__in=["Bill",
                                "User"
                                ]
                     )
        ),
    )

    objects = CreditQuerySet.as_manager()

    class Meta:
        db_table = 'matrix_accounts_credit'

    @property
    def state(self):
        return TransactionState.get(self.amount)

    @staticmethod
    def increase(
        user, title, amount, credit_type=CreditType.ADMIN, content_type_model=None, object_id=None, desc=None, site=None
    ):
        try:
            if not site:
                site = models.Site.objects.get_current()
            elif isinstance(site, int):
                site = models.Site.objects.get(id=site)
            if amount < 0:
                amount *= -1
            credit_item = Credit.objects.create(
                user=user, title=title, amount=amount, desc=desc, site=site, type=credit_type
            )
            if content_type_model:
                credit_item.content_type = ContentType.objects.get(model=content_type_model)
                credit_item.object_id = object_id
                credit_item.save()
            logger.info(
                "credit={} title={} amount={} desc={} site={}".format(
                    credit_item.id, title, amount, desc, site
                ), extra={"user": user}
            )
            return True, credit_item
        except Exception as e:
            logger.exception(
                "title={} amount={} desc={} site={} | {}".format(
                    title, amount, desc, site, e
                ), extra={"user": user}
            )
            return False, str(e)

    @staticmethod
    def decrease(
        user, title, amount, credit_type=CreditType.ADMIN, content_type_model=None, object_id=None, desc=None, site=None
    ):
        try:
            if not site:
                site = models.Site.objects.get_current()
            elif isinstance(site, int):
                site = models.Site.objects.get(id=site)
            if amount > 0:
                amount *= -1
            credit_item = Credit.objects.create(
                user=user, title=title, amount=amount, site=site, desc=desc, type=credit_type
            )
            if content_type_model:
                credit_item.content_type = ContentType.objects.get(model=content_type_model)
                credit_item.object_id = object_id
                credit_item.save()
            logger.info(
                "credit={} title={} amount={} desc={} site={}".format(
                    credit_item.id, title, amount, desc, site
                ), extra={"user": user}
            )
            return True, credit_item
        except Exception as e:
            logger.exception(
                "title={} amount={} desc={} site={} | {}".format(
                    user, title, amount, desc, site, e
                ), extra={"user": user}
            )
            return False, str(e)

    def __str__(self):
        return "[{}] {} / {} / {} / {}".format(self.id, self.site, self.user, self.amount, self.title)

    @property
    def previous_total_amount(self):
        return self.user.credit_items.filter(site=self.site, created_at__lt=self.created_at).get_sum("amount")

    @property
    def current_total_amount(self):
        return self.user.credit_items.filter(site=self.site, created_at__lte=self.created_at).get_sum("amount")
