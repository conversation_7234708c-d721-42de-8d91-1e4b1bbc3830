import logging

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import CooKieAuthentication
from core.http import JsonResponse

logger = logging.getLogger("dashboard")


class LogoutApi(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def post(self, request):
        response = JsonResponse()
        response.delete_cookie(key="access_token", samesite='none', path='/')
        response.delete_cookie(key="refresh_token", samesite='none', path='/')
        return response
