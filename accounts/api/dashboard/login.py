import logging
from datetime import timedelta

from django.contrib.auth import authenticate
from django.utils.translation import gettext
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import Coo<PERSON><PERSON><PERSON>uthentication
from core.http import JsonResponse

logger = logging.getLogger("dashboard")


class LoginApi(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request):
        self.exception_data = {"message": gettext("invalid data"), "status": 400}
        username, password = request.data["username"], request.data["password"]
        user = authenticate(username=username, password=password)
        if user is None:
            logger.info("Login Unsuccessful username:{} , password:{}".format(username, password))
            return JsonResponse(message=gettext("wrong information"), status=400)

        if not user.is_staff and not user.is_superuser:
            logger.info("Login Unsuccessful user is not staff username:{} , password:{}".format(username, password))
            return JsonResponse(message=gettext("user not found"), status=400)

        refresh = RefreshToken.for_user(user)
        access = refresh.access_token

        response = JsonResponse(data={"username": user.username, "fullname": user.get_full_name()})
        response.set_cookie(key="access_token", value=access, httponly=True, secure=True, samesite='none', path='/',
                            max_age=timedelta(minutes=200).total_seconds())
        response.set_cookie(key="refresh_token", value=refresh, httponly=True, secure=True, samesite='none',
                            max_age=timedelta(days=14).total_seconds(),
                            path='/', )

        return response
