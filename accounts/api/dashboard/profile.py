import logging

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import CooKieAuthentication
from core.http import JsonResponse

logger = logging.getLogger("dashboard")


class ProfileApi(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request):
        response = JsonResponse({"username": request.user.username, "fullname": request.user.get_full_name()})
        return response
