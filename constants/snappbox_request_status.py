from django.db.models import TextChoices


class SnappboxReqeustStatus(TextChoices):
    ACCEPTED = "accepted", "Accepted"
    ARRIVED = "arrived", "Arrived"
    PREORDER = "preorder", "Preorder"
    PENDING = "pending", "Pending",
    REORDERED = "reordered", "Reordered"
    PICKED_UP = "pickup", "Pickup"
    DELIVERED = "delivered", "Delivered"
    CANCELLED = "cancelled", "Cancelled"
    CANCELLED_BY_SYSTEM = "cancelled_by_system", "Cancelled By System"
    ARRIVED_AT_PICKUP = "arrived_at_pickup", "Arrived At Pickup"
    ARRIVED_AT_DROP_OFF = "arrived_at_drop_off", "Arrived At Drop off"

