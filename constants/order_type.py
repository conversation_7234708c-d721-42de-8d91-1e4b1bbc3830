from django.db.models import TextChoices


class OrderType(TextChoices):
    SERVICE_CENTER_SERVICE = "service_center_service", "Service Center Service"
    SERVICE_CENTER_DELIVERY = "service_center_delivery", "Service Center Delivery"
    LOCATION_SERVICE = "location_service", "Location Service"
    LOCATION_DELIVERY = "location_delivery", "Location Delivery"
    POST_DELIVERY = "post_delivery", "Post Delivery"
    SNAPP_BOX_DELIVERY = "snapp_box_delivery", "Snapp Box Delivery"
    NO_SERVICE_METHOD = "no_service_method", "No Service Method"

    @staticmethod
    def get_symbol(value, default="-"):
        values = {
            "service_center_service": "ss",
            "service_center_delivery": "sd",
            "location_service": "ls",
            "location_delivery": "ld",
            "post_delivery": "pd",
            "snapp_box_delivery": "sbd",
            "no_service_method": "nsm",
        }
        return values.get(value, default)

    @staticmethod
    def get_display(value, default="-"):
        values = {
            "service_center_service": "خدمات در مراکز",
            "service_center_delivery": "تحویل در مراکز",
            "location_service": "خدمات در محل",
            "location_delivery": "تحویل در محل",
            "post_delivery": "ارسال با پست",
            "snapp_box_delivery": "تحویل با اسنپ باکس",
            "no_service_method": "خرید عادی",
        }
        return values.get(value, default)

    @staticmethod
    def get_display_names():
        return {
            "service_center_service": "خدمات در مراکز",
            "service_center_delivery": "تحویل در مراکز",
            "location_service": "خدمات در محل",
            "location_delivery": "تحویل در محل",
            "post_delivery": "ارسال با پست",
            "snapp_box_delivery": "تحویل با اسنپ باکس",
            "no_service_method": "خرید عادی",
        }
