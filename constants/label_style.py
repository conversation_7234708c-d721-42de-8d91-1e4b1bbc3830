from django.db.models import TextChoices


class LabelStyle(TextChoices):
    NONE = "none", "-"
    SNAPP = "snapp", "Snapp"
    SPECIAL_OFFER = "special_offer", "Special Offer"
    BEST_SELLER = "best_seller", "Best Seller"
    DISCOUNT_CODE = "discount_code", "Discount Code"

    @staticmethod
    def get_display(value, default=""):
        values = {
            "none": "",
            "snapp": "تخفیف ويژه",
            "special_offer": "پیشنهاد ویژه",
            "best_seller": "پرفروش‌ترین‌ها",
            "discount_code": "کد تخفیف",
        }
        return values.get(value, default)
