from core.templates.utils import colored_text
from django.db.models import TextChoices


class Grade(TextChoices):
    A = "a", "A"
    AP = "ap", "A+"
    B = "b", "B"
    BP = "bp", "B+"
    C = "c", "C"
    CP = "cp", "C+"

    @staticmethod
    def get_colored(value):
        values = {
            "a": ("A", "#53a70d"),
            "ap": ("AP", "#53a70d"),
            "b": ("B", "#f5bb00"),
            "bp": ("BP", "#f5bb00"),
            "c": ("C", "#bc0e0e"),
            "cp": ("CP", "#bc0e0e"),
        }
        return colored_text(*values.get(value, (value, "#bc0e0e")))
