from django.db.models import TextChoices


class FireBaseServerKey(TextChoices):
    T2BON = (
        "AAAAvmR0Bc4:APA91bHRzSNxnS8hhhHBoTlbqKamHKQuoTCDiFv024GcUBut5ilmLqaeCQXzFNGxheHAJGgcV6Zlbh6vevjW7eNwemZPgWcZV5qAHoDQOLnJVo_5RsoGNg5lhbnDgH8zgNcbGiqNuYOW",
        "T2Bon",
    )
    T2BSERVICE = (
        "AAAASCSPwoI:APA91bHia_FMcNTMfygS4atsM-5l3LWpy6WN0ie0-3COAnN3bHjeVxGR4QcqXUUMkfWzx6MLO_TEOXpIOD6seBRtghBDFViP7AFJ3E54lFxlRt3NyaoiMJcJjQE9DXkzYrvZTpnh4r5N",
        "T2BService",
    )
    T2BAGENT = (
        "AAAAA83GfKc:APA91bHVNhRbel0Pn0AriVQw4LXotWzXYSG5-Mqr7nuydJ9WD0wadxB2lBjrhplzqInuZ9d9ts7EnPZYQo_mWijnKly47ov9VDHPXws-6_xyK2d55rTr6-sSa00buFOBN7I-HYIoov__",
        "T2BAgent",
    )
