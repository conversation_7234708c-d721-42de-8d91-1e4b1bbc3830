import datetime

from django.db.models import TextChoices


class DeliveryShift(TextChoices):
    FIRST = "first", "First"
    SECOND = "second", "Second"
    ALL = "all", "All"

    @staticmethod
    def get_display(value, default="-"):
        values = {
            "first": "۹ تا ۱۳",
            "second": "۱۵ تا ۲۰",
        }
        return values.get(value, default)

    @staticmethod
    def get_display_names(value, default="-"):
        values = {
            "first": "09:00-13:00",
            "second": "15:00-20:00",
        }
        return values.get(value, default)

    @staticmethod
    def get_symbol(value, default="-"):
        values = {"first": "f", "second": "s"}
        return values.get(value, default)

    @staticmethod
    def get_time(value, default="-"):
        values = {
            "first": {"start": datetime.time(hour=9), "end": datetime.time(hour=12)},
            "second": {"start": datetime.time(hour=15), "end": datetime.time(hour=19)},
        }
        return values.get(value, default)

    @staticmethod
    def get_data(value, default=[]):
        values = {
            "all": [
                {"name": DeliveryShift.get_display("first"), "value": "first"},
                {"name": DeliveryShift.get_display("second"), "value": "second"},
            ],
            "first": [{"name": DeliveryShift.get_display("first"), "value": "first"}],
            "second": [{"name": DeliveryShift.get_display("second"), "value": "second"}],
        }
        return values.get(value, default)

    # fixme: this must be removed!
    @staticmethod
    def get_display_name(value, category_id=None, default="-"):
        if category_id == 119:
            return {
                DeliveryShift.FIRST: "۸ تا ۱۰",
                DeliveryShift.SECOND: "۱۱ تا ۱۳",
            }.get(value, default)
        return DeliveryShift.get_display(value, default)
