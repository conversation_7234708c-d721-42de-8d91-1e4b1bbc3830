import datetime

from django.db.models import TextChoices


class SnappBoxDeliveryShift(TextChoices):
    FIRST = "first", "First"
    SECOND = "second", "Second"
    ALL = "all", "All"

    @staticmethod
    def get_display(value, default="-"):
        values = {
            "first": "۹ تا ۱۳",
            "second": "۱۳ تا ۱۷",
        }
        return values.get(value, default)

    @staticmethod
    def get_display_names(value, default="-"):
        values = {
            "first": "09:00-13:00",
            "second": "13:00-17:00",
        }
        return values.get(value, default)

    @staticmethod
    def get_symbol(value, default="-"):
        values = {"first": "f", "second": "s"}
        return values.get(value, default)

    @staticmethod
    def get_time(value, default="-"):
        values = {
            "first": {"start": datetime.time(hour=9), "end": datetime.time(hour=12)},
            "second": {"start": datetime.time(hour=13), "end": datetime.time(hour=16)},
        }
        return values.get(value, default)

    @staticmethod
    def get_data(value, default=[]):
        values = {
            "all": [
                {"name": SnappBoxDeliveryShift.get_display("first"), "value": "first"},
                {"name": SnappBoxDeliveryShift.get_display("second"), "value": "second"},
            ],
            "first": [{"name": SnappBoxDeliveryShift.get_display("first"), "value": "first"}],
            "second": [{"name": SnappBoxDeliveryShift.get_display("second"), "value": "second"}],
        }
        return values.get(value, default)

    # fixme: this must be removed!
    @staticmethod
    def get_display_name(value, category_id=None, default="-"):
        if category_id == 119:
            return {
                SnappBoxDeliveryShift.FIRST: "۸ تا ۱۰",
                SnappBoxDeliveryShift.SECOND: "۱۱ تا ۱۳",
            }.get(value, default)
        return SnappBoxDeliveryShift.get_display(value, default)
