from django.db.models import TextChoices


class DeliveryStatus(TextChoices):
    CREATED = "created", "Created"
    CONFIRMED = "confirmed", "Confirmed"
    PROCESSED = "processed", "Processed"
    PACKAGED = "packaged", "Packaged"
    POSTED = "posted", "Posted"
    DELIVERED = "delivered", "Delivered"
    REJECTED = "rejected", "Rejected"

    @staticmethod
    def get_code(value, default="-"):
        values = {
            0: "created",
            1: "confirmed",
            2: "processed",
            3: "packaged",
            4: "posted",
            5: "delivered",
            6: "rejected",
        }
        return values.get(value, default)

    @staticmethod
    def get_code_reverse(value, default="-"):
        values = {
            "created": 0,
            "confirmed": 1,
            "processed": 2,
            "packaged": 3,
            "posted": 4,
            "delivered": 5,
            "rejected": 6,
        }
        return values.get(value, default)

    @staticmethod
    def get_display(value, default="-"):
        values = {
            "created": "ایجاد شده",
            "confirmed": "تائید شده",
            "processed": "پردازش شده",
            "packaged": "بسته بندی شده",
            "posted": "ارسال شده",
            "delivered": "تحویل شده",
            "rejected": "رد شده",
        }
        return values.get(value, default)
