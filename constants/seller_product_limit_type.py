from django.db.models import TextChoices


class SellerProductLimitType(TextChoices):
    GLOBAL = "global", "Global"
    GLOBAL_CATEGORY = "global_category", "Global Category"
    CITY = "city", "City"
    CITY_CATEGORY = "city_category", "City Category"
    USER_GROUP = "user_group", "User Group"
    USER_ORGANIZATION = "user_organization", "User Organization"
    USER_CATEGORY = "user_category", "User Category"
    USER_PRODUCT = "user_product", "User Product"
    USER_BRAND = "user_brand", "User Brand"
