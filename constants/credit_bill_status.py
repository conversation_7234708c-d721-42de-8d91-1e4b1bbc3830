from core.templates.utils import colored_text
from django.db.models import TextChoices


class CreditBillStatus(TextChoices):
    IN_PROGRESS = "in_progress", "InProgress"
    FULFILLED = "fulfilled", "Fulfilled"
    FAILED = "failed", "Failed"

    @staticmethod
    def get_colored(value):
        values = {
            "in_progress": ("InProgress", "#f5bb00"),
            "fulfilled": ("Fulfilled", "#53a70d"),
            "failed": ("Failed", "#bc0e0e")
        }
        return colored_text(*values.get(value, (value, "#bc0e0e")))
