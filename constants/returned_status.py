from core.templates.utils import colored_text
from django.db.models import TextChoices


class ReturnedStatus(TextChoices):
    PENDING = "pending", "Pending"
    APPROVED = "approved", "Approved"
    REJECTED = "rejected", "Rejected"
    PAID = "paid", "Paid"

    REFUND_PENDING = "refund_pending", "Refund_Pending"
    REFUND_FAILED = "refund_failed", "Refund_Failed"
    REFUND_LOCKED = "refund_locked", "Refund_Locked"
    REFUND_CANCELED = "refund_canceled", "Refund_Canceled"

    @staticmethod
    def get_colored(value):
        values = {
            "pending": ("Pending", "grey"),
            "approved": ("Approved", "#f5bb00"),
            "rejected": ("Rejected", "#bc0e0e"),
            "paid": ("Paid", "#53a70d"),

            "refund_pending": ("Refund_Pending", "#f5bb00"),
            "refund_failed": ("Refund_Failed", "#bc0e0e"),
            "refund_locked": ("Refund_Locked", "#900C3F"),
            "refund_canceled": ("Refund_Canceled", "#bc0e0e"),
        }
        return colored_text(*values.get(value, (value, "#bc0e0e")))
