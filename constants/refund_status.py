from core.templates.utils import colored_text
from django.db.models import TextChoices


class RefundStatus(TextChoices):
    PENDING = "pending", "Pending"
    FAILED = "failed", "Failed"
    FULFILLED = "fulfilled", "Fulfilled"
    CANCELED = "canceled", "Canceled"

    @staticmethod
    def get_colored(value):
        values = {
            "pending": ("Pending", "#f5bb00"),
            "failed": ("Failed", "#bc0e0e"),
            "fulfilled": ("Fulfilled", "#53a70d"),
            "canceled": ("Canceled", "#bc0e0e"),
        }
        return colored_text(*values.get(value, (value, "#bc0e0e")))
