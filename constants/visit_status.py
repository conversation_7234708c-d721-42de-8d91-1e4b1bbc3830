from core.templates.utils import colored_text
from django.db.models import TextChoices


class VisitStatus(TextChoices):
    VISIT = "visit", "Visit"
    FAST = "fast", "Fast"
    PEND = "pend", "Pending"
    FOCUS = "focus", "Focus"

    @staticmethod
    def get_colored(value):
        values = {
            "visit": ("Visit", "grey"),
            "fast": ("Fast", "#bc0e0e"),
            "pend": ("Pend", "#f5bb00"),
            "focus": ("Focus", "#53a70d"),
        }
        return colored_text(*values.get(value, (value, "grey")))
