from django.db.models import TextChoices


class CreditType(TextChoices):
    ADMIN = "admin", "Admin"

    USER_DISCOUNT = "user_discount", "User Discount"
    USER_PAYMENT = "user_payment", "User Payment"
    RETURNED = "returned", "Returned"
    CASH_OUT = "cash_out", "Cash Out"

    SERVICE_CENTER_BILL = "service_center_bill", "Service Center Bill"
    SERVICE_CENTER_GIFT = "service_center_gift", "Service Center Gift"
    SERVICE_CENTER_NEW_USER = "service_center_new_user", "Service Center New User"
    SERVICE_CENTER_SERIAL_NUMBER = "service_center_serial_number", "Service Center Serial Number"

    AGENT_BILL = "agent_bill", "Agent Bill"
    AGENT_CHECKOUT = "agent_checkout", "Agent Checkout"
    AGENT_NEW_USER = "agent_new_user", "Agent New User"
    AGENT_SERVICE_CENTER_FACTOR = "agent_service_center_factor", "Agent Service Center Factor"

    AGENT_SERVICE_CENTER_FACTOR_FIRST_HEAD = (
        "agent_service_center_factor_first_head",
        "Agent Service Center Factor First Head",
    )

    AGENT_SERVICE_CENTER_FACTOR_SECOND_HEAD = (
        "agent_service_center_factor_second_head",
        "Agent Service Center Factor Second Head",
    )
    AGENT_SERVICE_CENTER_FACTOR_THIRD_HEAD = (
        "agent_service_center_factor_third_head",
        "Agent Service Center Factor Third Head",
    )
