from core import admin


class QuickBookProductItemAdmin(admin.AbstractBaseAdmin):
    list_display = ("id", "product", "item", "quantity")
    raw_id_fields = ("product", "item")
    list_editable = ()
    search_fields = (
        "product__quickbooks_id", "item__quickbooks_id", "item__sku", "product__sku",
        "product_id", "item_id"

    )

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related("product__site", "item__site")
