from core import admin
from translation.utils.admin_form import AdminTranslationInline
from ..models import CategoryTranslation


class CategoryTranslationInline(AdminTranslationInline):
    model = CategoryTranslation




class CategoryAdmin(admin.AbstractBaseAdmin):
    list_display = ('id', 'name', 'is_active', 'priority', 'parent', 'site')
    inlines = [CategoryTranslationInline]
    raw_id_fields = ("parent",)
    list_filter = ("site", "is_active", "created_at", "updated_at")

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related("parent__site", "site").prefetch_related('translations')
