from core import serializers
from ..models import Product


class ProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = Product
        fields = '__all__'


class ProductDashboardSerializer(serializers.ModelSerializer):
    category = serializers.SerializerMethodField()
    sales_price = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = '__all__'

    def get_category(self, obj):
        if obj.category:
            return obj.category.name

    def get_sales_price(self, obj):
        if obj.sales_price:
            return obj.sales_price
        return 0
