# from faker import Faker
# from django.urls import reverse
# from rest_framework.test import APITestCase
# from rest_framework import status
# from django.utils.translation import gettext as _
#
# from products.tests.default_test_data import create_duplicated_product_data
# from quick_book.models import Product
# from quick_book.serializers.product import ProductSerializer
#
#
# class ProductApiTest(APITestCase):
#
#     def setUp(self):
#         faker = Faker()
#
#         self.staff_user, self.user, self.product, self.product_hig = create_duplicated_product_data()
#         self._product = Product.objects.create(name=faker.name(), quickbooks_id=faker.unique.name(),
#                                                site=self.product_hig.site,
#                                                category=self.product_hig.category)
#         self.list_url = reverse('quick_book:dashboard_v1:products')
#
#     def test_get_single_product_no_auth(self):
#         url = reverse('quick_book:dashboard_v1:product', args=[self._product.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_get_single_product_no_perm(self):
#         self.client.force_authenticate(user=self.user)
#         url = reverse('quick_book:dashboard_v1:product', args=[self._product.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_get_single_product_wrong_id(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('quick_book:dashboard_v1:product', args=[555])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
#         self.assertEqual(response.json()["message"], _("product not found"))
#
#     def test_get_single_product_wrong_site(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('quick_book:dashboard_v1:product', args=[self.product.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
#         self.assertEqual(response.json()["message"], _("product not found"))
#
#     def test_get_single_category(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('quick_book:dashboard_v1:product', args=[self._product.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.json()["data"], ProductSerializer(self._product).data)
#
#     def test_unauthenticated_user_access(self):
#         response = self.client.get(self.list_url)
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_no_perm_user_list(self):
#         self.client.force_authenticate(user=self.user)
#         response = self.client.get(self.list_url)
#
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_get_all_products_with_pagination(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 1, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIn('data', response.json())
#         self.assertEqual(len(response.json()['data']), 1)
#
#     def test_get_all_products_with_pagination_check_next_page(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 1, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIsNotNone(response.json()["pagination"]["next"])
#         self.assertIsNotNone(response.json()["pagination"]["next_url"])
#
#     def test_get_all_products_with_pagination_check_previous_page(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 2, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIsNotNone(response.json()["pagination"]["previous"])
#         self.assertIsNotNone(response.json()["pagination"]["previous_url"])
#
#     def test_get_all_products_with_default_pagination(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(len(response.json()['data']), 2)
#         self.assertEqual(response.json()['data'],
#                          ProductSerializer(Product.objects.filter(site__name="hig"), many=True).data)
#
#     def test_get_all_products_with_category_filter(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, data={"categories": str(self.product_hig.category.id)})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(len(response.json()['data']), 2)
#
#     def test_get_all_products_with_wrong_category_filter(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, data={"categories": "66,67"})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(response.json()['data'], [])
