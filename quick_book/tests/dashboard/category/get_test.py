# from faker import Faker
# from django.urls import reverse
# from rest_framework.test import APITestCase
# from rest_framework import status
# from django.utils.translation import gettext as _
#
# from products.tests.default_test_data import create_duplicated_product_data
# from quick_book.models import Category, CategoryTranslation
#
#
# class CategoryApiTest(APITestCase):
#
#     def setUp(self):
#         faker = Faker()
#
#         self.staff_user, self.user, self.product, self.product_hig = create_duplicated_product_data()
#         Category.objects.create(name=faker.name(), quickbooks_id=faker.unique.name(), site=self.product_hig.site)
#         self.category = self.product_hig.category
#         self.list_url = reverse('quick_book:dashboard_v1:categories')
#
#     def test_get_single_category_no_auth(self):
#         url = reverse('quick_book:dashboard_v1:category', args=[self.category.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_get_single_category_no_perm(self):
#         self.client.force_authenticate(user=self.user)
#         url = reverse('quick_book:dashboard_v1:category', args=[self.category.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_get_single_category_wrong_id(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('quick_book:dashboard_v1:category', args=[555])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
#         self.assertEqual(response.json()["message"], _("category not found"))
#
#     def test_get_single_category_wrong_site(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('quick_book:dashboard_v1:category', args=[self.product.category.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
#         self.assertEqual(response.json()["message"], _("category not found"))
#
#     def test_get_single_category(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('quick_book:dashboard_v1:category', args=[self.category.id])
#         self.client.credentials(HTTP_ACCEPT_LANGUAGE='en')
#         response = self.client.get(url)
#
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         expected_data = {"id": self.category.id, "image": self.category.image, "name": self.category.name}
#         self.assertEqual(response.json()["data"], expected_data)
#
#     def test_unauthenticated_user_access(self):
#         response = self.client.get(self.list_url)
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_no_perm_user_list(self):
#         self.client.force_authenticate(user=self.user)
#         response = self.client.get(self.list_url)
#
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_get_all_categories_with_pagination(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 1, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIn('data', response.json())
#         self.assertEqual(len(response.json()['data']), 1)
#
#     def test_get_all_categories_with_pagination_check_next_page(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 1, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIsNotNone(response.json()["pagination"]["next"])
#         self.assertIsNotNone(response.json()["pagination"]["next_url"])
#
#     def test_get_all_categories_with_pagination_check_previous_page(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 2, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIsNotNone(response.json()["pagination"]["previous"])
#         self.assertIsNotNone(response.json()["pagination"]["previous_url"])
#
#     def test_get_all_categories_with_default_pagination(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(len(response.json()['data']), 2)
#
#
#     def test_get_all_categories_with_translation(self):
#         self.client.force_authenticate(user=self.staff_user)
#         CategoryTranslation.objects.create(
#             category=self.category,
#             lang_code="ar",
#             name="الترجمه"
#         )
#         url = reverse('quick_book:dashboard_v1:category', args=[self.category.id])
#         self.client.credentials(HTTP_ACCEPT_LANGUAGE='ar')
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         expected_data = {"id": self.category.id, "image": self.category.image, "name": "الترجمه"}
#         self.assertEqual(response.json()["data"], expected_data)
