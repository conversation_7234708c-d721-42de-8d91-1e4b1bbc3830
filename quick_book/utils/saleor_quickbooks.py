from saleor.models import Product as SaleorProduct
from quick_book.utils import QuickBook

import logging

logger = logging.getLogger("quick_book")


def sync_products():
    saleor_products = SaleorProduct.objects.filter(quickbooks_id__isnull=True)
    for saleor_product in saleor_products:

        try:
            product = saleor_product
            category = product.category
            if category and not category.quickbooks_id:
                try:
                    object_type = "category"
                    quick_books_id = QuickBook.create_item(object_type=object_type, saleor_object=category)
                    category.quickbooks_id = quick_books_id
                    category.save()
                except Exception as e:
                    logger.error("Saleor-QuickBooks| create-item: category-id: {} error: {}".format(saleor_product.id, str(e)))
                    continue
            object_type = "product"
            quick_books_id = QuickBook.create_item(object_type=object_type, saleor_object=product)
            product.quickbooks_id = quick_books_id
            product.save()
        except Exception as e:
            logger.error("Saleor-QuickBooks | create-item: product-id: {} error: {}".format(saleor_product.id, str(e)))
