from django.core.management.base import BaseCommand

from quick_book.models import Category, Product
from quick_book.utils import bills, items


class Command(BaseCommand):
    def handle(self, *args, **options):
        self.handle_products()

    def handle_products(self):
        object_ids = ["55", "50", "48", "56", "8", "25", "26", "19", "20", "24", "23", "21", "22", "51", "18", "60",
                      "57", "58", "59"]
        items.sync_item(object_ids, "HIG")
