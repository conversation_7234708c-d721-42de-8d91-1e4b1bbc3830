import logging

from django.db.models import Prefetch
from django.utils.translation import gettext
from django.db import connection
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import <PERSON><PERSON><PERSON><PERSON><PERSON>uthentication
from core.http import JsonResponse
from core.http import paginate
from quick_book.models import Category, CategoryTranslation
from translation.utils.translate import get_object_translation

logger = logging.getLogger("dashboard")


class CategoriesApi(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request, category_id=None):
        if category_id:
            self.exception_data = {"message": gettext("category not found"), "status": 404}
            category_obj = Category.objects.get(pk=category_id, site__name="hig", is_active=True)
            category_resp = {
                "value": category_obj.id,
                "label": category_obj.name,
            }
            category_resp.update(get_object_translation(category_obj, request.lang))
            return JsonResponse(data=category_resp)

        self.exception_data = {"message": gettext("Error occurred while showing categories"), "status": 400}

        categories = Category.objects.exclude(name="service_fee").filter(
            site__name="hig",
            is_active=True,
        ).order_by("-id").prefetch_related(
            Prefetch('translations', queryset=CategoryTranslation.objects.filter(lang_code=request.lang))
        )
        response_data = []
        for category in categories:
            # Handle translations in a single lookup
            translation = next((t for t in category.translations.all() if t.lang_code == request.lang), None)

            category_resp = {
                "value": category.id,
                "label": translation.name if translation else category.name,
            }
            response_data.append(category_resp)
        page, limit = request.query_params.get('page'), request.query_params.get("limit")
        if page and limit:
            return JsonResponse(
                **paginate(
                    data=response_data,
                    page=page,
                    limit=limit,
                )
            )
        return JsonResponse(
            data=response_data
        )
