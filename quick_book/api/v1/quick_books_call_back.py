import logging
from django.conf import settings
from django.core.cache import cache


from core.http import JsonResponse
from rest_framework.views import APIView
from intuitlib.client import AuthClient

from quick_book.utils import QuickBook

logger = logging.getLogger("quick_book")


class QuickBooksCallBack(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        logger.info("QIUCKBOOKS-REDIRECT-API| redirect called successfully.")
        logger.info("QIUCKBOOKS-REDIRECT-API| request-state: {}, auth_code: {} ".format(
            request.GET.get('state'),
            request.GET.get('code')
        )
        )
        logger.info("QIUCKBOOKS-REDIRECT-API| auth-client Done.")


        # state = request.GET.get('state')
        # if state != request.session['state']:  # CSRF protection
        #     return JsonResponse(status=401, message=str("CSRF protection"))

        auth_code = request.GET.get('code')
        realm_id = request.GET.get('realmId')
        _site = request.GET.get("site")
        cache.set("{}_quickbooks_realm_id".format(_site), realm_id, 5 * 24 * 60 * 60)
        qb = QuickBook(site=_site)
        auth_client = AuthClient(
            getattr(settings, "{}_QUICKBOOKS_CLIENT_ID".format(str(_site).upper())),
            getattr(settings, "{}_QUICKBOOKS_CLIENT_SECRET".format(str(_site).upper())),
            getattr(settings, "{}_QUICKBOOKS_REDIRECT_URI".format(str(_site).upper())),
            environment=getattr(settings, "{}_QUICKBOOKS_ENVIRONMENT".format(str(_site).upper()))
        )
        logger.info("QIUCKBOOKS-REDIRECT-API| auth-client Done.")
        cash_key_access = getattr(qb, "CASH_KEY_ACCESS")
        cash_key_refresh = getattr(qb, "CASH_KEY_REFRESH")

        try:
            logger.info("QIUCKBOOKS-REDIRECT-API| auth-get_bearer_token in process.")
            auth_client.get_bearer_token(auth_code, realm_id=realm_id)
            logger.info("QIUCKBOOKS-REDIRECT-API| auth-get_bearer_token Done.")

            cache.set(cash_key_access, auth_client.access_token, 1 * 60 * 60)
            cache.set(cash_key_refresh, auth_client.refresh_token, 5 * 24 * 60 * 60)
            cache.set("{}_token_added".format(_site), True)

        except Exception as e:
            logger.error("QUICK-BOOK-AUTH-TOKEN| ERROR: {}".format(str(e)))
            return JsonResponse(status=401, message=str(e))
        return JsonResponse(status=200)
