import logging

from rest_framework.views import APIView
from core.http import JsonResponse
from quick_book.webhooks import QuickBooksWebhookHandler

logger = logging.getLogger("quick_book")


class QuickBookWebhookApi(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request):
        logger.info("[QuickBooksWebhookApi] data={}, headers={}".format(request.data, request.headers))
        self.exception_data = {"status": 400, "message": "Invalid webhook data"}
        site = request.GET.get("site")
        webhook_handler = QuickBooksWebhookHandler(request.data, request.headers, site)
        webhook_handler.handle()
        return JsonResponse(status=200, message="done!")
