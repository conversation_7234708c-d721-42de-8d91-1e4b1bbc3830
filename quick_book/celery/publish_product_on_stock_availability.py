"""
Celery Task: Publish Product on Stock Availability

This task synchronizes the publishing status of products in Saleor with their
stock availability. It checks both individual products and bundled products.

Description:
    - Fetches products associated with the 'HiGaraj' site.
    - Evaluates stock availability for individual (non-bundled) products.
    - Updates publishing status in Saleor for non-bundled products.
    - Evaluates stock availability for bundled products based on the availability of their components.
    - Updates publishing status in Saleor for bundled products.

Side Effects:
    - Makes API calls to Saleor to update product publishing statuses.sa
"""

import logging

from django.db.models import Sum

from products.models import ProductServiceCenterStock
from products.utils.stock import check_sc_stock
from quick_book.models import Product
from saleor.utils import Saleor
from service_center.models import ServiceCenter
from settings.celery_app import celery_app

logger = logging.getLogger("quick_book")


@celery_app.task(name="publish_product_on_stock_availability", autoregister=True)
def publish_product_on_stock_availability(product_ids=None):
    service_center_ids = list(ServiceCenter.objects.filter(
        is_active=True,
    ).values_list('id', flat=True))

    product_queryset = Product.objects.filter(
        site__name='hig'
    )
    if product_ids is not None:
        product_queryset = product_queryset.filter(
            id__in=product_ids,
        )

    not_bundle_products = product_queryset.filter(
        is_bundle=False,
    )

    # Use dict in order to make the fetching time complexity to O(1)
    product_items_dict = dict()
    for product in not_bundle_products:
        product_items_dict[product.id] = product

    service_center_quantity_per_product_item_dict = dict()
    for product in not_bundle_products:
        service_center_quantity_per_product_item_dict[product.id] = ProductServiceCenterStock.objects.filter(
            product=product,
        ).aggregate(sum=Sum('stock'))['sum']

    for product in not_bundle_products:
        try:
            check_sc_stock(product=product, service_center_ids=service_center_ids, quantity=1)
            is_available = True
        except Exception as e:
            is_available = False

        saleor = Saleor(product.site.name)
        saleor.product_change_published_status(product.saleor_id, is_available)
        print(f"Product {product.name} is available: {is_available}")

    bundle_products = product_queryset.filter(
        is_bundle=True,
    ).prefetch_related('items')
    for bundle_product in bundle_products:
        is_available = True
        product_items = bundle_product.items.select_related('product')
        for product_item in product_items:
            child_product = product_item.item
            try:
                check_sc_stock(
                    product=child_product,
                    service_center_ids=service_center_ids,
                    quantity=product_item.quantity,
                )
            except Exception as e:
                is_available = False
                break

        saleor = Saleor(bundle_product.site.name)
        saleor.product_change_published_status(bundle_product.saleor_id, is_available)
        print(f"Product {bundle_product.name} is available: {is_available}")
