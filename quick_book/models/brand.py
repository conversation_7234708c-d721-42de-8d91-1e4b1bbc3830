from django.contrib.contenttypes.fields import GenericRelation

from files.models import Image

from core import models


class Brand(models.AbstractBaseModel):
    name = models.CharField(max_length=255, db_index=True)
    parent = models.ForeignKey(
        "self", null=True, blank=True, related_name="children", on_delete=models.CASCADE
    )
    images = GenericRelation(Image, related_query_name="brands")

    def __str__(self):
        return self.name
