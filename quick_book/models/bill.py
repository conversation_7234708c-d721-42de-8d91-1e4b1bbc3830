from core import models


class Bill(models.AbstractBaseModel):
    quickbooks_id = models.Char<PERSON>ield(max_length=50)
    is_synced = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    site = models.ForeignKey(models.Site, related_name="bills", on_delete=models.PROTECT, null=True, blank=True)

    def __str__(self):
        return "{}| {}".format(self.quickbooks_id, self.is_synced)

    def save(self, *args, **kwargs):
        if self.items.all().count() == self.items.filter(is_synced=True).count() != 0:
            self.is_synced = True
        super().save(*args, **kwargs)


class BillItem(models.AbstractBaseModel):
    bill = models.ForeignKey("quick_book.Bill", on_delete=models.PROTECT, related_name="items")
    quickbooks_id = models.CharField(max_length=50)
    quantity = models.PositiveIntegerField(default=0)
    product = models.ForeignKey("quick_book.Product", on_delete=models.PROTECT)
    is_synced = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    def __str__(self):
        return "{}| {}".format(self.quickbooks_id, self.is_synced)
