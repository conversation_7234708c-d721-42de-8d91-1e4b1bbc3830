from django.db.models import F, Case, When, Value, Q
from django.db.models.functions import Coalesce

from core import models
from translation.models import Translation


class Category(models.AbstractBaseModel):
    name = models.CharField(max_length=100)
    quickbooks_id = models.CharField(max_length=50)
    saleor_id = models.CharField(max_length=100, null=True, blank=True)
    parent = models.ForeignKey(
        "self", null=True, blank=True, related_name="children", on_delete=models.CASCADE
    )
    image = models.ImageField(upload_to="category/", null=True, blank=True)
    site = models.ForeignKey(models.Site, related_name="quick_book_categories", on_delete=models.PROTECT, null=True,
                             blank=True)

    def __str__(self):
        return "{}|{}|{}".format(self.name, self.quickbooks_id, self.site)


class CategoryTranslation(Translation):
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name="translations")
    name = models.Char<PERSON><PERSON>(max_length=100, null=True, blank=True)

    def __str__(self):
        return "{}|{}".format(self.name, self.category_id)

    class Meta:
        db_table = "matrix_quick_book_category_translation"
        unique_together = (("lang_code", "category"),)

    @property
    def get_translation_fields(self):
        return {
            "name": self.name if self.name else self.category.name,
        }
