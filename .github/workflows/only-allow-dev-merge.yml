name: Enforce Dev to Main Merges Only

on:
  pull_request:
    branches: [ main ]

jobs:
  check-branch:
    runs-on: ubuntu-latest
    steps:
      - name: Check if PR is from dev branch
        run: |
          echo "Source branch: ${{ github.head_ref }}"
          if [ "${{ github.head_ref }}" != "dev" ]; then
            echo "❌ Only PRs from 'dev' branch can be merged into 'main'."
            exit 1
          fi