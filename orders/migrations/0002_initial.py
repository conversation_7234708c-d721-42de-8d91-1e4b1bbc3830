# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('locations', '0001_initial'),
        ('sites', '0002_alter_domain_unique'),
        ('service_center', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('orders', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='orderline',
            name='service_center',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='order_lines', to='service_center.servicecenter'),
        ),
        migrations.AddField(
            model_name='order',
            name='city',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='locations.city'),
        ),
        migrations.AddField(
            model_name='order',
            name='site',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='sites.site'),
        ),
        migrations.AddField(
            model_name='order',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to=settings.AUTH_USER_MODEL),
        ),
    ]
