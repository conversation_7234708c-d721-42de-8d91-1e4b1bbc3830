# Generated by Django 3.2.10 on 2023-09-17 08:58

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('locations', '0001_initial'),
        ('sellers', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('token', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('saleor_order_id', models.PositiveIntegerField()),
                ('description', models.TextField(blank=True)),
                ('price', models.PositiveIntegerField(default=0, editable=False)),
                ('amount', models.PositiveIntegerField(default=0, editable=False)),
                ('service_amount', models.PositiveIntegerField(default=0, editable=False)),
                ('delivery_amount', models.PositiveIntegerField(default=0, editable=False)),
                ('discount_amount', models.PositiveIntegerField(default=0, editable=False)),
                ('gift_card_amount', models.PositiveIntegerField(default=0, editable=False)),
                ('metadata', models.JSONField(blank=True, default=dict, null=True)),
            ],
            options={
                'db_table': 'matrix_orders_order',
            },
        ),
        migrations.CreateModel(
            name='OrderLine',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product_id', models.PositiveIntegerField()),
                ('saleor_order_line_id', models.PositiveIntegerField()),
                ('quantity', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('type', models.CharField(choices=[('service_center_service', 'Service Center Service'), ('service_center_delivery', 'Service Center Delivery'), ('location_service', 'Location Service'), ('location_delivery', 'Location Delivery'), ('post_delivery', 'Post Delivery'), ('snapp_box_delivery', 'Snapp Box Delivery'), ('no_service_method', 'No Service Method')], default='service_center_service', max_length=30)),
                ('delivery_date', models.DateTimeField(blank=True, null=True)),
                ('delivery_shift', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('delivery_status', models.CharField(choices=[('created', 'Created'), ('confirmed', 'Confirmed'), ('processed', 'Processed'), ('packaged', 'Packaged'), ('posted', 'Posted'), ('delivered', 'Delivered'), ('rejected', 'Rejected')], default='created', max_length=30)),
                ('delivery_reference', models.CharField(blank=True, max_length=255, null=True)),
                ('extra_charge', models.PositiveIntegerField(default=0)),
                ('service_amount', models.PositiveIntegerField(default=0)),
                ('delivery_amount', models.PositiveIntegerField(default=0)),
                ('price', models.PositiveIntegerField()),
                ('discount_amount', models.PositiveIntegerField(default=0)),
                ('gift_card_amount', models.PositiveIntegerField(default=0)),
                ('amount', models.PositiveIntegerField(default=0, editable=False)),
                ('used', models.PositiveIntegerField(default=0, editable=False)),
                ('locked', models.PositiveIntegerField(default=0, editable=False)),
                ('returned', models.PositiveIntegerField(default=0, editable=False)),
                ('expired', models.PositiveIntegerField(default=0, editable=False)),
                ('address', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='locations.address')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_lines', to='orders.order')),
                ('seller', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='order_lines', to='sellers.seller')),
            ],
            options={
                'db_table': 'matrix_orders_order_line',
            },
        ),
    ]
