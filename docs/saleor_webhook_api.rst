==============
Saleor Webhook API
==============

The Saleor Webhook API allows you to receive and process webhook events from Saleor.

POST saleor/api/v1/webhooks/
------------------------

**Authentication:** None required

**Permissions:** None required

Endpoint for receiving Saleor webhook events. When a POST request is made to this endpoint, the API processes the webhook event data.

Parameters
^^^^^^^^^^^^^

- `data` (JSON): The JSON data of the webhook event.
- `headers` (HTTP Headers): The HTTP headers of the incoming request.

Response
^^^^^^^^^^^^^

- Status Code 200: The webhook event has been successfully processed.
- Status Code 400: If the request or data is invalid, a response with an error message is returned.

Example
^^^^^^^^^^^^^

A sample usage of the Saleor Webhook API:

```http

POST saleor/api/v1/webhooks/ HTTP/1.1

Host: your-api-host.com

Content-Type: application/json

Content-Length: 123

Saleor-Event: product_created

{
  "event_type": "product_created",

  "data": {
    "product": {
            "id": 123,
            "name": "Sample Product",
            "slug": "sample-product",

            "category": {
                "id": 456,
                "name": "Sample Category"
            },

            "productType": {
                "id": 789,
                "name": "Sample Product Type"
            }
        }
  }
}