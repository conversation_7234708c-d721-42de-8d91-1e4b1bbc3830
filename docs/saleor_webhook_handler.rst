=============
Saleor Webhook Handler
=============

The Saleor Webhook Handler is a class used to handle incoming webhook events from Saleor. It processes different types of webhook events and updates the corresponding data in the system.

Saleor Webhook Handler Functions
---------------------------

The `SaleorWebhookHandler` class provides several functions to handle different types of webhook events:

- `_handle_product_created()`: Handles the creation of a new product in the system.
- `_handle_product_updated()`: Handles updates to an existing product.
- `_handle_product_deleted()`: Handles the deletion of a product.
- `_handle_category_created()`: Handles the creation of a new category.
- `_handle_category_updated()`: Handles updates to an existing category.
- `_handle_category_deleted()`: Handles the deletion of a category.

Initialization
---------------------------

When an instance of the `SaleorWebhookHandler` class is created, it takes two parameters:

- `data` (JSON): The JSON data of the webhook event.
- `headers` (HTTP Headers): The HTTP headers of the incoming request, including the Saleor event type.

Handling Webhook Events
---------------------------

The `handle()` method is called to process the incoming webhook event. It dynamically calls the appropriate handler function based on the event type provided in the headers.

Example
---------------------------

Here's an example of how the Saleor Webhook Handler is used:

```python
data = {
    "event_type": "product_created",
    "data": {
        "product": {
            "id": 123,
            "name": "Sample Product",
            "slug": "sample-product",
            "category": {
                "id": 456,
                "name": "Sample Category"
            },
            "productType": {
                "id": 789,
                "name": "Sample Product Type"
            }
        }
    }
}

headers = {
    "Saleor-Event": "product_created"
}

webhook_handler = SaleorWebhookHandler(data, headers)
webhook_handler.handle()
