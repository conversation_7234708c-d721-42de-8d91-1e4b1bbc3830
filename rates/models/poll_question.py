from constants import PollQuestionType
from core.models import AbstractBaseModel
from django.db import models


class PollQuestion(AbstractBaseModel):
    name = models.CharField(max_length=250)
    type = models.CharField(max_length=250, choices=PollQuestionType.choices, default=PollQuestionType.BILL)

    class Meta:
        db_table = "matrix_rates_poll_question"

    def __str__(self):
        return "[{}] {} - {}".format(self.id, self.name, self.type)
