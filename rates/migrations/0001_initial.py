# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PollQuestion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=250)),
                ('type', models.CharField(choices=[('bill', 'Bill'), ('agent', 'Agent'), ('service_center_app', 'Service Center App'), ('service_center_site', 'Service Center Site'), ('returned', 'Returned')], default='bill', max_length=250)),
            ],
            options={
                'db_table': 'matrix_rates_poll_question',
            },
        ),
        migrations.CreateModel(
            name='Rate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('value', models.PositiveIntegerField(default=1)),
            ],
            options={
                'db_table': 'matrix_rates_rate',
            },
        ),
    ]
