# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('service_center', '0001_initial'),
        ('rates', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='rate',
            name='bill',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='rate', to='service_center.bill'),
        ),
        migrations.AddField(
            model_name='rate',
            name='poll_question',
            field=models.ManyToManyField(blank=True, related_name='rates', to='rates.PollQuestion'),
        ),
    ]
