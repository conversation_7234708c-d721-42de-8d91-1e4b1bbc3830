import logging

from core.http import JsonResponse
from rates.models import Rate
from rest_framework.views import APIView
from service_center.models import Bill

logger = logging.getLogger("rate")


class RateAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def post(self, request):
        rate = Rate.objects.create(bill=Bill.objects.get(token=request.data["token"]), value=request.data["rate"])
        rate.poll_question.set(request.data["items"])
        return JsonResponse()
