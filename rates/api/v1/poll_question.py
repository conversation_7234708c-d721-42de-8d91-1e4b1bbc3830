import logging

from core.http import JsonResponse
from core.models import Q
from core.permissions import IsActiveUser
from rates.models import PollQuestion
from rates.serializers import PollQuestionAgentSerializer
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from orders.models import OrderLine
from sellers.models import SellerProduct

logger = logging.getLogger("rates")


class PollQuestionAPI(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsActiveUser)

    def get(self, request):
        queryset = PollQuestion.objects.filter(is_active=True)
        if request.query_params.get("type"):
            queryset = queryset.filter(type=request.query_params["type"])
        if request.query_params.get("category_id"):
            queryset = queryset.filter(
                Q(categories__isnull=True)
                | Q(categories__id=int(request.query_params["category_id"]))
            )
        if request.query_params.get("order_line_id"):
            ol = OrderLine.objects.get(id=request.query_params.get("order_line_id"))
            parent_city = SubCity.objects.get(related_cities=ol.address.region.city).parent_city
            try:
                seller_product = SellerProduct.objects.get(
                    site=request.site,
                    product=ol.product,
                    city=parent_city,
                    seller=ol.seller,
                )
                queryset = queryset.filter(seller_products__in=[seller_product])
            except Exception as e:
                return JsonResponse(status=404, message="لطفا با پشتیبانی تماس بگیرید.")
        return JsonResponse(
            data=PollQuestionAgentSerializer(
                queryset,
                many=True,
                exclude=("created_at", "updated_at", "priority", "is_active"),
            ).data
        )
