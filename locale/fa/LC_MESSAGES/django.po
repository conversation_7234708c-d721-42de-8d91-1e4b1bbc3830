# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-20 20:10+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: agent/api/v1/agent_colleagues.py:49
msgid "لطفا اطلاعات را کامل وارد کنید."
msgstr ""

#: agent/api/v1/agent_colleagues.py:51 agent/api/v1/login.py:32
msgid "شماره موبایل وارد شده اشتباه است."
msgstr ""

#: agent/api/v1/agent_colleagues.py:55
msgid "شماره تلفن وارد شده اشتباه است."
msgstr ""

#: agent/api/v1/agent_colleagues.py:59
msgid "کد ملی وارد شده اشتباه است."
msgstr ""

#: agent/api/v1/agent_colleagues.py:64
msgid "شماره موبایل قبلا به عنوان همکار اضافه شده است."
msgstr ""

#: agent/api/v1/agent_colleagues.py:89
msgid "کاربر با موفقیت اضافه شد. پیامک دعوت ارسال شد."
msgstr ""

#: agent/api/v1/agent_visit.py:40
msgid "برای ثبت ویزیت باید در محدوده مرکز قرار بگیرید."
msgstr ""

#: agent/api/v1/agent_visit.py:51
msgid ""
"مرکز به حداکثر تعداد مجاز ویزیت در ماه رسیده است. امکان ثبت ویزیت تا ماه بعد "
"وجود ندارد."
msgstr ""

#: agent/api/v1/agent_visit.py:70
msgid "برای این مرکز ویزیت فعال وجود دارد. لطفا با پشتیبانی تماس بگیرید."
msgstr ""

#: agent/api/v1/agent_visit.py:73
msgid "شما هم اکنون ویزیت فعال دارید. لطفا ویزیت قبلی خود را تمام کنید."
msgstr ""

#: agent/api/v1/agent_visit.py:84
msgid "ویزیت شما آغاز شد."
msgstr ""

#: agent/api/v1/agent_visit.py:93
msgid "ویزیت فعالی یافت نشد. لطفا با پشتیبانی تماس بگیرید."
msgstr ""

#: agent/api/v1/agent_visit.py:97
msgid "یادداشت و سوالات با موفقیت ثبت شد."
msgstr ""

#: agent/api/v1/agent_visit.py:100
msgid "پایان ویزیت با موفقیت ثبت شد."
msgstr ""

#: agent/api/v1/focus.py:107
msgid "عدم دسترسی برای ثبت فکوس مرکز ارائه خدمات."
msgstr ""

#: agent/api/v1/focus.py:134
msgid "تمام اطلاعات مورد نیاز باید پر شده باشد."
msgstr ""

#: agent/api/v1/focus.py:145
msgid "برای ثبت فکوس باید در محدوده مرکز قرار بگیرید."
msgstr ""

#: agent/api/v1/focus.py:209
msgid "عملیات فکوس مرکز ارائه خدمات با موفقیت انجام شد."
msgstr ""

#: agent/api/v1/login.py:40
msgid "مشاوری با شماره وارد شده یافت نشد."
msgstr ""

#: agent/api/v1/login.py:59
msgid "ورود شما با موفقیت انجام شد."
msgstr ""

#: agent/api/v1/login.py:67
msgid "کد با موفقیت ارسال شد."
msgstr ""

#: agent/api/v1/login.py:69
msgid "خطا در ارسال کد تائید، لطفا مجددا تلاش کنید."
msgstr ""

#: agent/api/v1/region_summary.py:50
msgid "عدم دسترسی برای ثبت منطقه جدید."
msgstr ""

#: agent/api/v1/region_summary.py:56
msgid "درخواست شما انجام شد"
msgstr ""

#: agent/api/v1/service_center.py:43 agent/api/v1/service_center_location.py:22
msgid "مرکز ارائه خدمات یافت نشد."
msgstr ""

#: agent/api/v1/service_center.py:141
msgid "لطفا اسم مرکز را وارد کنید."
msgstr ""

#: agent/api/v1/service_center.py:144
msgid "لطفا شماره موبایل مسئول مرکز را وارد کنید."
msgstr ""

#: agent/api/v1/service_center.py:147
msgid "شماره موبایل وارد شده صحیح نیست."
msgstr ""

#: agent/api/v1/service_center.py:150
msgid "لطفا نام مسئول مرکز را وارد کنید."
msgstr ""

#: agent/api/v1/service_center.py:153
msgid "لطفا نام خانوادگی مسئول مرکز را وارد کنید."
msgstr ""

#: agent/api/v1/service_center.py:156
msgid "لطفا نوع مرکز را وارد کنید."
msgstr ""

#: agent/api/v1/service_center.py:159
msgid "لطفا شماره تلفن مرکز را وارد کنید."
msgstr ""

#: agent/api/v1/service_center.py:162
msgid "شماره تلفن وارد شده صحیح نیست."
msgstr ""

#: agent/api/v1/service_center.py:165
msgid "لطفا منطقه مرکز را انتخاب کنید."
msgstr ""

#: agent/api/v1/service_center.py:168
msgid "لطفا کد پستی مرکز را وارد کنید."
msgstr ""

#: agent/api/v1/service_center.py:171
msgid "لطفا آدرس دقیق مرکز را وارد کنید."
msgstr ""

#: agent/api/v1/service_center.py:174
msgid "لطفا مکان مرکز را بر روی نقشه مشخص کنید."
msgstr ""

#: agent/api/v1/service_center.py:185
msgid "خطا در ثبت اطلاعات کاربر."
msgstr ""

#: agent/api/v1/service_center.py:194
msgid ""
"برای شماره وارد شده مرکز ارائه خدمات دیگری در سیستم ثبت شده است. لطفا این "
"مرکز را دوباره ثبت نکنید."
msgstr ""

#: agent/api/v1/service_center.py:200 agent/api/v1/service_center.py:206
#: agent/api/v1/service_center.py:212 agent/api/v1/service_center.py:234
msgid "message"
msgstr ""

#: agent/api/v1/service_center.py:247
msgid "مرکز خدمات با موفقیت ایجاد شد."
msgstr ""

#: agent/api/v1/service_center.py:291
msgid "عدم دسترسی برای تغییر مرکز ارائه خدمات."
msgstr ""

#: agent/api/v1/service_center.py:396
msgid "خطا در ثبت آدرس مرکز."
msgstr ""

#: agent/api/v1/service_center.py:404
msgid "خطا در ثبت اطلاعات عامل مرکز."
msgstr ""

#: agent/api/v1/service_center.py:410
msgid "عدم تطابق کاربر و عامل سرویس سنتر."
msgstr ""

#: agent/api/v1/service_center.py:419
msgid "خطا در ثبت درصد نوع خودرو ها."
msgstr ""

#: agent/api/v1/service_center.py:421
msgid "جمع درصد نوغ خودرو ها باید برابر با ۱۰۰ باشد."
msgstr ""

#: agent/api/v1/service_center.py:438
msgid "تاریخ اتمام اجاره را وارد کنید."
msgstr ""

#: agent/api/v1/service_center.py:444
msgid "کد نقش باید حداکثر ده رقم داشته باشد."
msgstr ""

#: agent/api/v1/service_center.py:450
msgid "خطا در ثبت اطلاعات مرکز."
msgstr ""

#: agent/api/v1/service_center.py:489
msgid "مرکز خدمات با موفقیت بروزرسانی شد."
msgstr ""

#: agent/api/v1/service_center_image.py:31
msgid "عدم دسترسی برای ثبت تصویر جدید."
msgstr ""

#: agent/api/v1/service_center_image.py:38
msgid "تصویر با موفقیت اضافه شد."
msgstr ""

#: agent/api/v1/service_center_image.py:42
msgid "عدم دسترسی برای حذف تصویر."
msgstr ""

#: agent/api/v1/service_center_image.py:45
msgid "تصویر مورد نظر با موفقیت پاک شد."
msgstr ""

#: agent/api/v1/service_center_location.py:27
msgid ""
"امکان تغییر آدرس وجود ندارد، لطفا در صورت نیاز به تغییر آدرس با پشتیبانی "
"تماس بگیرید."
msgstr ""

#: agent/api/v1/service_center_location.py:31
msgid "عدم دسترسی برای ثبت مرکز ارائه خدمات."
msgstr ""

#: agent/api/v1/service_center_location.py:33
msgid "لطفا موقعیت مرکز را بر روی نقشه مشخص کنید."
msgstr ""

#: agent/api/v1/service_center_location.py:37
msgid "خطا در ثبت موقعیت مرکز."
msgstr ""

#: agent/api/v1/service_center_location.py:44
msgid "موقعیت مرکز با موفقیت تغییر‌ یافت."
msgstr ""

#: agent/api/v1/toggle_status.py:24
msgid "وضعیت شما با موفقیت تغییر کرد."
msgstr ""

#: agent/api/v1/toggle_status.py:25
msgid "عدم دسترسی برای تغییر وضعیت."
msgstr ""

#: constants/discount_value_type.py:6
msgctxt "Discount type"
msgid "Rials"
msgstr ""

#: constants/discount_value_type.py:7
msgctxt "Discount type"
msgid "%"
msgstr ""

#: constants/gift_types.py:6
msgctxt "Gift Type is Internet"
msgid "Internet"
msgstr ""

#: constants/gift_types.py:7
msgctxt "Gift Type is Voice"
msgid "Voice"
msgstr ""

#: constants/gift_types.py:8
msgctxt "Gift Type is Cash"
msgid "Cash"
msgstr ""

#: core/validators/acceptable_credit_card.py:12
msgid "credit card from this bank is not acceptable!"
msgstr ""

#: core/validators/credit_card.py:19
msgid "Enter a valid Credit Card"
msgstr ""

#: core/validators/mobile_number.py:9
msgid ""
"Enter a valid mobile number, start with 09 and followed by 8, 13 digits."
msgstr ""

#: core/validators/national_id.py:20
msgid "Enter a valid National ID"
msgstr ""

#: otp/managers/otp.py:89
msgid "کد وارد شده معتبر نیست"
msgstr ""

#: settings/utils.py:15
msgid "English"
msgstr ""

#: settings/utils.py:16
msgid "Farsi"
msgstr ""

#: settings/utils.py:17
msgid "Arabic"
msgstr ""
