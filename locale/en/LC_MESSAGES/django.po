# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-20 20:10+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: agent/api/v1/agent_colleagues.py:49
msgid "لطفا اطلاعات را کامل وارد کنید."
msgstr "please fill the informations completely"

#: agent/api/v1/agent_colleagues.py:51 agent/api/v1/login.py:32
msgid "mobile_number_is_invalid"
msgstr "شماره موبایل وارد شده اشتباه است."

#: agent/api/v1/agent_colleagues.py:55
msgid "phone_number_is_invalid"
msgstr "شماره تلفن وارد شده اشتباه است."

#: agent/api/v1/agent_colleagues.py:59
msgid "national_code_is_invalid"
msgstr "کد ملی وارد شده اشتباه است."


#: agent/api/v1/agent_colleagues.py:64
msgid "mobile_number_is_already_exists"
msgstr "شماره موبایل قبلا به عنوان همکار اضافه شده است."


#: agent/api/v1/agent_colleagues.py:89
msgid "user_added_successfully_invite_message_sent"
msgstr "کاربر با موفقیت اضافه شد. پیامک دعوت ارسال شد."



#: agent/api/v1/agent_visit.py:40
msgid "for_register_visit_you_must_locate_in_center"
msgstr "برای ثبت ویزیت باید در محدوده مرکز قرار بگیرید."






#: agent/api/v1/agent_visit.py:51
msgid ""
"مرکز به حداکثر تعداد مجاز ویزیت در ماه رسیده است. امکان ثبت ویزیت تا ماه بعد "
"وجود ندارد."
msgstr ""
"center reached the maximum visit in month, not possible to visit until next "
"month"

#: agent/api/v1/agent_visit.py:70
msgid "for register visit you must locate in center"
msgstr "برای این مرکز ویزیت فعال وجود دارد. لطفا با پشتیبانی تماس بگیرید."


#: agent/api/v1/agent_visit.py:73
msgid "you have active visit currently, please finish your previous visit"
msgstr "شما هم اکنون ویزیت فعال دارید. لطفا ویزیت قبلی خود را تمام کنید."



#: agent/api/v1/agent_visit.py:84
msgid "your request done"
msgstr "ویزیت شما آغاز شد."


#: agent/api/v1/agent_visit.py:93
msgid "no found active"
msgstr "ویزیت فعالی یافت نشد. لطفا با پشتیبانی تماس بگیرید."


#: agent/api/v1/agent_visit.py:97
msgid "the focus operation of the service center was successfully completed"
msgstr "یادداشت و سوالات با موفقیت ثبت شد."


#: agent/api/v1/agent_visit.py:100
msgid "code sent successfully"
msgstr "پایان ویزیت با موفقیت ثبت شد."


#: agent/api/v1/focus.py:107
msgid "no access to register service center focus"
msgstr "عدم دسترسی برای ثبت فکوس مرکز ارائه خدمات."


#: agent/api/v1/focus.py:134
msgid "all informations must have filled"
msgstr "تمام اطلاعات مورد نیاز باید پر شده باشد."


#: agent/api/v1/focus.py:145
msgid "for register focus you must locate in center"
msgstr "برای ثبت فکوس باید در محدوده مرکز قرار بگیرید."


#: agent/api/v1/focus.py:209
msgid "the focus operation of the service center was successfully completed"
msgstr "عملیات فکوس مرکز ارائه خدمات با موفقیت انجام شد."


#: agent/api/v1/login.py:40
msgid "no agent found with this number"
msgstr "مشاوری با شماره وارد شده یافت نشد."


#: agent/api/v1/login.py:59
msgid "login successfully"
msgstr "ورود شما با موفقیت انجام شد."


#: agent/api/v1/login.py:67
msgid "code sent successfully"
msgstr "کد با موفقیت ارسال شد."


#: agent/api/v1/login.py:69
msgid "error in sending otp code,try again"
msgstr "خطا در ارسال کد تائید، لطفا مجددا تلاش کنید."


#: agent/api/v1/region_summary.py:50
msgid "no access to register for new area"
msgstr "عدم دسترسی برای ثبت منطقه جدید."


#: agent/api/v1/region_summary.py:56
msgid "your request done"
msgstr "درخواست شما انجام شد"


#: agent/api/v1/service_center.py:43 agent/api/v1/service_center_location.py:22
msgid "servcie center not found"
msgstr "مرکز ارائه خدمات یافت نشد."


#: agent/api/v1/service_center.py:141
msgid "please enter the center name"
msgstr "لطفا اسم مرکز را وارد کنید."


#: agent/api/v1/service_center.py:144
msgid "please enter head's mobile number"
msgstr "لطفا شماره موبایل مسئول مرکز را وارد کنید."


#: agent/api/v1/service_center.py:147
msgid "mobile number is invalid"
msgstr "شماره موبایل وارد شده صحیح نیست."


#: agent/api/v1/service_center.py:150
msgid "please enter head's firstname"
msgstr "لطفا نام مسئول مرکز را وارد کنید."


#: agent/api/v1/service_center.py:153
msgid "please enter head's lastname"
msgstr "لطفا نام خانوادگی مسئول مرکز را وارد کنید."


#: agent/api/v1/service_center.py:156
msgid "please enter type of service center"
msgstr "لطفا نوع مرکز را وارد کنید."


#: agent/api/v1/service_center.py:159
msgid "please enter mobile number of center"
msgstr "لطفا شماره تلفن مرکز را وارد کنید."


#: agent/api/v1/service_center.py:162
msgid "mobile number is invalid"
msgstr "شماره تلفن وارد شده صحیح نیست."


#: agent/api/v1/service_center.py:165
msgid "please enter service center's area"
msgstr "لطفا منطقه مرکز را انتخاب کنید."


#: agent/api/v1/service_center.py:168
msgid "please enter postal code "
msgstr "لطفا کد پستی مرکز را وارد کنید."


#: agent/api/v1/service_center.py:171
msgid "please enter exact address"
msgstr "لطفا آدرس دقیق مرکز را وارد کنید."

#: agent/api/v1/service_center.py:174
msgid "please locate the center on map"
msgstr "لطفا مکان مرکز را بر روی نقشه مشخص کنید."


#: agent/api/v1/service_center.py:185
msgid "error in user registration "
msgstr "خطا در ثبت اطلاعات کاربر."

#: agent/api/v1/service_center.py:194
msgid "for this mobile number registered"
msgstr "برای شماره وارد شده مرکز ارائه خدمات دیگری در سیستم ثبت شده است. لطفا این 
مرکز را دوباره ثبت نکنید."

#: agent/api/v1/service_center.py:200 agent/api/v1/service_center.py:206
#: agent/api/v1/service_center.py:212 agent/api/v1/service_center.py:234
msgid "message"
msgstr ""

#: agent/api/v1/service_center.py:247
msgid "service center done successfully"
msgstr "مرکز خدمات با موفقیت ایجاد شد."


#: agent/api/v1/service_center.py:291
msgid "no access to edit servcie center"
msgstr "عدم دسترسی برای تغییر مرکز ارائه خدمات."


#: agent/api/v1/service_center.py:396
msgid "registration_error_in_address"
msgstr "خطا در ثبت آدرس مرکز."

#: agent/api/v1/service_center.py:404
msgid "registration_error"
msgstr "خطا در ثبت اطلاعات عامل مرکز."

#: agent/api/v1/service_center.py:410
msgid "agent service center and user not matched"
msgstr "عدم تطابق کاربر و عامل سرویس سنتر."

#: agent/api/v1/service_center.py:419
msgid "error in recording percent of cars'types "
msgstr "خطا در ثبت درصد نوع خودرو ها."

#: agent/api/v1/service_center.py:421
msgid "the_total_number_of_cars_must_be_one_hundred."
msgstr "جمع درصد نوع خودرو ها باید برابر با ۱۰۰ باشد."

#: agent/api/v1/service_center.py:438
msgid "enter_the_lease_and_date"
msgstr "تاریخ اتمام اجاره را وارد کنید."


#: agent/api/v1/service_center.py:444
msgid "registration_code_number_must_have_maximum_10_digit"
msgstr "کد نقش باید حداکثر ده رقم داشته باشد."


#: agent/api/v1/service_center.py:450
msgid "error_in_recording_the_service_center_information"
msgstr "خطا در ثبت اطلاعات مرکز."


#: agent/api/v1/service_center.py:489
msgid "service_center_updated_successfully"
msgstr "مرکز خدمات با موفقیت بروزرسانی شد."

#: agent/api/v1/service_center_image.py:31
msgid "no_accessـtoـaddـnewـimage"
msgstr "عدم دسترسی برای ثبت تصویر جدید."

#: agent/api/v1/service_center_image.py:38
msgid "image_added_successfully"
msgstr "تصویر با موفقیت اضافه شد."


#: agent/api/v1/service_center_image.py:42
msgid "no_access_to_delete_image"
msgstr "عدم دسترسی برای حذف تصویر."

#: agent/api/v1/service_center_image.py:45
msgid "image_deleted_successfully"
msgstr "تصویر مورد نظر با موفقیت پاک شد."

#: agent/api/v1/service_center_location.py:27
msgid "not_possible_to_change_address_please_contact_with_supporter"
msgstr "امکان تغییر آدرس وجود ندارد، لطفا در صورت نیاز به تغییر آدرس با پشتیبانی
تماس بگیرید."

#: agent/api/v1/service_center_location.py:31
msgid "no_access_to_registr_service_center"
msgstr "عدم دسترسی برای ثبت مرکز ارائه خدمات."

#: agent/api/v1/service_center_location.py:33
msgid "please locate the position of center on map"
msgstr "لطفا موقعیت مرکز را بر روی نقشه مشخص کنید."

#: agent/api/v1/service_center_location.py:37
msgid "error in recording center status"
msgstr "خطا در ثبت موقعیت مرکز."

#: agent/api/v1/service_center_location.py:44
msgid "status of service center change successfully"
msgstr "موقعیت مرکز با موفقیت تغییر‌ یافت."

#: agent/api/v1/toggle_status.py:24
msgid "your_status_changed_successfully"
msgstr "وضعیت شما با موفقیت تغییر کرد."

#: agent/api/v1/toggle_status.py:25
msgid "noـaccessـtoـedit_the_status"
msgstr "عدم دسترسی برای تغییر وضعیت."

#: constants/discount_value_type.py:6
msgctxt "Discount type"
msgid "Rials"
msgstr ""

#: constants/discount_value_type.py:7
msgctxt "Discount type"
msgid "%"
msgstr ""

#: constants/gift_types.py:6
msgctxt "Gift Type is Internet"
msgid "Internet"
msgstr ""

#: constants/gift_types.py:7
msgctxt "Gift Type is Voice"
msgid "Voice"
msgstr ""

#: constants/gift_types.py:8
msgctxt "Gift Type is Cash"
msgid "Cash"
msgstr ""

#: core/validators/acceptable_credit_card.py:12
msgid "credit_card_from_this_bank_is_not_acceptable!"
msgstr "کارت بانکی از این بانک قابل دسترس نیست"

#: core/validators/credit_card.py:19
msgid "Enter_valid_credit_card"
msgstr ""

#: core/validators/mobile_number.py:9
msgid ""
"Enter_valid_mobile_number"
msgstr "شماره معتبر وارد کنید"

#: core/validators/national_id.py:20
msgid "Enter_valid_national_ID"
msgstr ""

#: otp/managers/otp.py:89
msgid "code is invalid"
msgstr "کد وارد شده معتبر نیست"

#: settings/utils.py:15
msgid "English"
msgstr ""

#: settings/utils.py:16
msgid "Farsi"
msgstr ""

#: settings/utils.py:17
msgid "Arabic"
msgstr ""