# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-29 17:55+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: api/v1/agent_profile.py:20
msgid "Image format is invalid."
msgstr "Resim biçimi geçersiz."

#: api/v1/agent_profile.py:23
msgid "Error while saving Image."
msgstr "Resim kaydedilirken hata oluştu"

#: api/v1/agent_profile.py:32 api/v1/service_center_image.py:43
msgid "Image added successfully."
msgstr "Resim başarıyla eklendi"

#: api/v1/agent_visit.py:33
msgid "Lack of access to visit service point."
msgstr "Servis noktasını ziyaret etmek için erişim yok"

#: api/v1/agent_visit.py:44
msgid "To visit a center, you must be within the center's vicinity."
msgstr "Bir merkezi ziyaret etmek için merkezin yakınında olmanız gerekir"

#: api/v1/agent_visit.py:50
msgid "more than one visit per day is impossible."
msgstr "günde birden fazla ziyaret mümkün değildir"

#: api/v1/agent_visit.py:58
msgid "visit done."
msgstr "ziyaret tamamlandı"

#: api/v1/captcha.py:19
msgid "error in loading captcha."
msgstr "captcha yüklenirken hata oluştu"

#: api/v1/focus.py:39
msgid "Lack of access to register the focus for service point."
msgstr "Hizmet noktasına odaklanmayı kaydetmeye yönelik erişim eksikliği"

#: api/v1/focus.py:58
msgid "All required information must be filled in to focus."
msgstr "Odaklanmak için tüm gerekli bilgilerin doldurulması gerekir"

#: api/v1/focus.py:86
msgid "The center's service focus operation has been successfully completed."
msgstr "Merkezin hizmet odakmak operasyonu tamamlandı"

#: api/v1/get_new_access_token.py:18
msgid "Invalid refresh token"
msgstr "Yenileme belirteci geçersiz"

#: api/v1/login.py:39
msgid "invalid phone number"
msgstr "Geçersiz telefon numarası"

#: api/v1/login.py:43
msgid "there is no agent with this phone number"
msgstr "bu telefon numarasına sahip bir acente yok"

#: api/v1/login.py:77
#, fuzzy
#| msgid "Invalid code"
msgid "invalid captcha."
msgstr "Geçersiz kod"

#: api/v1/login.py:81
msgid "otp code sent"
msgstr "Otp kodu gönderildi"

#: api/v1/map_service_center.py:25 api/v1/service_center.py:42
msgid "Service center not found."
msgstr "Servis merkezi bulunamadı"

#: api/v1/map_service_center.py:35 api/v1/service_center.py:56
msgid "Error in showing centers"
msgstr "Merkezleri göstermede hata"

#: api/v1/province.py:22
msgid "error occurred"
msgstr "hata oluştu"

#: api/v1/service_center.py:155
msgid "No access to register a service center."
msgstr "Servis merkezini kaydetmek için erişim yok"

#: api/v1/service_center.py:157
msgid "country code not found"
msgstr "ülke kodu bulunamadı"

#: api/v1/service_center.py:167
msgid "Invalid code"
msgstr "Geçersiz kod"

#: api/v1/service_center.py:173
msgid "data is not valid"
msgstr "veri geçerli değil"

#: api/v1/service_center.py:186
msgid "The service center has been successfully created."
msgstr "Servis merkezi başarıyla oluşturuldu"

#: api/v1/service_center.py:192 serializers/service_center.py:183
#: serializers/service_center.py:213
#, fuzzy
#| msgid "Error in registering service center's information."
msgid "Error in registering service center information."
msgstr "Servis merkezinin bilgilerini kaydederken hata"

#: api/v1/service_center.py:198
msgid "No access to edit a service center."
msgstr "Servis merkezini düzenlemek için erişim yok"

#: api/v1/service_center.py:215
msgid "The service center has been successfully updated."
msgstr "Servis merkezi başarıyla güncellendi"

#: api/v1/service_center_addresses.py:22
msgid "Please enter the ID of service center."
msgstr "Lütfen servis merkezinin kimliğini girin"

#: api/v1/service_center_addresses.py:31
msgid "invalid service center ID."
msgstr "Geçersiz servis merkezi kimliği"

#: api/v1/service_center_addresses.py:34 api/v1/service_center_addresses.py:36
msgid "invalid address ID."
msgstr "Geçersiz adres kimliği/posta codu"

#: api/v1/service_center_addresses.py:46 api/v1/service_center_addresses.py:82
msgid "Please enter the region."
msgstr "Lütfen bölgeyi girin"

#: api/v1/service_center_addresses.py:49 api/v1/service_center_addresses.py:85
msgid "Please enter the shipping_address of service center."
msgstr "Lütfen servis merkezinin gönderim adresini girin"

#: api/v1/service_center_addresses.py:52 api/v1/service_center_addresses.py:88
msgid "Please enter the postal code"
msgstr "Lütfen posta kodunu girin"

#: api/v1/service_center_addresses.py:56 api/v1/service_center_addresses.py:92
msgid "Please pinpoint the location of the shipping address on the map."
msgstr "Lütfen gönderim adresinin haritadaki yerini belirtin"

#: api/v1/service_center_addresses.py:60 serializers/service_center.py:166
msgid "Error in registering the center's address."
msgstr "Merkezin adresini kaydederken hata"

#: api/v1/service_center_addresses.py:80
msgid "You dont have permission to update location address"
msgstr "Konum adresini güncelleme izniniz yok"

#: api/v1/service_center_addresses.py:105
msgid "lack of required information."
msgstr "Gerekli bilgiler eksik"

#: api/v1/service_center_addresses.py:113
msgid "added successfully."
msgstr "Başarıyla eklendi"

#: api/v1/service_center_image.py:30
msgid "no accesses to add image."
msgstr "Resim eklemek için erişim yok"

#: api/v1/service_center_image.py:50
msgid "image deleted successfully."
msgstr "Resim başarıyla silindi"

#: api/v1/sync_service_center_address.py:24
#: api/v1/sync_service_center_address.py:37
msgid "Incorrect Service Center ID."
msgstr "Yanlış Servis Merkezi Kimliği"

#: api/v1/sync_service_center_address.py:28
msgid "address already added."
msgstr "adres zaten eklendi."

#: api/v1/sync_service_center_address.py:32
msgid "Address successfully added."
msgstr "Adres zaten eklendi"

#: api/v1/sync_service_center_address.py:41
msgid "please try later."
msgstr "Lütfen daha sonra deneyin"

#: api/v1/sync_service_center_address.py:45
msgid "Address successfully deleted."
msgstr "Adres başarıyla silindi"

#: serializers/service_center.py:22
msgid "Name"
msgstr ""

#: serializers/service_center.py:23
msgid "Mobile number"
msgstr ""

#: serializers/service_center.py:24
msgid "Region"
msgstr ""

#: serializers/service_center.py:27
msgid "Center type"
msgstr ""

#: serializers/service_center.py:28 serializers/service_center.py:240
msgid "Commercial register"
msgstr ""

#: serializers/service_center.py:30 serializers/service_center.py:223
msgid "License expiration date"
msgstr ""

#: serializers/service_center.py:31 serializers/service_center.py:224
msgid "Sponsor civil number"
msgstr ""

#: serializers/service_center.py:32 serializers/service_center.py:226
msgid "Foreman civil number"
msgstr ""

#: serializers/service_center.py:33 serializers/service_center.py:225
msgid "Sponsor name"
msgstr ""

#: serializers/service_center.py:34
msgid "Experience in year"
msgstr ""

#: serializers/service_center.py:35
msgid "Ownership type"
msgstr ""

#: serializers/service_center.py:47 serializers/service_center.py:253
#, fuzzy
#| msgid ""
#| "Error in registering the information of service center's incharge person."
msgid "Please enter the mobile number of the center's incharge person."
msgstr "Servis merkezinin sorumlu kişisinin bilgilerini kaydederken hata"

#: serializers/service_center.py:54 serializers/service_center.py:260
msgid "Mobile number is not valid."
msgstr "Mobil numara geçerli değil."

#: serializers/service_center.py:57 serializers/service_center.py:263
#, fuzzy
#| msgid "Please enter the ID of service center."
msgid "Please enter the country code of user's mobile number"
msgstr "Lütfen servis merkezinin kimliğini girin"

#: serializers/service_center.py:62 serializers/service_center.py:268
#, fuzzy
#| msgid ""
#| "Error in registering the information of service center's incharge person."
msgid "Please enter the firstname of the center's incharge person."
msgstr "Servis merkezinin sorumlu kişisinin bilgilerini kaydederken hata"

#: serializers/service_center.py:65
#, fuzzy
#| msgid ""
#| "Error in registering the information of service center's incharge person."
msgid "Please enter the email of the center's incharge person."
msgstr "Servis merkezinin sorumlu kişisinin bilgilerini kaydederken hata"

#: serializers/service_center.py:72 serializers/service_center.py:275
#, fuzzy
#| msgid "data is not valid"
msgid "Email is not valid."
msgstr "veri geçerli değil"

#: serializers/service_center.py:75 serializers/service_center.py:278
#, fuzzy
#| msgid ""
#| "Error in registering the information of service center's incharge person."
msgid "Please enter the lastname of the center's incharge person."
msgstr "Servis merkezinin sorumlu kişisinin bilgilerini kaydederken hata"

#: serializers/service_center.py:78 serializers/service_center.py:82
#: serializers/service_center.py:287 serializers/service_center.py:293
msgid "Please enter valid nationality."
msgstr "Lütfen geçerli bir uyruk girin."

#: serializers/service_center.py:97 serializers/service_center.py:310
#, fuzzy
#| msgid "Error in registering service center's information."
msgid "Error in registering user information."
msgstr "Servis merkezinin bilgilerini kaydederken hata"

#: serializers/service_center.py:105 serializers/service_center.py:318
msgid ""
"The entered number is already registered for another service center Please "
"do not register this center again."
msgstr ""
"Girilen numara başka bir servis merkezi için zaten kayıtlı. Lütfen bu "
"merkezi tekrar kaydetmeyin."

#: serializers/service_center.py:118
#, fuzzy
#| msgid "Please enter the region."
msgid "Please enter valid region id."
msgstr "Lütfen bölgeyi girin"

#: serializers/service_center.py:120
msgid "Agent's country must match the service center's region."
msgstr "Temsilcinin ülkesi, servis merkezinin bölgesiyle eşleşmelidir."

#: serializers/service_center.py:125
#, fuzzy
#| msgid "Please enter the ID of service center."
msgid "Please enter the postalcode of service center."
msgstr "Lütfen servis merkezinin kimliğini girin"

#: serializers/service_center.py:127
#, fuzzy
#| msgid "Invalid code"
msgid "invalid postalcode."
msgstr "Geçersiz kod"

#: serializers/service_center.py:130
#, fuzzy
#| msgid "Please enter the shipping_address of service center."
msgid "Please enter the address of service center."
msgstr "Lütfen servis merkezinin gönderim adresini girin"

#: serializers/service_center.py:132
#, fuzzy
#| msgid "Please pinpoint the location of the shipping address on the map."
msgid "Please pinpoint the location of the service center on the map."
msgstr "Lütfen gönderim adresinin haritadaki yerini belirtin"

#: serializers/service_center.py:142
#, fuzzy
#| msgid "To visit a center, you must be within the center's vicinity."
msgid "To create a center, you must be within the center's vicinity."
msgstr "Bir merkezi ziyaret etmek için merkezin yakınında olmanız gerekir"

#: serializers/service_center.py:194
#, fuzzy
#| msgid "Error in registering the service center type."
msgid "Error in registering service types."
msgstr "Servis merkezi türünü kaydederken hata"

#: serializers/service_center.py:227
msgid "Foreman national ID"
msgstr "Ustabaşı kimlik numarası"

#: serializers/service_center.py:248
#, fuzzy
#| msgid "country code not found"
msgid "Brand not found"
msgstr "ülke kodu bulunamadı"

#: serializers/service_center.py:281
#, fuzzy
#| msgid ""
#| "Error in registering the information of service center's incharge person."
msgid "Please enter the nationality id of the center's incharge person."
msgstr "Servis merkezinin sorumlu kişisinin bilgilerini kaydederken hata"

#: serializers/service_center_update.py:51
msgid "User ID is required"
msgstr "Kullanıcı kimliği gereklidir."

#: serializers/service_center_update.py:74
msgid "User and service center's incharge person do not match."
msgstr "Kullanıcı ve servis merkezinin sorumlu kişisi eşleşmiyor"

msgid "login_successfully"
msgstr "giriş başarılı"

msgid "Code is invalid"
msgstr "Kod geçersiz"

msgid "Code has been expired"
msgstr "Kodun süresi dolmuş"

msgid "error_in_sending_otp_please_try_again"
msgstr "OTP gönderilirken hata oluştu, lütfen tekrar deneyin"


#~ msgid "Error in registering the service center's address."
#~ msgstr "Servis merkezinin adresini kaydederken hata"

#~ msgid "invalid email."
#~ msgstr "geçersiz e-posta"

#~ msgid "license expiration is not correct."
#~ msgstr "Lisans süresi doğru değil"

#~ msgid "Error in registering the service center shift capacity."
#~ msgstr "Servis merkezi vardiya kapasitesini kaydederken hata"

#~ msgid "Error in setting the service center config."
#~ msgstr "Servis merkezi yapılandırmasını ayarlarken hata"

#~ msgid "Error in setting the service center weekday shifts."
#~ msgstr "Servis merkezi hafta içi vardiyalarını ayarlarken hata"

#~ msgid "Error in setting the service center happy and rush hour."
#~ msgstr "Servis merkezi mutlu ve yoğun saatlerini ayarlarken hata"
