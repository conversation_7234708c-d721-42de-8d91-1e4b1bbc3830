# Generated by Django 3.2.10 on 2024-07-14 11:23

import django.contrib.postgres.indexes
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DynamicTextTranslation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lang_code', models.Char<PERSON>ield(max_length=35)),
                ('text', models.<PERSON>r<PERSON>ield(max_length=1024, verbose_name='Text')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='translation.dynamictexttranslation')),
            ],
            options={
                'db_table': 'matrix_translation_dynamic_text_translation',
            },
        ),
        migrations.AddIndex(
            model_name='dynamictexttranslation',
            index=django.contrib.postgres.indexes.HashIndex(fields=['text'], name='check_sum'),
        ),
    ]
