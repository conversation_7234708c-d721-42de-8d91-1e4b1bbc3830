from django.contrib import admin
from translation.models import DynamicTextTranslation
from translation.utils.admin_form import UniqueLangCodeInlineFormSet, DynamicChildrenTranslateAdminForm, \
    DynamicParentTranslateAdminForm


class ChildDynamicTextInline(admin.TabularInline):
    model = DynamicTextTranslation
    fk_name = 'parent'
    formset = UniqueLangCodeInlineFormSet
    form = DynamicChildrenTranslateAdminForm
    extra = 0


@admin.register(DynamicTextTranslation)
class DynamicTextAdmin(admin.ModelAdmin):
    list_display = ('text', 'lang_code')
    inlines = [ChildDynamicTextInline]
    form = DynamicParentTranslateAdminForm
    search_fields = ("text",)

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(parent__isnull=True)
