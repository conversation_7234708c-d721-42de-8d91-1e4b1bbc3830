from django.db import models
from .translation import Translation
from django.contrib.postgres.indexes import HashIndex


class DynamicTextTranslation(Translation):
    text = models.CharField(max_length=1024, verbose_name="Text")
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')

    class Meta:
        db_table = "matrix_translation_dynamic_text_translation"
        indexes = (
            HashIndex(fields=['text'], name='check_sum'),
        )
