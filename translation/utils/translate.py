from django.core.exceptions import ObjectDoesNotExist

from ..models import DynamicTextTranslation


def translation_checker(text: str, lang: str) -> str:
    """

    Parameters
    ----------
    text
    lang

    this functions created for dynamic translating of texts in project
    First we check the text in DynamicTextTranslation in en lang if it was
    in db then check for other langs otherwise create or returns the En one

    Returns
    -------

    """
    dynamic_text, created = DynamicTextTranslation.objects.get_or_create(
        text=text, lang_code="en"
    )

    if not created:
        try:
            return dynamic_text.children.get(lang_code=lang).text
        except DynamicTextTranslation.DoesNotExist:
            return dynamic_text.text
    return dynamic_text.text


def get_object_translation(obj, lang: str) -> dict:
    """

    Parameters
    ----------
    obj
    lang

    this function is for dynamic translating of texts in models that we provided
    translation and check for lang we want if it was we return the translation field
    if it was not return empty dict


    Returns
    -------

    """
    if lang == "en":
        return {}
    try:
        translated_obj = obj.translations.get(lang_code=lang)

        return translated_obj.get_translation_fields
    except ObjectDoesNotExist:
        return {}
