from django import forms
from django.contrib import admin
from django.forms import BaseInlineFormSet, ValidationError, ModelForm, ChoiceField, CharField, TextInput
from ..models import DynamicTextTranslation


class UniqueLangCodeInlineFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()

        lang_codes = []
        for form in self.forms:
            if self.can_delete and self._should_delete_form(form):
                continue

            lang_code = form.cleaned_data.get('lang_code')
            if lang_code and lang_code in lang_codes:
                raise ValidationError("Language must be unique")
            lang_codes.append(lang_code)


language_choices = [
    ('ar', 'Arabic'),
    ('hi', "Hindi"),
    ('tr', 'Turkish'),
]


class DynamicChildrenTranslateAdminForm(ModelForm):
    lang_code = ChoiceField(choices=language_choices)

    class Meta:
        model = DynamicTextTranslation
        fields = ["text", "parent", "lang_code"]


class DynamicParentTranslateAdminForm(ModelForm):
    lang_code = CharField(initial='English', widget=TextInput(attrs={'readonly': 'readonly'}))

    class Meta:
        model = DynamicTextTranslation
        fields = ["text", "lang_code"]

    def clean_lang_code(self):
        return "en"


def create_translation_form(model_class, model_fields=None, model_widgets=None):
    class Meta:
        model = model_class
        fields = model_fields if model_fields else "__all__"
        widgets = model_widgets if model_widgets else {}

    attrs = {
        'Meta': Meta,
        'lang_code': forms.ChoiceField(choices=language_choices)

    }

    return type(f'{model_class.__name__}Form', (forms.ModelForm,), attrs)


class AdminTranslationInline(admin.StackedInline):
    model = None
    extra = 0

    def get_formset(self, request, obj=None, **kwargs):
        if obj:
            self.form = create_translation_form(self.model)
        return super().get_formset(request, obj, **kwargs)

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.exclude(lang_code="en")
