from pathlib import Path
import sys
import os


BASE_DIR = Path(__file__).resolve().parent.parent
FIXTURE_DIRS = (
    Path.joinpath(BASE_DIR, "fixtures", "test"),
    Path.joinpath(BASE_DIR, "fixtures", "production"),
)
ADMIN_SITE = "settings.admin.AdminSite"
AUTH_USER_MODEL = "accounts.User"

ROOT_URLCONF = "infrastructure.urls"
WSGI_APPLICATION = "infrastructure.wsgi.application"

MEDIA_URL = "/media/"
MEDIA_ROOT = Path.joinpath(BASE_DIR, "media")
STATIC_URL = "/static/"
STATIC_ROOT = Path.joinpath(BASE_DIR, "static")

LOCALE_PATHS = [
    os.path.join(BASE_DIR, "locale/")
]

