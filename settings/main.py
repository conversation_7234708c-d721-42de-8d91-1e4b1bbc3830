import ast
import os

SEND_SMS = ast.literal_eval(os.getenv("SEND_SMS"))

REDIRECT_LOCAL_PAYMENT = ast.literal_eval(os.getenv("REDIRECT_LOCAL_PAYMENT"))
LOCAL_FRONTEND_PAYMENT_URL = os.getenv("LOCAL_FRONTEND_PAYMENT_URL")

FRONTEND_PAYMENT_URL = os.getenv("FRONTEND_PAYMENT_URL")
PAYMENT_CALLBACK_URL = os.getenv("PAYMENT_CALLBACK_URL")

CARFIXONLINE_PAYMENT_CALLBACK_URL = os.getenv("CARFIXONLINE_PAYMENT_CALLBACK_URL")
PAYMENT_PRE_REDIRECT_URL = os.getenv("PAYMENT_PRE_REDIRECT_URL")

SHORT_URL_PREFIX = os.getenv("SHORT_URL_PREFIX")
FACTOR_URL_PREFIX = os.getenv("FACTOR_URL_PREFIX")

FIREBASE_SERVER_KEY = os.getenv("FIREBASE_SERVER_KEY")

TINYMCE_DEFAULT_CONFIG = {
    "plugins": "link,directionality,lists,advlist,preview",
    "toolbar": "undo redo | styleselect fontselect fontsizeselect | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist | link | ltr rtl | preview",
    'paste_as_text': True,
}
