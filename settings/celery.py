from celery.schedules import crontab

CELERY_IMPORTS = [
    "saleor.celery",
    "quick_book.celery",
    "agent.celery",
    "notification.celery"
]
TASK_ROUTES = {}
CELERY_TASK_SOFT_TIME_LIMIT = 600
CELERY_BEAT_SCHEDULE = {
    "sync_saleor_orders": {
        "task": "sync_saleor_orders",
        # "schedule": crontab(minute="00", hour="03"),
        'schedule': 10800,
        "options": {"queue": "periodic_queue"},
    },
    "update_stock": {
        "task": "update_stock",
        # "schedule": crontab(minute="00", hour="03"),
        'schedule': 3600,
        "options": {"queue": "periodic_queue"},
    },
    "check_synced_saleor_addresses": {
        "task": "check_synced_saleor_addresses",
        "schedule": 21600,
        "options": {"queue": "periodic_queue"},
    },
    "expire_order_lines": {
        "task": "expire_order_lines",
        "schedule": crontab(minute="00", hour="00"),
        "options": {"queue": "periodic_queue"},
    },
    "delete_expired_notifications": {
        "task": "delete_expired_notifications",
        "schedule": 12 * 60 * 60,
        "options": {"queue": "periodic_queue"},
    },
    # "publish_product_on_stock_availability": {
    #     "task": "publish_product_on_stock_availability",
    #     "schedule": crontab(minute="00", hour="08"),
    #     "options": {"queue": "periodic_queue"},
    # },
    "cache_service_products": {
        "task": "cache_service_products",
        "schedule": 5 * 60,
        "options": {"queue": "periodic_queue"},
    },
}

# commands to run!
# celery -A settings.celery_app worker -l INFO -B -Q periodic_queue
# celery -A settings.celery_app beat -l INFO
