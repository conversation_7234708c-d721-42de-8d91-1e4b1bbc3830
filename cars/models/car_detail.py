from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models

from constants import CarCapacityUnit
from core.models import AbstractBaseModel

OMAN_PLATE_LETTERS = "ABDEGHJKLMNRSTWXYZ"


def plate_letters_validator(value):
    if not 1 <= len(value) <= 2:
        raise ValidationError("The license plate must contain between 1 and 2 letters.")

    value = value.upper()

    if not all(char in OMAN_PLATE_LETTERS for char in value):
        raise ValidationError(
            f"The value '{value}' contains invalid characters. "
            f"Allowed letters are: {OMAN_PLATE_LETTERS}"
        )


class CarDetail(AbstractBaseModel):
    car = models.ForeignKey("cars.Car",
                            on_delete=models.PROTECT,
                            related_name="car_details",
                            )
    initial_km = models.PositiveIntegerField(default=0)
    year = models.CharField(max_length=100, blank=True, null=True)

    plate_digits = models.IntegerField(
        validators=[MinValueValidator(1000), MaxValueValidator(99999)],
        blank=True,
        null=True,
        default=None,
    )
    plate_letters = models.CharField(
        validators=[plate_letters_validator],
        max_length=2,
        null=True,
        blank=True,
        default=None,
    )

    capacity = models.PositiveIntegerField(null=True, blank=True)
    capacity_unit = models.CharField(max_length=50, choices=CarCapacityUnit.choices, default=CarCapacityUnit.KILOGRAM)
    vin = models.CharField(max_length=50, null=True, blank=True)
    chassis_no = models.CharField(max_length=17, blank=True, null=True)
    engine_no = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        unique_together = (
            ("plate_letters", "plate_digits"),
        )
        db_table = "matrix_cars_car_detail"
        verbose_name = "car_detail"
        verbose_name_plural = "car_details"

    def __str__(self):
        return "[{}][{}'{}']".format(self.id, self.car_id, self.car.name)

    def save(self, *args, **kwargs):
        if self.plate_letters is not None:
            self.plate_letters = self.plate_letters.upper()
        return super().save(*args, **kwargs)
