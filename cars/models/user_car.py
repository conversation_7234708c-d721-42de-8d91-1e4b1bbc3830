from django.db import models
from django.db.models import Q

from core.models import AbstractBaseModel


class UserCar(AbstractBaseModel):
    user = models.ForeignKey(
        "accounts.User", on_delete=models.PROTECT, related_name="user_car"
    )
    car_detail = models.ForeignKey("cars.CarDetail", on_delete=models.CASCADE, related_name="user_cars")

    class Meta:
        db_table = "matrix_cars_user_car"

    def __str__(self):
        return "[{}][{}] {}".format(self.id, self.car_detail, self.user.username)

    # def save(self, *args, **kwargs):
    #     if self._state.adding and self.user_car_info:
    #         if UserCar.objects.filter(user=self.user, is_active=True).exists():
    #             raise ValueError("یک پلاک فعال برای این راننده در سیستم موجود است.")
    #
    #     if self.is_active and self.specified_car:
    #         if UserCar.objects.filter(~Q(user=self.user), specified_car=self.specified_car, is_active=True).exists():
    #             raise ValueError("این پلاک برای راننده دیگری فعال است."
    #                              "در صورت نیاز ابتدا پلاک را برای راننده قبلی غیرفعال کنید")
    #     super().save(*args, **kwargs)
