from constants import CarModelType
from core.models import AbstractBaseModel
from django.db import models


class Car(AbstractBaseModel):
    name = models.CharField(max_length=255)
    brand = models.ForeignKey("cars.CarBrand", on_delete=models.PROTECT, related_name="cars")
    type = models.CharField(max_length=64, choices=CarModelType.choices, default=CarModelType.PASSENGER)

    class Meta:
        db_table = "matrix_cars_car"

    def __str__(self):
        return "[{}] {}".format(self.id, self.name)

    @property
    def full_name(self):
        return "{} {}".format(self.brand.name, self.name)
