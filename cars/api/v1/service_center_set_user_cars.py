import logging

from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from unidecode import unidecode

from accounts.models import User
from cars.models import Car, UserCar
from cars.serializers import UserCarSerializer
from core.http import JsonResponse
from core.permissions import IsActiveUser
from core.validators import mobile_number_validator

logger = logging.getLogger("cars")


class ServiceCenterSetUserCarsAPI(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsActiveUser)

    def post(self, request):
        self.exception_data = {"message": "شماره موبایل وارد شده اشتباه است."}
        mobile_number = unidecode(str(request.data["phone_number"]))
        mobile_number_validator(mobile_number)

        self.exception_data = {"message": "کاربر یافت نشد."}
        user = User.objects.get(username=mobile_number)

        self.exception_data = {"message": "خودرو یافت نشد."}
        car = Car.objects.get(id=request.data["car_id"])

        self.exception_data = {"message": "خطا در ثبت اطلاعات خودرو کاربر."}
        user_car, created = UserCar.objects.get_or_create(user=user, car=car, year=request.data["year"])

        user_car.chassis_no = request.data.get("chassis_no")
        user_car.engine_no = request.data.get("engine_no")
        user_car.save()

        return JsonResponse(
            status=201 if created else 200, message="خودرو با موفقیت ثبت شد.", data=UserCarSerializer(user_car).data
        )
