import logging
from django.db.models import F
from django.utils.translation import gettext
from rest_framework.views import APIView
from cars.models import CarBrand
from core.http import Response
from constants import CarModelType

logger = logging.getLogger("cars")


class LoadBrandType(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        self.exception_data = {"message": gettext("error occurred while loading brands")}
        type_id = request.query_params.get("type_id", None)
        if type_id:
            brand = list(CarBrand.objects.filter(
                cars__type=type_id,
            ).annotate(
                brand_id=F("id"),
                brand_name=F("name"),
            ).distinct().order_by(
                'brand_name',
            ).values("brand_id", "brand_name"))
            return Response(
                data=brand
            )

        else:
            return Response(
                data=[{"type_id": car_type[0], "type_name": car_type[1]} for car_type in CarModelType.choices]
            )
