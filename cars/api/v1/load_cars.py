import logging
from django.utils.translation import gettext
from rest_framework.views import APIView

from cars.models import Car
from cars.serializers import CarsSerializer
from core.http import JsonResponse

logger = logging.getLogger("cars")


class LoadCarsAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        self.exception_data = {"message": gettext("error occurred while loading cars")}
        brand_id = request.query_params.get("brand_id")
        cars = Car.objects.filter(
            is_active=True,
            brand_id=brand_id,
        ).order_by('name')

        type_id = request.query_params.get("type_id", None)
        if type_id:
            cars = cars.filter(
                type=type_id,
            )

        return JsonResponse(
            data=CarsSerializer(cars, many=True).data
        )
