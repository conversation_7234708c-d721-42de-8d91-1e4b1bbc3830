import logging
from rest_framework.views import APIView
from core.http import Response
from core.authentication import CustomAuthentication
from rest_framework.permissions import IsAuthenticated
from cars.serializers import ScSetUserCarSerializer
from cars.models import UserCar, CarDetail
from django.utils.translation import gettext
from rest_framework import serializers

logger = logging.getLogger("cars")


class ScSetUserCarAPI(APIView):
    authentication_classes = (CustomAuthentication,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        serializer = ScSetUserCarSerializer(data=request.data)
        self.exception_data = {"message": gettext("error occurred while saving user car")}
        if serializer.is_valid():
            try:
                user_car_id = serializer.save()
                return Response(
                    data={
                        "user_car_id": user_car_id
                    },
                    message=gettext("new_car_added_successfully")
                )
            except serializers.ValidationError as e:
                logger.error(f"ScSetUserCarSerializerError: {e.detail[0]}")
                return Response(
                    message=e.detail[0],
                    status=400,
                    success=False
                )

        logger.error(f"ScSetUserCarSerializerError: {serializer.errors}")
        return Response(
            message="user car was not save",
            data=serializer.errors,
            status=400,
            success=False
        )

    def put(self, request):
        self.exception_data = {"message": gettext("something_went_wrong")}
        user_car_id = request.data["user_car_id"]

        user_car = UserCar.objects.select_related("car_detail").get(id=user_car_id)
        serializer = ScSetUserCarSerializer(user_car, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                message=gettext("user_car_edited_successfully")
            )
        return Response(
            status=400,
            data=serializer.errors,
            message="user car not updated"
        )
