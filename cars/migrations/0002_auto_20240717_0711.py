# Generated by Django 3.2.10 on 2024-07-17 07:11

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CarDetail',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('initial_km', models.PositiveIntegerField(default=0)),
                ('year', models.CharField(blank=True, max_length=100, null=True)),
                ('number_plate_city_code', models.PositiveIntegerField(blank=True, null=True, validators=[django.core.validators.MaxValueValidator(99), django.core.validators.MinValueValidator(10)])),
                ('number_plate_2_digit', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MaxValueValidator(99), django.core.validators.MinValueValidator(10)])),
                ('number_plate_3_digit', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MaxValueValidator(999), django.core.validators.MinValueValidator(100)])),
                ('number_plate_alphabet', models.CharField(blank=True, choices=[('الف', 'الف'), ('ب', 'ب'), ('س', 'س'), ('د', 'د'), ('ف', 'ف'), ('گ', 'گ'), ('ه', 'ه'), ('ی', 'ی'), ('ج', 'ج'), ('ک', 'ک'), ('ل', 'ل'), ('م', 'م'), ('ن', 'ن'), ('پ', 'پ'), ('ص', 'ص'), ('ث', 'ث'), ('ت', 'ت'), ('ط', 'ط'), ('ق', 'ق'), ('و', 'و'), ('ز', 'ز'), ('ش', 'ش'), ('ع', 'ع'), ('D', 'D'), ('S', 'S')], default='الف', max_length=3, null=True)),
                ('capacity', models.PositiveIntegerField(blank=True, null=True)),
                ('capacity_unit', models.CharField(choices=[('person', 'نفر'), ('kilogram', 'کیلوگرم')], default='kilogram', max_length=50)),
                ('usage', models.CharField(choices=[('hardtop', 'مسقف فلزی'), ('refrigerator_equipped', 'یخچال دار')], default='hardtop', max_length=50)),
                ('vin', models.CharField(blank=True, max_length=50, null=True)),
                ('chassis_no', models.CharField(blank=True, max_length=17, null=True)),
                ('engine_no', models.CharField(blank=True, max_length=20, null=True)),
            ],
            options={
                'verbose_name': 'car_detail',
                'verbose_name_plural': 'car_details',
                'db_table': 'matrix_cars_car_detail',
            },
        ),
        migrations.RemoveField(
            model_name='car',
            name='is_jalali',
        ),
        migrations.RemoveField(
            model_name='car',
            name='type',
        ),
        migrations.RemoveField(
            model_name='usercar',
            name='specified_car',
        ),
        migrations.AlterField(
            model_name='carbrand',
            name='name',
            field=models.CharField(max_length=64),
        ),
        migrations.AlterField(
            model_name='carbrand',
            name='type',
            field=models.CharField(choices=[('1', 'Passenger'), ('2', 'Commercial'), ('3', 'Motorcycle')], default='1', max_length=64),
        ),
        migrations.DeleteModel(
            name='SpecifiedCar',
        ),
        migrations.AddField(
            model_name='cardetail',
            name='car',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='car_details', to='cars.car'),
        ),
        migrations.AddField(
            model_name='usercar',
            name='car_detail',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='user_cars', to='cars.cardetail'),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='cardetail',
            unique_together={('number_plate_city_code', 'number_plate_2_digit', 'number_plate_3_digit', 'number_plate_alphabet')},
        ),
    ]
