# Generated by Django 3.2.10 on 2025-01-21 11:53

import cars.models.car_detail
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cars', '0003_auto_20250120_0831'),
    ]

    operations = [
        migrations.AddField(
            model_name='cardetail',
            name='plate_digits',
            field=models.IntegerField(blank=True, default=None, null=True, validators=[django.core.validators.MinValueValidator(1000), django.core.validators.MaxValueValidator(99999)]),
        ),
        migrations.AddField(
            model_name='cardetail',
            name='plate_letters',
            field=models.CharField(blank=True, default=None, max_length=2, null=True, validators=[cars.models.car_detail.plate_letters_validator]),
        ),
        migrations.AlterField(
            model_name='cardetail',
            name='capacity_unit',
            field=models.CharField(choices=[('person', 'Person'), ('kilogram', 'Kilogram')], default='kilogram', max_length=50),
        ),
        migrations.AlterUniqueTogether(
            name='cardetail',
            unique_together={('plate_letters', 'plate_digits')},
        ),
        migrations.RemoveField(
            model_name='cardetail',
            name='number_plate_2_digit',
        ),
        migrations.RemoveField(
            model_name='cardetail',
            name='number_plate_3_digit',
        ),
        migrations.RemoveField(
            model_name='cardetail',
            name='number_plate_alphabet',
        ),
        migrations.RemoveField(
            model_name='cardetail',
            name='number_plate_city_code',
        ),
        migrations.RemoveField(
            model_name='cardetail',
            name='usage',
        ),
    ]
