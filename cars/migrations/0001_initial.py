# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Car',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('type', models.CharField(choices=[('riding', 'سواری'), ('heavy', 'سنگین'), ('bike', 'موتورسیکلت'), ('pickup_truck', 'وانت بار'), ('mini_truck', 'نیمه سنگین')], default='riding', max_length=20)),
                ('is_jalali', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'matrix_cars_car',
            },
        ),
        migrations.CreateModel(
            name='CarBrand',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('type', models.CharField(choices=[('riding', 'سواری'), ('heavy', 'سنگین'), ('bike', 'موتورسیکلت'), ('pickup_truck', 'وانت بار'), ('mini_truck', 'نیمه سنگین')], default='riding', max_length=50)),
            ],
            options={
                'db_table': 'matrix_cars_car_brand',
            },
        ),
        migrations.CreateModel(
            name='SpecifiedCar',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('initial_km', models.PositiveIntegerField(default=0)),
                ('year', models.CharField(blank=True, max_length=100, null=True)),
                ('number_plate_city_code', models.PositiveIntegerField(validators=[django.core.validators.MaxValueValidator(99), django.core.validators.MinValueValidator(10)])),
                ('number_plate_2_digit', models.IntegerField(validators=[django.core.validators.MaxValueValidator(99), django.core.validators.MinValueValidator(10)])),
                ('number_plate_3_digit', models.IntegerField(validators=[django.core.validators.MaxValueValidator(999), django.core.validators.MinValueValidator(100)])),
                ('number_plate_alphabet', models.CharField(choices=[('الف', 'الف'), ('ب', 'ب'), ('س', 'س'), ('د', 'د'), ('ف', 'ف'), ('گ', 'گ'), ('ه', 'ه'), ('ی', 'ی'), ('ج', 'ج'), ('ک', 'ک'), ('ل', 'ل'), ('م', 'م'), ('ن', 'ن'), ('پ', 'پ'), ('ص', 'ص'), ('ث', 'ث'), ('ت', 'ت'), ('ط', 'ط'), ('ق', 'ق'), ('و', 'و'), ('ز', 'ز'), ('ش', 'ش'), ('ع', 'ع'), ('D', 'D'), ('S', 'S')], default='الف', max_length=3)),
                ('capacity', models.PositiveIntegerField(blank=True, null=True)),
                ('capacity_unit', models.CharField(choices=[('person', 'نفر'), ('kilogram', 'کیلوگرم')], default='kilogram', max_length=50)),
                ('usage', models.CharField(choices=[('hardtop', 'مسقف فلزی'), ('refrigerator_equipped', 'یخچال دار')], default='hardtop', max_length=50)),
                ('vin', models.CharField(blank=True, max_length=50, null=True)),
                ('chassis_no', models.CharField(blank=True, max_length=17, null=True)),
                ('engine_no', models.CharField(blank=True, max_length=20, null=True)),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='specified_cars', to='cars.car')),
            ],
            options={
                'db_table': 'matrix_cars_specified_car',
                'unique_together': {('number_plate_city_code', 'number_plate_2_digit', 'number_plate_3_digit', 'number_plate_alphabet')},
            },
        ),
        migrations.CreateModel(
            name='UserCar',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('specified_car', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='user_car', to='cars.specifiedcar')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='user_car', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'matrix_cars_user_car',
            },
        ),
        migrations.AddField(
            model_name='car',
            name='brand',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='cars', to='cars.carbrand'),
        ),
    ]
