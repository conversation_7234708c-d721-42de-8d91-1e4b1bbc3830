from core.admin import AbstractBaseAdmin


class CarBrandAdmin(AbstractBaseAdmin):
    list_display = ("id", "name", "priority", "is_active")
    list_filter = () + AbstractBaseAdmin.list_filter
    list_editable = ("priority", "is_active")
    ordering = ("priority",)
    sortable_by = ("priority",)
    search_fields = ("name",)
    list_per_page = 200

    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("is_active",),
                    ("name", "priority"),
                    ("created_at", "updated_at"),
                )
            },
        ),
    )
