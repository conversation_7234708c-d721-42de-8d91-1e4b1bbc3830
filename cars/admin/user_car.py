from core.admin import AbstractBaseAdmin


class UserCarAdmin(AbstractBaseAdmin):
    list_display = ("id", "user")
    search_fields = ("id", "car__name", "user__username")
    raw_id_fields = ("user", "car_detail")
    list_editable = ()

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "is_active",
                    "priority",
                    "user",
                    "car_detail",
                    ("created_at", "updated_at"),
                )
            },
        ),
    )
