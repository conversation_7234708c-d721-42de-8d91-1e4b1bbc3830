from core.admin import AbstractBaseAdmin


class CarDetailAdmin(AbstractBaseAdmin):
    list_display = ("id", "car", "year", "plate_digits", "plate_letters")
    search_fields = ("id", "car__name", "plate_digits", "plate_letters")
    raw_id_fields = ("car",)
    list_editable = ()
    readonly_fields = ("created_at", "updated_at",)

    fieldsets = (
        (
            None,
            {
                "fields": (
                    "is_active",
                    ("priority", "year"),
                    "car",
                    ("plate_digits", "plate_letters"),
                    ("created_at", "updated_at"),
                    ("capacity", "capacity_unit"),
                    "vin"
                )
            },
        ),
    )
