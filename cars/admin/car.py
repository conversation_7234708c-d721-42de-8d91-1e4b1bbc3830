from core.admin import AbstractBaseAdmin


class CarAdmin(AbstractBaseAdmin):
    list_display = ("id", "name", "type", "brand", "priority", "is_active")
    search_fields = ("id", "name")
    list_editable = ("priority", "is_active")
    raw_id_fields = ("brand",)
    sortable_by = ("priority",)
    list_filter = (
        "type",
        "brand",
        "is_active",
        "created_at",
        "updated_at",
    )

    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("is_active", "priority"),
                    ("name", "type", "brand"),
                    ("created_at", "updated_at"),
                )
            },
        ),
    )
