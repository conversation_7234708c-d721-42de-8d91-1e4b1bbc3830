from rest_framework import serializers
from cars.models import UserCar, CarDetail
from accounts.models import User
from django.utils.translation import gettext
from django.db import transaction


class ScSetUserCarSerializer(serializers.Serializer):
    mobile_number = serializers.CharField()
    year = serializers.CharField()
    model_id = serializers.IntegerField()
    plate_digits = serializers.IntegerField(required=False)
    plate_letters = serializers.CharField(required=False)

    def create(self, validated_data):
        with transaction.atomic():
            car_detail, created = CarDetail.objects.get_or_create(
                car_id=validated_data["model_id"],
                year=validated_data["year"],
                plate_digits=validated_data.get("plate_digits"),
                plate_letters=validated_data.get("plate_letters"),
            )
            if not created:
                raise serializers.ValidationError(gettext("this_car_is_already_exists"))

            try:
                user = User.objects.only("id").get(username=validated_data["mobile_number"])
            except:
                raise serializers.ValidationError("user_with_this_phone_number_does_not_exists")

            obj = UserCar.objects.create(
                user_id=user.id,
                car_detail_id=car_detail.id,
            )
            return obj.id

    def update(self, instance, validated_data):
        instance.car_detail.year = validated_data["year"]
        instance.car_detail.save()

        return instance
