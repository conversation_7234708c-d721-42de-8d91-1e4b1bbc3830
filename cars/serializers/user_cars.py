from rest_framework import serializers
from cars.models import UserCar
from cars.serializers import CarsSerializer
from core.serializers import ModelSerializer


class UserCarSerializer(ModelSerializer):
    car_name = serializers.SerializerMethodField()
    user_car_id = serializers.SerializerMethodField()
    type_id = serializers.SerializerMethodField()
    type_name = serializers.SerializerMethodField()
    brand_id = serializers.SerializerMethodField()
    brand_name = serializers.SerializerMethodField()
    model_id = serializers.SerializerMethodField()
    model_name = serializers.SerializerMethodField()
    year = serializers.SerializerMethodField()

    class Meta:
        model = UserCar
        fields = (
            "user_car_id",
            "car_name",
            "type_id",
            "type_name",
            "brand_id",
            "brand_name",
            "model_id",
            "model_name",
            "year",
        )

    def get_user_car_id(self, obj):
        return obj.id

    def get_car_name(self, obj):
        result = f"{obj.car_detail.car.name}-{obj.car_detail.year}"

        if obj.car_detail.plate_digits and obj.car_detail.plate_letters:
            result += f" {obj.car_detail.plate_digits}-{obj.car_detail.plate_letters}"

        return result

    def get_type_id(self, obj):
        return obj.car_detail.car.type

    def get_type_name(self, obj):
        return obj.car_detail.car.get_type_display()

    def get_brand_id(self, obj):
        return obj.car_detail.car.brand.id

    def get_brand_name(self, obj):
        return obj.car_detail.car.brand.name

    def get_model_id(self, obj):
        return obj.car_detail.car.id

    def get_model_name(self, obj):
        return obj.car_detail.car.name

    def get_year(self, obj):
        return obj.car_detail.year
