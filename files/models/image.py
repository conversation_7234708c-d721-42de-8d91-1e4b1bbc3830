from core import models
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType


class Image(models.AbstractBaseModel):
    name = models.CharField(max_length=100, null=True, blank=True)
    image = models.ImageField(upload_to="statics/images/")
    color = models.CharField(max_length=30, null=True, blank=True)
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        limit_choices_to=models.Q(
            model__in=[
                "Product",
                "SellerProduct",
                "Category",
                "Brand",
                "Gift",
                "User",
                "ServiceCenter",
                "Proof",
            ]
        ),
        related_name="images",
    )
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey()

    class Meta:
        db_table = "matrix_files_image"

    def __str__(self):
        return "[{}] {}".format(self.id, self.content_type)
