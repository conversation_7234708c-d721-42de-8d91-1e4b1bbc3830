# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='Image',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('image', models.ImageField(upload_to='statics/images/')),
                ('color', models.CharField(blank=True, max_length=30, null=True)),
                ('object_id', models.PositiveIntegerField()),
                ('content_type', models.ForeignKey(limit_choices_to=models.Q(('model__in', ['Product', 'SellerProduct', 'Category', 'Brand', 'Gift', 'User', 'ServiceCenter', 'Proof'])), on_delete=django.db.models.deletion.CASCADE, related_name='images', to='contenttypes.contenttype')),
            ],
            options={
                'db_table': 'matrix_files_image',
            },
        ),
    ]
