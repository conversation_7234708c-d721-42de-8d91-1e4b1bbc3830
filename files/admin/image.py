from core.admin import link_image
from core.admin.abstract_base_admin import AbstractBaseAdmin


class ImageAdmin(AbstractBaseAdmin):
    list_display = ("id", "thumb", "color", "content_type", "object_id", "priority", "is_active")
    search_fields = ("id", "content_type")
    list_editable = ("is_active", "priority", "color")
    readonly_fields = ("details_thumb",) + AbstractBaseAdmin.readonly_fields
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "details_thumb",
                    ("created_at", "updated_at"),
                    ("is_active", "priority"),
                    ("image", "color"),
                    ("content_type", "object_id"),
                )
            },
        ),
    )

    def thumb(self, obj):
        return link_image(obj.image)

    def details_thumb(self, obj):
        return link_image(obj.image, width=300, height=300)

    details_thumb.short_description = "thumb"
