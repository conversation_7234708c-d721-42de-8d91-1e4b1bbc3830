from core.admin import link_button, link_image
from django.contrib.contenttypes.admin import GenericTabularInline
from django.urls import reverse
from files.models import Image


class ImageInline(GenericTabularInline):
    model = Image
    extra = 0
    readonly_fields = ("image_link", "thumb", "name")
    fieldsets = (
        (
            None,
            {"fields": ("image_link", "thumb", "image", "color", "name", "is_active", "priority")},
        ),
    )
    ordering = ("-priority",)

    def thumb(self, obj):
        return link_image(obj.image)

    def image_link(self, obj):
        return link_button(reverse("admin:files_image_change", args=(obj.id,)), obj.id)

    image_link.short_description = "#"
