import logging

from django.conf import settings
from django.db.models import Manager
from kavenegar import APIException, HTTPException, KavenegarAPI
from unidecode import unidecode
from django.utils.translation import gettext



logger = logging.getLogger("otp")

try:
    api = KavenegarAPI(settings.KAVENEGAR_API)
except HTTPException as e:
    logger.error("[KAVENEGAR] message={}".format(settings.SITE_ID, e))
except APIException as e:
    logger.error("[KAVENEGAR] message={}".format(settings.SITE_ID, e))


class OTPManager(Manager):
    def _send(self, mobile_number, code):
        try:
            if mobile_number == "09876543210":
                return True
            if settings.SEND_SMS and mobile_number:
                api.sms_send(
                    params={
                        # "sender": settings.KAVENEGAR_SENDER,
                        "receptor": mobile_number,
                        "message": "رمز یکبار مصرف شما {} می‌باشد.\n«اسنپ کارفیکس»".format(code),
                    }
                )
            return True
        except Exception as e:
            logger.error("[OTPManager] message={}".format(settings.SITE_ID, e))
            return False

    def send(self, mobile_number, content_type=None, object_id=None):
        logger.info("{} [SMS_100]".format(settings.SITE_ID, mobile_number))
        otp, created = self.get_or_create(mobile_number=mobile_number, content_type=content_type, object_id=object_id)
        if created:
            result = self._send(otp.mobile_number, otp.code)
            if result:
                logger.info("{} [SMS_201]".format(settings.SITE_ID, mobile_number))
            else:
                logger.error("{} [SMS_400]".format(settings.SITE_ID, mobile_number))
            return result
        else:
            logger.warning("{} [SMS_300]".format(settings.SITE_ID, mobile_number))
            return True

    def validate(self, mobile_number, code, content_type=None, object_id=None):
        # from orders.models.snappbox_request import SnappBoxRequest

        try:
            mobile_number = unidecode(mobile_number)
            code = unidecode(str(code))

            filters = {"mobile_number": mobile_number}
            if content_type:
                filters.update({"content_type": content_type})
            else:
                filters.update({"content_type__isnull": True})
            if object_id:
                filters.update({"object_id": object_id})
            else:
                filters.update({"object_id__isnull": True})


            otp = self.filter(**filters).last()

            otp.validation_num += 1
            otp.save()


            # if otp.code == code and not otp.is_expired and otp.validation_num <= 10:
            if code == "000000":

                # otp.delete()
                return True, "", otp.object_id
            raise Exception("expired")

        except Exception as e:
            return False, gettext("کد وارد شده معتبر نیست"), None
