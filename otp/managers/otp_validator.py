import datetime
import logging
import secrets

from django.conf import settings
from django.core.cache import cache
from django.utils.translation import gettext as _

from constants.otp_types import OTPType
from core.utils.sms_microservice_facade import SMSServiceFacade
from content.models import Config

logger = logging.getLogger('otp')


# try:
#     api = KavenegarAPI(settings.KAVENEGAR_API)
# except HTTPException as e:
#     logger.error("[KAVENEGAR] message={}".format(settings.SITE_ID, e))
# except APIException as e:
#     logger.error("[KAVENEGAR] message={}".format(settings.SITE_ID, e))

# WHITE_LIST_NUMBERS = {
#     "+96809999999": "000000",
#     "+900888888888": "000000",
#     "+96872221793": "687945",
#     "+96898413181": "231984",
#     "+905309596789": "897345"
# }


class OtpValidator:
    @staticmethod
    def white_list_numbers():
        config = Config.objects.get(key="agent_whitelist_numbers")
        white_list = config.value
        return white_list

    @staticmethod
    def _set_cache(key):
        if cache.get(key):
            cache.delete(key)
        code = secrets.SystemRandom().randint(100000, 999999)
        cache.set(key, {"otp_code": str(code)}, 120)
        return True, ""

    @staticmethod
    def _send(mobile_number, otp_code, text, messaging_service=None):
        logger.info("_send {} [SMS_120]".format(settings.SITE_ID, mobile_number))

        white_list_numbers = OtpValidator.white_list_numbers()

        try:
            if mobile_number in white_list_numbers:
                return True
            _status = False
            if mobile_number:
                _mobile_number = str(mobile_number).split("+")[1]
                uid = "{}-{}".format(_mobile_number, datetime.datetime.now().strftime("%m-%d-%Y-%H-%M-%S"))
                logger.info("_send {} [SMS_122]".format(settings.SITE_ID, mobile_number))
                data = {
                    "phone_number": str(_mobile_number),
                    "text": text.format(otp_code),

                }
                if messaging_service:
                    data["messaging_service"] = messaging_service
                logger.info("_send {} [SMS_123]".format(data))
                # _status, message = SMSAla.send_sms(**data)
                is_omani_number = mobile_number.startswith("+968")
                if is_omani_number:
                    sms_service_facade_instance = SMSServiceFacade()
                    response, _status = sms_service_facade_instance.create_message_request(**data)
                    logger.info("_send_response {} [SMS_123]".format(response))
                else:
                    # todo send sms for turkey
                    pass

            return _status
        except Exception as e:
            logger.info("_send {} [SMS_420]".format(settings.SITE_ID, mobile_number))
            logger.info("_send {} [SMS_420]".format(str(e)))

            return False

    @staticmethod
    def send(mobile_number, otp_type: OTPType, text, messaging_service=None, content_type=None, object_id=None):

        white_list_numbers = OtpValidator.white_list_numbers()

        logger.info("{} [SMS_100]".format(settings.SITE_ID, mobile_number))

        response, message = OtpValidator._set_cache(f"{otp_type}-{mobile_number}")
        if response:
            code = cache.get(f"{otp_type}-{mobile_number}")['otp_code']

            if OtpValidator._send(mobile_number, code, text=text, messaging_service=messaging_service):
                logger.info("{} [SMS_201]".format(settings.SITE_ID, mobile_number))
            if mobile_number in white_list_numbers or mobile_number:
                return True, code
            else:
                logger.error("{} [SMS_400]".format(settings.SITE_ID, mobile_number))
                return False, _("error_in_sending_otp_please_try_again.")
        else:
            return response, message

    @staticmethod
    def validate(phone_number, otp_code: str, otp_type: OTPType) -> [bool, str]:

        white_list_numbers = OtpValidator.white_list_numbers()

        if phone_number in white_list_numbers and otp_code == white_list_numbers[phone_number]:
            return True, _("login_successfully")

        cache_data = cache.get(f"{otp_type}-{phone_number}")
        if cache_data:
            if cache_data["otp_code"] == otp_code:
                cache.delete(phone_number)
                return True, _("login_successfully")
            return False, _("Code is invalid")
        return False, _("Code has been expired")
