from core.admin.abstract_base_admin import AbstractBaseAdmin


class OTPAdmin(AbstractBaseAdmin):
    list_display = (
        "mobile_number",
        "code",
        "content_type",
        "object_id",
        "expire_at",
        "created_at",
        "validation_num",
    )
    search_fields = ("mobile_number",)
    readonly_fields = ("code", "validation_num", )
    ordering = ("-created_at",)
    list_editable = ()

    def has_change_permission(self, request, obj=None):
        return False

    def has_export_permission(self, request):
        return False

    def has_import_permission(self, request):
        return False
