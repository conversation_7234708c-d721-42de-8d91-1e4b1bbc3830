import datetime

from core.utils import timezone
from django.contrib.contenttypes.models import ContentType
from otp.models.otp import OTP
from settings.celery_app import celery_app

@celery_app.task(name="otp_removal", autoregister=True)
def otp_removal(**kwargs):
    date = timezone.now() - datetime.timedelta(seconds=180)
    snappbox_content_type = ContentType.objects.get(model="SnappBoxRequest")
    OTP.objects.exclude(content_type=snappbox_content_type).filter(created_at__lte=date).delete()
