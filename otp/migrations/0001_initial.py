# Generated by Django 3.2.10 on 2023-09-17 08:58

import core.validators.mobile_number
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='OTP',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('mobile_number', models.CharField(max_length=15, null=True, validators=[core.validators.mobile_number.MobileNumberValidator()])),
                ('code', models.Char<PERSON>ield(blank=True, editable=False, max_length=10)),
                ('object_id', models.CharField(blank=True, max_length=100, null=True)),
                ('validation_num', models.IntegerField(default=0)),
                ('content_type', models.ForeignKey(blank=True, limit_choices_to=models.Q(('model__in', ['Bill', 'Returned', 'Factor', 'SnappBoxRequest', 'CreditBill'])), null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
            options={
                'db_table': 'matrix_otp_otp',
            },
        ),
    ]
