from random import randint

from core import models
from core.utils import timezone
from core.validators import mobile_number_validator
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType

from otp.managers import OTPManager


class OTP(models.AbstractBaseModel):
    mobile_number = models.CharField(max_length=15, validators=[mobile_number_validator], null=True)
    code = models.CharField(max_length=10, blank=True, editable=False)

    content_object = GenericForeignKey()
    object_id = models.CharField(max_length=100, null=True, blank=True)
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        limit_choices_to=(models.Q(model__in=["Bill", "Returned", "Factor", "SnappBoxRequest", "CreditBill"])),
    )

    validation_num = models.IntegerField(default=0)

    objects = OTPManager()

    class Meta:
        db_table = "matrix_otp_otp"

    @property
    def is_expired(self):
        return (timezone.now() - self.created_at).total_seconds() > 300

    @property
    def expire_at(self):
        time = (timezone.now() - self.created_at).total_seconds()
        if time < 300:
            return int(300 - time)
        return "expired"

    def __str__(self):
        return "[{}] {}".format(self.code, self.mobile_number)

    def save(self, *args, **kwargs):
        if self._state.adding or not self.code:
            if self.mobile_number == "09876543210":
                self.code = "000000"
            else:
                self.code = str(randint(111111, 999999))
        return super().save(*args, **kwargs)
