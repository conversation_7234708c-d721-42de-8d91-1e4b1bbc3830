from django.core.management.base import BaseCommand as DjangoBaseCommand

style_functions = [
    "NOTICE",
    "SUCCESS",
    "WARNING",
    "ERROR",
    "ERROR_OUTPUT",
    "HTTP_SUCCESS",
    "HTTP_BAD_REQUEST",
    "HTTP_INFO",
    "HTTP_NOT_FOUND",
    "HTTP_NOT_MODIFIED",
    "HTTP_REDIRECT",
    "HTTP_SERVER_ERROR",
    "MIGRATE_HEADING",
    "MIGRATE_LABEL",
    "SQL_COLTYPE",
    "SQL_FIELD",
    "SQL_KEYWORD",
    "SQL_TABLE",
]


class BaseCommand(DjangoBaseCommand):
    def handle(self, *args, **options):
        raise NotImplementedError("subclasses of BaseCommand must provide a handle() method")

    def confirm(self, msg, accept=["y"], style="WARNING", raise_exception=True):
        msg = "{} [{}] ".format(msg, "|".join(accept))
        self.print(msg, style=style, ending="")
        result = input()
        if result not in accept:
            if raise_exception:
                raise SystemExit(self.s("Progress Aborted.", "ERROR"))
            else:
                return False, result
        return True, result

    def print(self, msg, style=None, ending=None):
        try:
            style = self.style.__getattribute__(style.upper())
        except Exception as e:
            style = None
        self.stdout.write(msg, style_func=style, ending=ending)

    def s(self, msg, style):
        try:
            return self.style.__getattribute__(style)(msg)
        except Exception as e:
            return msg
