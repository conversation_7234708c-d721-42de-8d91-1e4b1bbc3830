import logging
from service_center.models import ServiceCenter
from saleor.utils import Saleor
from django.core.management.base import BaseCommand

logger = logging.getLogger("saleor")


class Command(BaseCommand):

    def handle(self, *args, **options):
        service_centers = ServiceCenter.objects.filter(saleor_user_id__isnull=True, user__country_code="+968").order_by("-id")

        for sc in service_centers:
            try:
                saleor = Saleor(site="gmall")
                status, response = saleor.create_customer(service_center=sc)
            except Exception as e:
                saleor = Saleor(site="gmall")
                saleor.update_customer(service_center=sc)
                logger.error(f'SYNC-CUSTOMER | error: {e} service-center-id: {sc.id}')
                continue
