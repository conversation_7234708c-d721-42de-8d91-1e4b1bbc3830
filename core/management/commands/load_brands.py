import logging
from django.core.management.base import BaseCommand
from quick_book.models import Brand

logger = logging.getLogger("quick_book")


class Command(BaseCommand):
    help = 'Loads brands from file'

    def handle(self, *args, **options):
        brands = [
            "Mobil",
            "Castrol",
            "Liqui Moly",
            "Petrol Ofisi",
            "Motul",
            "Shell",
            "Lubex",
            "Brava",
            "Art Oil",
            "Elf",
            "Mannol",
            "Moil",
            "Awax",
            "Doill",
            "Opet",
            "Türkiye Petrolleri",
            "Snc Oil",
            "Total",
            "BSG",
            "Lukoil",
            "Rowe",
            "Petronas",
            "Axa Lubricants",
            "Nova",
            "Alpet",
            "Petro Time",
            "Lubrico",
            "LUBCO",
            "Caldini",
            "Goodyear",
            "Powerten",
            "ENEOS",
            "Endoil",
            "Japan Oil",
            "Texaco",
            "Kixx",
            "Opar",
            "Mobil 1",
            "Ordu Tarım",
            "Petrochem",
            "Valvoline",
            "FMY",
            "Ipone",
            "Magena",
            "SNC",
            "Igonn",
            "Kratos",
            "Rexoil",
            "Store",
            "Delphi",
            "Grat",
            "Kraftvoll",
            "Rafinol",
            "Texol",
            "Uberlub",
            "Bor Power",
            "Gm",
            "Manpet",
            "Oleo-Mac",
            "Oregon",
            "Selenia",
            "Wolf",
            "Wolver",
            "Active",
            "Brio",
            "Deep Power",
            "Eni",
            "Eurol",
            "Gazpromneft",
            "JLM",
            "Motorcraft",
            "Motrio/Mais",
            "OEST",
            "Polymerium",
            "Putoline",
            "Skf",
            "Speedol",
            "TK Petrol",
            "Woil",
            "Würth",
            "İtal",
            "Abro",
            "Audi",
            "BDR",
            "Boilex",
            "Bp",
            "Bul-Max",
            "Bybest",
            "CCP",
            "CST",
            "Carlio",
            "Champion",
            "Datsu",
            "Dtx Kimya",
            "Dynamix",
            "EC Shop",
            "Eco-Fix",
            "Favorit",
            "Ferrino",
            "Fin25",
            "GDY",
            "GK Creative",
            "Gazprom",
            "General Motors",
            "Grassburg",
            "Hattat",
            "Hella",
            "Heropar",
            "Hodbehod",
            "Honda",
            "Humaoil",
            "Hunthink",
            "Husqvarna",
            "Ingco",
            "Koto",
            "Kruger",
            "Kunzel",
            "Last Point",
            "Loctite",
            "Lubran",
            "Lubratech",
            "M Oil",
            "MF Oil",
            "Mann",
            "Metra Shop",
            "Monex",
            "Monreal",
            "Naturachem",
            "Nergy Tech",
            "Oilox",
            "PARION",
            "Pure",
            "Ravenol",
            "Renault Mais",
            "SP",
            "Salvo",
            "Spoil",
            "Sprint",
            "Stihl",
            "Stilvoll",
            "Suroil",
            "Swanson Works",
            "TUSEMOIL",
            "Teknotrust",
            "Total Energies",
            "Walther",
            "Wynns",
            "Xenol",
            "Xpro",
            "Yalova Oto Aksesuar",
            "Zig&Zoc",
            "Öz Trend Store"
        ]

        for brand in brands:
            Brand.objects.get_or_create(name=brand)