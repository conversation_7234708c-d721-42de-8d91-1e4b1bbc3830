import logging

from django.contrib.sites.models import Site
from django.core.management.base import BaseCommand

from constants import SellerStockType
from products.models import ProductSellingConfig, ProductServiceCenterConfig, SellingConfig, \
    ProductServiceCenterStockTransaction
from quick_book.models import Product
from service_center.models import ServiceCenter

logger = logging.getLogger("saleor")


class Command(BaseCommand):

    def handle(self, *args, **options):
        hig = Site.objects.get(name="hig")
        products = Product.objects.active_products(is_bundle=False, site=hig).exclude(name="service_fee")
        sc = ServiceCenter.objects.get(pk=1302)
        selling_config = SellingConfig.objects.filter(
            stock_type=SellerStockType.TRUSTY,
            charge_price=0,
            shift_delay=0,
            service_amount=2,
            happy_hour_service_amount=0,
            delivery_amount=0
        ).first()
        if not selling_config:
            selling_config = SellingConfig.objects.create(
                stock_type=SellerStockType.TRUSTY,
                charge_price=0,
                shift_delay=0,
                service_amount=2,
                happy_hour_service_amount=0,
                delivery_amount=0
            )
        for product in products:
            try:
                psc = ProductSellingConfig.objects.get(
                    product=product,
                    selling_config__stock_type=SellerStockType.TRUSTY,
                )
                pscc, _ = ProductServiceCenterConfig.objects.get_or_create(
                    service_center=sc,
                    product_selling_config=psc,
                    balance_percentage=100,
                    is_active=True

                )
            except Exception as e:

                psc = ProductSellingConfig.objects.create(
                    product=product,
                    selling_config=selling_config,
                )
                pscc = ProductServiceCenterConfig.objects.create(
                    service_center=sc,
                    product_selling_config=psc,
                    balance_percentage=100,
                    is_active=True

                )
                pscc.sites.add(hig)

            try:
                ProductServiceCenterStockTransaction.objects.create(
                    service_center=sc,
                    product=product,
                    quantity=1000,
                    stock_type=SellerStockType.TRUSTY
                )
            except Exception as e:
                logger.error("creating transaction from excel error :{}".format(e))
                continue
