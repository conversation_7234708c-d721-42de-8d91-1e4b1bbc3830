import logging

from django.contrib.sites.models import Site
from django.db.models import Sum, ExpressionWrapper, F, Value, IntegerField, Q
from django.db.models.functions import Coalesce

from constants import SellerStockType
from constants.creator_type import CreatorType
from core.utils.create_excel import create_excel_file
from products.models import ProductServiceCenterStockTransaction

from django.core.management.base import BaseCommand
from openpyxl import Workbook

logger = logging.getLogger("products")


class Command(BaseCommand):

    def handle(self, *args, **options):
        transactions = ProductServiceCenterStockTransaction.objects.exclude(service_center_id=1302).filter(
            order_line_id__isnull=True,
            stock_type=SellerStockType.TRUSTY
        ).values("product__name", "product_id", "service_center_id").annotate(
            delivered=Sum("quantity"),
            hig_share=ExpressionWrapper(
                Sum(
                    (Coalesce(F('service_center_balance_percentage'), Value(100))) * F('quantity'), ) / 100,
                output_field=IntegerField()),

            sc_share=F('delivered') - F('hig_share')

        )


        for transaction in transactions:
            ProductServiceCenterStockTransaction.objects.create(
                product_id=transaction["product_id"],
                service_center_id=transaction["service_center_id"],
                quantity=transaction["sc_share"],
                creator_type=CreatorType.ADMINISTRATOR,
                stock_type=SellerStockType.TRUSTY,
                service_center_balance_percentage=100
            )
