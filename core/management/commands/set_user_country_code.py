import logging
from django.core.management.base import BaseCommand
from accounts.models import User

logger = logging.getLogger("saleor")


class Command(BaseCommand):

    def handle(self, *args, **options):
        for user in User.objects.filter(country_code__isnull=True):
            if user.username.startswith("+90"):
                user.country_code = "+90"


            else:
                user.country_code = "+968"

            user.save(update_fields=["country_code"])
