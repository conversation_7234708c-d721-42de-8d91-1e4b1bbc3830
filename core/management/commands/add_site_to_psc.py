import logging

from django.contrib.sites.models import Site

from service_center.models import ServiceCenter
from products.models import ProductServiceCenterConfig

from django.core.management.base import BaseCommand

logger = logging.getLogger("saleor")

class Command(BaseCommand):

    def handle(self, *args, **options):
        for pscc in ProductServiceCenterConfig.objects.all():
            pscc.sites.add(Site.objects.get(name="hig"))
            pscc.save()
        sc = ServiceCenter.objects.get(id=1372)
        sc.is_active = True
        sc.save()