from django.conf import settings
from packaging import version

from .base_permission import BasePermission


class HasAppVersion(BasePermission):
    api_version_key = "HTTP_APP_VERSION"
    settings_version_key = "APP_VERSION"
    settings_message_key = "APP_VERSION_MESSAGE"
    message = (
        "شما از نسخه قدیمی {request_version} برنامه استفاده میکنید، " 
        "لطفا نسخه جدید {app_version} را نصب کنید."
    )

    def has_permission(self, request, view):
        app_version = getattr(settings, self.settings_version_key)
        request_version = request.META.get(self.api_version_key, "0.0.0")
        if app_version and version.parse(request_version) < version.parse(app_version):
            self.message = getattr(settings, self.settings_message_key, self.message).format(
                request_version=request_version, app_version=app_version
            )
            return False
        return True
