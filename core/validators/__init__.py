from django.contrib.auth.validators import (
    ASCIIUsernameValidator,
    UnicodeUsernameValidator,
)

from .acceptable_credit_card import acceptable_credit_card_validator
from .ascii import ASCIIValidator
from .credit_card import credit_card_validator
from .digit import DigitValidator
from .location import location_in_iran
from .mobile_number import MobileNumberValidator
from .national_id import national_id_validator
from .phone_number import PhoneNumberValidator
from .sheba_number import sheba_number_validator
from .vin_validator import VINValidator
from .zip_code import ZipCodeValidator
from .postal_code import validate_postal_code_based_on_country

ascii_validator = ASCIIValidator()
digit_validator = DigitValidator()
mobile_number_validator = MobileNumberValidator()
phone_number_validator = PhoneNumberValidator()
unicode_username_validator = UnicodeUsernameValidator()
ascii_username_validator = ASCIIUsernameValidator()
zip_code_validator = ZipCodeValidator()
vin_validator = VINValidator()
