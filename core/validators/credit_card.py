from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


def credit_card_validator(value):
    try:
        if len(value) != 16 or not isinstance(value, str) or not value.isdigit():
            raise ValueError()

        total = 0
        for index, number in enumerate(value, 1):
            val = int(number) * ((index % 2) + 1)
            total += val > 9 and val - 9 or val

        if total % 10 != 0:
            raise ValueError()

    except Exception as e:
        raise ValidationError(_("Enter a valid Credit Card"), code="invalid")
