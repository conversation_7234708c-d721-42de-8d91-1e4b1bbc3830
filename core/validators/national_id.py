from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


def national_id_validator(value):
    try:
        if len(value) != 10 or not isinstance(value, str) or not value.isdigit():
            raise ValueError()
        total = 0
        for index, number in enumerate(value[-2::-1], 2):
            total += int(number) * index
        total = total % 11
        if total < 2:
            if total != int(value[-1]):
                raise ValueError()
        elif int(value[-1]) != 11 - total:
            raise ValueError()

    except Exception as e:
        raise ValidationError(_("Enter a valid National ID"), code="invalid")
