def location_in_iran(latitude, longitude):
    center = (32.4279, 53.6880)
    top_right = [
        (38.279062, 57.217006),
        (37.527990, 59.352500),
        (37.143605, 59.723371),
        (36.938656, 60.296300),
        (36.646725, 61.153942),
        (35.979179, 61.306648),
    ]
    bottom_right = [
        (30.833542, 61.776697),
        (29.859549, 60.879005),
        (28.997346, 61.531646),
        (26.645768, 63.194511),
        (25.744385, 61.822694),
        (25.059725, 61.421598),
        (26.848769, 63.310778),
    ]
    bottom_left = [
        (27.164476, 52.903434),
        (26.814574, 51.994818),
        (27.814720, 50.777967),
        (29.031625, 50.514831),
        (26.554817, 52.437935),
        (27.341429, 52.630983),
        (27.453323, 52.557269),
        (27.464757, 52.058994),
        (27.615768, 51.635373),
        (27.911016, 50.841969),
        (29.204812, 50.312928),
        (29.922345, 50.147290),
        (29.922461, 48.620831),
        (30.008843, 48.447153),
        (30.316523, 48.207353),
        (30.404197, 48.127525),
        (30.476835, 48.026016),
        (30.999513, 47.683073),
    ]
    top_left = [
        (32.118702, 47.587787),
        (32.459792, 47.141918),
        (32.758400, 46.754419),
        (32.969865, 46.071866),
        (33.501061, 45.869648),
        (33.663103, 45.718158),
        (33.850992, 45.574633),
        (33.951664, 45.471329),
        (39.390005, 44.053255),
        (39.410397, 44.374777),
        (39.782751, 44.610187),
        (39.807998, 44.370345),
    ]

    if latitude >= center[0] and longitude <= center[1]:
        for coordination in top_left:
            if latitude <= coordination[0] and longitude >= coordination[1]:
                return True
    elif latitude >= center[0] and longitude >= center[1]:
        for coordination in top_right:
            if latitude <= coordination[0] and longitude <= coordination[1]:
                return True
    elif latitude <= center[0] and longitude >= center[1]:
        for coordination in bottom_right:
            if latitude >= coordination[0] and longitude <= coordination[1]:
                return True
    else:
        for coordination in bottom_left:
            if latitude >= coordination[0] and longitude >= coordination[1]:
                return True
    return False
