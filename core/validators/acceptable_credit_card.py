from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _


def acceptable_credit_card_validator(value):
    unacceptable_credit_cards = ['606373', '636949', '627381', '639599', '505801', '504172']
    try:
        if value[0:6] in unacceptable_credit_cards:
            raise ValueError()

    except Exception as e:
        raise ValidationError(_("credit card from this bank is not acceptable!"), code="invalid")
