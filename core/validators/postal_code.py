def validate_postal_code_based_on_country(postal_code, country_code):
    postal_code_rules = {
        "968": {"max_length": 3, "min_length": 1},  # Oman: Postal code has a max length of 3
        "90": {"max_length": 5, "min_length": 5},  # Turkey: Postal code must be exactly 5 digits
        "255": {"max_length": 5, "min_length": 5},  # Tanzania: Postal code must be exactly 5 digits
        # Add more countries here as needed
    }

    # Check if the country code exists in the rules
    if country_code not in postal_code_rules:
        return False  # or raise an exception, depending on your preference

    # Retrieve the length rules for the given country code
    country_rules = postal_code_rules[country_code]

    # Check postal code length based on the country rules
    if not (country_rules["min_length"] <= len(postal_code) <= country_rules["max_length"]):
        return False

    return True
