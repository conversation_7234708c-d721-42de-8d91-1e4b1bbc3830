from django.core import validators
from django.utils.deconstruct import deconstructible
from django.utils.regex_helper import _lazy_re_compile


@deconstructible
class MobileNumberValidator(validators.RegexValidator):

    def __call__(self, value):
        if value in ['+96809999999', "+96800999999", "+900888888888", "+255999999999"]:
            return
        regex = self.get_regex_based_on_country_code(value)
        self.regex = regex
        self.regex = _lazy_re_compile(self.regex, self.flags)
        super().__call__(value)

    def get_regex_based_on_country_code(self, value):
        regex_validations = [
            {'code': '+971', 'regex': r'^\+971\d{9}$'},  # Dubai : +971501234567
            {'code': '+90', 'regex': r'^\+905\d{9}$'},  # Turkey
            {'code': '+968', 'regex': r'^\+968[279]\d{7}$'},  # oman : +968 24224149
            {'code': '+1', 'regex': r'^\+1\d{10}$'},  # Canada
            {'code': '+49', 'regex': r'^\+49\d{10}$'},  # Germany
            {'code': '+973', 'regex': r'^\+973\d{8}$'},  # Bahrain: +97333123456
            {'code': '+52', 'regex': r'^\+52\d{10}$'},  # Mexico: +521551234567
            {'code': '+98', 'regex': r'^\+98\d{10}'},  # Iran: 9382699675
            {'code': '+255', 'regex': r'^\+255[67]\d{8}$'}  # Tanzania: +255712345678
        ]

        for validation in regex_validations:
            if value.startswith(validation['code']):
                return validation.get("regex", r'^\d+$')
        return None

    message = "Invalid phone number"