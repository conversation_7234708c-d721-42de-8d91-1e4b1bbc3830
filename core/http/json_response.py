from django.conf import settings
from django.http import JsonResponse as BaseJsonResponse

META = {
    "site": settings.SITE_TITLE,
    "site_id": settings.SITE_ID,
    "version": settings.VERSION,
    "app_version": settings.APP_VERSION,
}


class JsonResponse(BaseJsonResponse):
    def __init__(
            self,
            data=None,
            message="",
            extra=None,
            success=None,
            pagination=None,
            status=200,
            code=None,
            meta=None,
            **kwargs
    ):
        message = "Error, Please try again later" if status >= 500 and not message else message
        success = success if success is not None else status < 400
        meta = meta or {}
        content = {
            "code": code or status,
            "success": success,
            "message": message or "",
            "pagination": pagination or {},
            "data": data or [],
            "extra": extra or {},
            "meta": {**meta, **META},
        }
        super().__init__(data=content, status=status, **kwargs)
