from core.authentication import ExistTokenAuthentication
from django.contrib.auth.middleware import get_user
from django.utils.deprecation import MiddlewareMixin


def get_user_token(request):
    user = get_user(request)
    if not user.is_authenticated:
        eta = ExistTokenAuthentication()
        token, user = eta.authenticate(request)
    return user


class TokenAuthenticationMiddleware(MiddlewareMixin):
    def process_request(self, request):
        request.user = get_user_token(request)
