from core.http import JsonResponse
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from packaging import version


class CheckAppVersionMiddleware(MiddlewareMixin):
    """
    will grant access if there is no APP_VERSION in settings
    otherwise if request version coming with APP_VERSION is lower than
    settings, it will return APP_VERSION_MESSAGE or default message to user
    for updating client.
    """

    def process_request(self, request):
        app_version = getattr(settings, "APP_VERSION", None)
        request_version = request.META.get("HTTP_APP_VERSION", "0.0.0")
        if app_version and version.parse(request_version) < version.parse(app_version):
            message = getattr(
                settings,
                "APP_VERSION_MESSAGE",
                "شما از نسخه قدیمی {request_version} برنامه استفاده میکنید، "
                "لطفا نسخه جدید {app_version} را نصب کنید.",
            )
            return JsonResponse(
                status=403,
                message=message.format(app_version=app_version, request_version=request_version),
                data={"app_version": app_version, "request_version": request_version},
            )
