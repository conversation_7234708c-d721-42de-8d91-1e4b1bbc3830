from .formatters import Base<PERSON><PERSON>atter


def get_logging(
    logs,
    path,
    log_class="logging.FileHandler",
    version=1,
    disable_existing_loggers=False,
    handlers_max_bytes=1024 * 1024 * 10,
    handlers_back_up_count=20,
    log_database=False,
    log_template=False,
    graylog_host="0.0.0.0",
    graylog_port=12202,
):
    base_handler = {
        "level": "DEBUG",
        "class": log_class,
        "formatter": "base",
    }
    if log_class == "logging.handlers.RotatingFileHandler":
        base_handler.update(
            {
                "maxBytes": handlers_max_bytes,
                "backupCount": handlers_back_up_count,
            }
        )
    logging = {
        "version": version,
        "disable_existing_loggers": disable_existing_loggers,
        "formatters": {
            "base": {
                "()": BaseFormatter,
                "format": "[{asctime}] [{version}] [SI:{site_id}] [{levelname}] [{type}] [L:{location}] [U:{user}] {message}",
                "style": "{",
                "colorize": False,
            },
            "console": {
                "()": BaseFormatter,
                "format": "[{asctime}] [{version}] [SI:{site_id}] [{levelname}] [U:{user}] {message}",
                "style": "{",
            },
            "not_colorized_console": {
                "()": BaseFormatter,
                "format": "[{asctime}] [{version}] [SI:{site_id}] [{levelname}] [U:{user}] {message}",
                "style": "{",
                "colorize": False,
            },
        },
        "handlers": {
            "console": {
                "level": "DEBUG",
                "class": "logging.StreamHandler",
                "formatter": "console",
            },
            "graylog": {
                "level": "DEBUG",
                "class": "graypy.GELFUDPHandler",
                "formatter": "base",
                "host": graylog_host,
                "port": graylog_port,
            },
            "matrix": {
                **base_handler,
                "level": "WARNING",
                "formatter": "not_colorized_console",
                "filename": "{}/matrix.log".format(path),
            },
            "graylog_matrix": {
                "level": "WARNING",
                "class": "graypy.GELFUDPHandler",
                "formatter": "not_colorized_console",
                "host": graylog_host,
                "port": graylog_port,
            },
        },
        "loggers": {
            "django": {
                "handlers": ["matrix", "graylog_matrix", "console"],
                "level": log_database and "DEBUG" or "INFO",
                "propagate": True,
            },
            "django.template": {
                "level": log_template and "DEBUG" or "INFO",
            },
        },
    }

    for log in logs:
        logging["handlers"].update({log: {**base_handler, "filename": "{}/{}.log".format(path, log)}})
        logging["loggers"].update({log: {"handlers": [log, "graylog"], "level": "INFO"}})

    return logging
