import inspect
import logging

from django.conf import settings
from django.core.management.color import color_style


class BaseFormatter(logging.Formatter):
    default_time_format = "%Y-%m-%d %H:%M:%S"

    def __init__(self, *args, **kwargs):
        self.styles = color_style()
        self.colorize = kwargs.pop("colorize", True)
        super().__init__(*args, **kwargs)

    def set_version(self, record):
        record.version = settings.VERSION
        return record

    def set_site_id(self, record):
        record.site_id = settings.SITE_ID
        return record

    def set_server_time(self, record):
        if self._fmt.find("{server_time}") >= 0 and not hasattr(record, "server_time"):
            record.server_time = self.formatTime(record, self.datefmt)

        return record

    def set_color(self, record):
        msg = record.msg
        status_code = getattr(record, "status_code", None)

        if status_code:
            if 200 <= status_code < 300:
                msg = self.styles.HTTP_SUCCESS(msg)
            elif 100 <= status_code < 200:
                msg = self.styles.HTTP_INFO(msg)
            elif status_code == 304:
                msg = self.styles.HTTP_NOT_MODIFIED(msg)
            elif 300 <= status_code < 400:
                msg = self.styles.HTTP_REDIRECT(msg)
            elif status_code == 404:
                msg = self.styles.HTTP_NOT_FOUND(msg)
            elif 400 <= status_code < 500:
                msg = self.styles.HTTP_BAD_REQUEST(msg)
            else:
                msg = self.styles.HTTP_SERVER_ERROR(msg)

        record.msg = msg
        return record

    def set_type(self, record):
        if not hasattr(record, "type"):
            location = getattr(record, "location", None)
            if location:
                if "ENV." in location:
                    record.type = "ENV"
                elif ".api." in location:
                    record.type = "API"
                elif ".serializers." in location:
                    record.type = "SER"
                elif ".models." in location:
                    record.type = "MOD"
                elif ".managers." in location:
                    record.type = "MAN"
                elif ".commands." in location:
                    record.type = "COM"
                elif ".scripts." in location:
                    record.type = "SCR"
                elif ".settings." in location:
                    record.type = "SET"
                elif ".utils." in location:
                    record.type = "UTI"
                else:
                    record.type = None
            else:
                record.type = None
        return record

    def get_clss_name(self, clss):
        try:
            return clss.__class__.__name__
        except Exception as e:
            return None

    def set_location(self, record):
        if not hasattr(record, "location"):
            _location = record.pathname.replace(str(settings.BASE_DIR) + "/", "")
            _location = _location.replace(".py", "").replace("/", ".")
            if ".site-packages." in _location:
                index = _location.find(".site-packages.")
                _location = "ENV." + _location[index + 15 :]
            record.location = _location

        clss_name = None
        if hasattr(record, "clss"):
            if isinstance(record.clss, str):
                clss_name = record.clss
            else:
                clss_name = self.get_clss_name(record.clss)
        if not clss_name:
            clss = inspect.stack()[11].frame.f_locals.get("self")
            if clss:
                clss_name = self.get_clss_name(clss)
        if clss_name:
            record.location = f"{record.location}.{clss_name}"

        record.location = f"{record.location}.{record.funcName}"
        return record

    def get_request_user(self, request):
        try:
            return request.user
        except Exception as e:
            return None

    def set_user(self, record):
        if not hasattr(record, "user"):
            request = getattr(record, "request", None)
            if request:
                user = self.get_request_user(request)
                if user:
                    record.user = user
                    return record
            request = inspect.stack()[11].frame.f_locals.get("request")
            if request:
                user = self.get_request_user(request)
                if user:
                    record.user = user
                    return record

            record.user = None
        return record

    def handle_drf_exc(self, record):
        record.type = "API"
        record.user = record.request.user
        record.location = f"{record.clss.__module__}.{record.clss.__class__.__name__}.{record.request.method.lower()}"
        return record

    def set_data(self, record):
        if hasattr(record, "drf"):
            return self.handle_drf_exc(record)

        record = self.set_location(record)
        record = self.set_user(record)
        record = self.set_type(record)
        return record

    def format(self, record):
        if self.colorize:
            record = self.set_color(record)
        record = self.set_version(record)
        record = self.set_site_id(record)
        record = self.set_server_time(record)
        record = self.set_data(record)
        if not record.user:
            record.user = "AnonymousUser"
        return super().format(record)
