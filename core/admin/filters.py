from django.contrib.admin import SimpleListFilter
from django.utils.encoding import force_text


class DefaultValueFilter(SimpleListFilter):
    title = None
    parameter_name = None

    all_value = "all"
    all_display = "All"
    default_value = "all"

    def queryset(self, request, queryset):
        if self.parameter_name in request.GET:
            if request.GET[self.parameter_name] == self.all_value:
                return queryset
            return queryset.filter(**{self.parameter_name: request.GET[self.parameter_name]})
        return queryset.filter(**{self.parameter_name: self.default_value})

    def choices(self, cl):
        yield {
            "selected": self.value() == self.all_value,
            "query_string": cl.get_query_string({self.parameter_name: self.all_value}, []),
            "display": self.all_display,
        }
        for lookup, title in self.lookup_choices:
            yield {
                "selected": self.value() == force_text(lookup)
                or (self.value() == None and force_text(self.default_value) == force_text(lookup)),
                "query_string": cl.get_query_string({self.parameter_name: lookup}, []),
                "display": title,
            }


class YesNoFilter(SimpleListFilter):
    title = None
    parameter_name = None

    def lookups(self, request, model_admin):
        return ("1", "Yes"), ("0", "No")

    def on_yes(self, request, queryset):
        raise NotImplementedError("subclass must implemented on_yes()")

    def on_no(self, request, queryset):
        raise NotImplementedError("subclass must implemented on_no()")

    def queryset(self, request, queryset):
        if self.value() == "1":
            return self.on_yes(request, queryset)
        elif self.value() == "0":
            return self.on_no(request, queryset)


class YesNoDefaultValueFilter(DefaultValueFilter, YesNoFilter):
    pass


class ValueFilter(SimpleListFilter):
    title = None
    parameter_name = None
    values = []

    def lookups(self, request, model_admin):
        if not self.values:
            raise NotImplementedError("subclass must set values")
        if isinstance(self.values[0], (list, tuple)):
            return self.values
        return ((x, x) for x in self.values)

    def on_value(self, request, queryset, value):
        raise NotImplementedError("subclass must implemented on_value()")

    def queryset(self, request, queryset):
        if self.value():
            return self.on_value(request, queryset, self.value())


class KeyValueFilter(ValueFilter):
    key = None

    def on_value(self, request, queryset, value):
        return queryset.filter(**{self.key: value})


class PriceFilter(KeyValueFilter):
    values = [500000, 1000000, 3000000, 7000000]

    def lookups(self, request, model_admin):
        return ((x, "{:,}".format(x)) for x in self.values)
