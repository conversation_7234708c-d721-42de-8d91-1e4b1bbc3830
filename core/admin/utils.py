from core.templates import link


def get_display(field, name, comma=False, default="-", func=None):
    def _func(cls, obj):
        try:
            data = obj.getattribute(field)
            if callable(data):
                data = data()
            if func:
                data = func(data)
            if comma:
                return "{:,}".format(data)
            return data
        except Exception as e:
            return default

    _func.short_description = name
    return _func


def get_object_link(field, name=None, value=None, default="-"):
    def _func(cls, obj):
        try:
            _obj = obj if field == "self" else obj.getattribute(field)
            _func.short_description = name or _obj._meta.model_name
            return link(_obj.get_admin_change(), value and _obj.getattribute(value) or _obj)
        except Exception as e:
            return default

    return _func


def get_link(href, name, title=None, default="-"):
    def _func(cls, obj):
        try:
            _href = href.format(obj=obj)
            return link(_href, title or _href)
        except Exception as e:
            return default

    _func.short_description = name
    return _func
