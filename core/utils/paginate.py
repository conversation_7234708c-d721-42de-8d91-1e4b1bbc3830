from django.core.paginator import EmptyPage, InvalidPage, Paginator
from unidecode import unidecode


def paginate(
    data,
    page=None,
    limit=None,
    max_limit=100,
    url="",
    serializer=None,
    serializer_kwargs=None,
    default_last=False,
    fix_out_range=False,
):
    next_page_url, previous_page_url, next_page, previous_page, count, pages = None, None, None, None, 0, 0
    limit = int(unidecode(str(limit))) if limit else 10
    if limit > max_limit:
        limit = max_limit
    try:
        data = Paginator(data, limit)
        pages = data.num_pages
        count = data.count
        if not page:
            page = pages if default_last else 1
        else:
            page = int(unidecode(str(page)))
            if page < 1 and fix_out_range:
                page = 1
            if page > pages and fix_out_range:
                page = pages
        data = data.page(page)
        if data.has_next():
            next_page_url = "{}&page={}&limit={}".format(url, page + 1, limit)
            next_page = page + 1
        if data.has_previous():
            previous_page_url = "{}&page={}&limit={}".format(url, page - 1, limit)
            previous_page = page - 1
        if serializer:
            serializer_kwargs = serializer_kwargs if serializer_kwargs else {}
            data = serializer(data, many=True, **serializer_kwargs).data
        else:
            data = list(data)
    except EmptyPage or InvalidPage or ValueError:
        data = []
    return {
        "data": data,
        "pagination": {
            "count": count,
            "pages": pages,
            "page": page,
            "limit": limit,
            "next": next_page,
            "next_url": next_page_url,
            "previous": previous_page,
            "previous_url": previous_page_url,
        },
    }
