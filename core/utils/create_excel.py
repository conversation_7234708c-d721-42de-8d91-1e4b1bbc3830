import openpyxl
import logging
from pathlib import Path
from openpyxl.workbook import Workbook
from openpyxl.styles import Font, Alignment, Border, Side
from django.conf import settings
from typing import List, Dict, Any

logger = logging.getLogger("core")


def create_excel_file(headers: List[str], data: List[Dict[str, Any]], filepath: str, title: str) -> None:
    """
    Creates an Excel file with a defined set of columns and populates it with data.

    Args:
        headers (List[str]): A list of strings representing the column headers.
                               The order in this list determines the column order in the Excel file.
        data (List[Dict[str, Any]]): A list of dictionaries, where each dictionary represents a row.
                                     The keys of the dictionary should match the headers.
        filepath (str): The path of the file to save.

        title (str): The title of the Excel file.
    """
    # 1. Create a new workbook and select the active worksheet
    wb = Workbook()
    sheet = wb.active
    sheet.title = title

    # --- Styling (Optional but Recommended) ---
    header_font = Font(bold=True, color="FFFFFF", name="<PERSON><PERSON><PERSON>")
    header_fill = openpyxl.styles.PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid")
    center_align = Alignment(horizontal='center', vertical='center')
    thin_border = Border(left=Side(style='thin'),
                         right=Side(style='thin'),
                         top=Side(style='thin'),
                         bottom=Side(style='thin'))

    # 2. Write the header row and apply styles
    sheet.append(headers)
    for cell in sheet[1]:  # Get all cells in the first row
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_align
        cell.border = thin_border

    # 3. Append data rows
    # The `headers` list is used to ensure the data is written in the correct column order.
    for row_data in data:
        # Create an ordered list of values based on the header order
        ordered_row_values = [row_data.get(header, "") for header in headers]
        sheet.append(ordered_row_values)

    # --- Auto-adjust column widths (Optional) ---
    for column_cells in sheet.columns:
        # Find the length of the longest cell in the column
        max_length = 0
        column = column_cells[0].column_letter  # Get the column letter (e.g., 'A', 'B')
        for cell in column_cells:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        # Add a little padding to the width
        adjusted_width = (max_length + 2)
        sheet.column_dimensions[column].width = adjusted_width

    # 4. Save the workbook to a file
    try:
        filepath = Path.joinpath(settings.MEDIA_ROOT, filepath)
        wb.save(filepath)
    except Exception as e:
        logger.error("CREATE FILE ERROR {}".format(e))
        raise e
