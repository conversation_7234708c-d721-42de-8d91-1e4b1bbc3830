from django.http import HttpResponse
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, colors
from openpyxl.styles.borders import Border, Side


class Style:
    pattern_fill = PatternFill
    colors = colors
    thin_border = Border(
        left=Side(style="thin"), right=Side(style="thin"), top=Side(style="thin"), bottom=Side(style="thin")
    )

    def font(**kwargs):
        # kwargs["name"] = kwargs.get("name", "Tahoma")
        kwargs["name"] = kwargs.get("name", "Arial")
        kwargs["size"] = kwargs.get("size", 10)
        return Font(**kwargs)


class Excel(Workbook):
    style = Style

    def insert_excel(self, sheet, row, column, value, color=None, bold=False):
        if color:
            sheet.cell(row=row, column=column).fill = color
        sheet.cell(row=row, column=column).font = self.style.font(bold=bold)
        sheet.cell(row=row, column=column).border = self.style.thin_border
        sheet.cell(row=row, column=column).value = value

    def write(self, sheet, row, column, value, **kwargs):
        cell = sheet.cell(row=row, column=column)
        cell.value = value
        cell.border = self.style.thin_border
        for k, v in kwargs.items():
            cell.__setattr__(k, v)

    def http_response(self, file_name):
        response = HttpResponse(content_type="application/vnd.ms-excel")
        response["Content-Disposition"] = "attachment; filename={}".format(file_name)
        self.save(response)
        return response
