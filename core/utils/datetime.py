import datetime

import jdatetime
import pytz
from django.db.models import Case, IntegerField, Value, When


def extract_jalali_months(period, field, end_date=None, df="%Y-%m-%dT%H:%M"):
    if not end_date:
        end_date = jdatetime.date.today()
    elif isinstance(end_date, str):
        try:
            end_date = jdatetime.datetime.strptime(end_date, df)
        except Exception as e:
            end_date = jdatetime.date.today()
    dates = []
    gte_month = None
    for i in range(11, -2, -1):
        month = (end_date.replace(day=15) - jdatetime.timedelta(days=i * 30)).replace(day=1)
        if gte_month:
            dates.append(
                (
                    {
                        "{}__gte".format(field): gte_month.togregorian(),
                        "{}__lt".format(field): month.togregorian(),
                    },
                    gte_month.month,
                )
            )
        gte_month = month
    return (
        {period: Case(*[When(**i[0], then=Value(i[1])) for i in dates], output_field=IntegerField())},
        dates[0][0]["{}__gte".format(field)],
        dates[-1][0]["{}__lt".format(field)],
        gte_month.month,
    )


def extract_jalali_days(period, field, end_date=None, df="%Y-%m-%dT%H:%M"):
    dates = []
    if not end_date:
        end_date = jdatetime.datetime.now(tz=pytz.timezone("Asia/Tehran"))
    elif isinstance(end_date, str):
        try:
            end_date = pytz.utc.localize(jdatetime.datetime.strptime(end_date, df))
        except Exception as e:
            end_date = jdatetime.datetime.now(tz=pytz.timezone("Asia/Tehran"))
    day_date = (end_date - jdatetime.timedelta(days=13)).replace(hour=0, minute=0, second=0, microsecond=0)
    for i in range(0, 14):
        next_day = day_date + jdatetime.timedelta(days=1)
        dates.append(
            (
                {
                    "{}__gte".format(field): day_date.togregorian().astimezone(pytz.utc),
                    "{}__lt".format(field): next_day.togregorian().astimezone(pytz.utc),
                },
                i,
            )
        )
        day_date = next_day

    dates[-1][0]["{}__lt".format(field)] = end_date.togregorian().astimezone(pytz.utc)
    return (
        {period: Case(*[When(**i[0], then=Value(i[1])) for i in dates], output_field=IntegerField())},
        dates[0][0]["{}__gte".format(field)],
        dates[-1][0]["{}__lt".format(field)],
        day_date.weekday(),
    )


def extract_jalali_dates(period, field, end_date=None, df="%Y-%m-%dT%H:%M"):
    if period == "day":
        return extract_jalali_days(period, field, end_date, df)
    return extract_jalali_months(period, field, end_date, df)


def get_period_filter(field, from_date, to_date, df="%Y-%m-%dT%H:%M"):
    data = {}
    try:
        data["{}__gte".format(field)] = pytz.utc.localize(jdatetime.datetime.strptime(from_date, df).togregorian())
    except Exception as e:
        pass
    try:
        data["{}__lte".format(field)] = pytz.utc.localize(jdatetime.datetime.strptime(to_date, df).togregorian())
    except Exception as e:
        pass
    return data


def jdt_now_numeric():
    return jdatetime.datetime.now().strftime("%Y%m%d%H%M%S")


def datetime_from_string(date_string, string_format, input_type="j", output_type="g", localize=True):
    if input_type == "j":
        dt = jdatetime.datetime.strptime(date_string, string_format)
        if output_type == "g":
            dt = dt.togregorian()

    else:
        dt = datetime.datetime.strptime(date_string, string_format)
        if output_type == "j":
            dt = jdatetime.datetime.fromgregorian(datetime=dt)

    if localize:
        return pytz.utc.localize(dt)
    return dt
