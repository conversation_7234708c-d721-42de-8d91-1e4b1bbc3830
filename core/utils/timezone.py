import pytz
from django.utils.timezone import *
from jdatetime import datetime as jdatetime

iran = pytz.timezone("Asia/Tehran")
default_date_format = "%Y-%m-%d"
default_time_format = "%H:%M"
default_datetime_format = "{} {}".format(default_date_format, default_time_format)


def utc_to_iran(dt):
    return dt.astimezone(iran)


def utc_to_iran_jalali(dt):
    return jdatetime.fromgregorian(datetime=utc_to_iran(dt))


def utc_to_iran_jalali_string(dt, fmt=default_datetime_format):
    return utc_to_iran_jalali(dt).strftime(fmt)


def iran_to_utc(dt):
    return iran.localize(dt).astimezone(None)


def iran_jalali_to_utc(dt):
    return iran_to_utc(dt.togregorian())


def iran_jalali_string_to_utc(dt, fmt=default_datetime_format):
    return iran_jalali_to_utc(jdatetime.strptime(dt, fmt))
