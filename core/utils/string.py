def remove_prefix(string, prefix):
    string = str(string)
    prefix = str(prefix)

    if prefix and string.startswith(prefix):
        prefix_length = len(prefix)
        return string[prefix_length:]
    return string


def remove_suffix(string, suffix):
    string = str(string)
    suffix = str(suffix)

    if suffix and string.endswith(suffix):
        suffix_length = len(suffix)
        return string[:-suffix_length]
    return string
