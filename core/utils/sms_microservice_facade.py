import logging
import os

from gql import Client, gql
from gql.transport.exceptions import TransportQueryError
from gql.transport.requests import RequestsHTTPTransport

logger = logging.getLogger("agent")


class SMSServiceFacade:
    API_URL = 'https://dev.sms.garajmall.com/graphql/'
    refresh_token = os.getenv("SMS_FACADE_REFRESH_TOKEN", "")
    access_token = None
    rotated_recently = False

    def execute(self, request_string, variable_values=None, with_auth_header: bool = True):
        query = gql(request_string)

        transport = RequestsHTTPTransport(url=self.API_URL)
        if with_auth_header:
            if self.access_token is None:
                self.rotate_access_token()

            transport.headers = {
                'Authorization': f'Bearer {self.access_token}'
            }

        # Create a GraphQL client using the defined transport
        client = Client(transport=transport, fetch_schema_from_transport=True)

        try:
            # Execute the query on the transport
            result = client.execute(query, variable_values=variable_values)
            return result
        except TransportQueryError as e:
            message = e.errors[0]['message']
            if message == 'Signature has expired':
                if self.rotated_recently:
                    raise e

                self.rotate_access_token()
                self.rotated_recently = True

                result = self.execute(request_string, variable_values, with_auth_header)

                self.rotated_recently = False

                return result

            raise e

    def rotate_access_token(self):
        access_token = self.execute('''
mutation ($refreshToken: String!) {
  rotateAccessToken(
    refreshToken: $refreshToken
  ) {
    accessToken
  }
}
''', {
            'refreshToken': self.refresh_token
        }, False)['rotateAccessToken']['accessToken']
        self.access_token = access_token
        return access_token

    def ping(self):
        return self.execute('''
{
    ping
}
''', with_auth_header=False)['ping']

    def get_message_request(self, _id: int):
        if not isinstance(_id, int):
            _id = int(_id)

        return self.execute('''
query ($id: Int!) {
  messageRequest(id: $id) {
    id
    text
    phoneNumber
    status
  }
}
''', {
            'id': _id
        })['messageRequest']

    def get_all_message_requests(self):
        return self.execute('''
{
  messageRequests {
    id
    text
    phoneNumber
    status
  }
}
''')['messageRequests']

    def create_message_request(self, text, phone_number, messaging_service=None):
        try:
            response = self.execute('''
    mutation ($text: String!, $phoneNumber: String!,  $messagingService:String) {
      createMessageRequest(input: {    
        phoneNumber:$phoneNumber,
        text:$text,
        messagingService:$messagingService
        }) {
        messageRequest {
          id
          status
        }
      }
    }
    ''', {
                'text': text,
                'phoneNumber': phone_number,
                "messagingService": messaging_service
            })
        except Exception as e:
            logger.error(e)
            return "", False

        if response['createMessageRequest'].get('messageRequest', {}).get("status", False) == "SUCCEEDED":
            return response, True
        return response, False
