class ImportExcel:
    def __init__(self, model, columns):
        self.model = model
        self.columns = columns

    def generate_message(self, row, column, value, messages):
        if not isinstance(messages, list) or isinstance(messages, tuple):
            messages = [messages]
        return {
            "row": row,
            "column": column,
            "value": value,
            "messages": messages,
        }

    def clean_field(self, obj, value, sheet, row, column, field_name):
        try:
            clean_field = self.__getattribute__("clean_{}".format(field_name))
        except Exception as e:
            return value
        if clean_field and callable(clean_field):
            return clean_field(obj, value, sheet, row, column, field_name)
        return value

    def get_column_data(self, obj, sheet, row, column, field_name):
        field = self.model._meta.get_field(field_name)
        raw_value = sheet.cell(row=row, column=column).value
        value = raw_value

        try:
            value = self.clean_field(obj, value, sheet, row, column, field_name)
        except Exception as e:
            try:
                return field, value, self.generate_message(row, column, raw_value, e.messages)
            except:
                return field, value, self.generate_message(row, column, raw_value, str(e))

        try:
            value = field.clean(value, obj)
        except Exception as e:
            return field, value, self.generate_message(row, column, raw_value, e.messages)

        return field, value, None

    def set_obj_data(self, obj, sheet, row):
        m2m_fields = []
        messages = []
        for field_name, column in self.columns.items():
            field, value, message = self.get_column_data(obj, sheet, row, column, field_name)
            if message:
                messages.append(message)
                break
            if field.many_to_many:
                m2m_fields.append((field_name, value))
            else:
                setattr(obj, field_name, value)

        return messages, m2m_fields

    def save_obj(self, obj, row):
        try:
            created_count = 0
            updated_count = 0
            if obj.pk:
                updated_count = 1
            else:
                created_count = 1
            obj.save()
            return None, created_count, updated_count
        except Exception as e:
            return self.generate_message(row, 0, "OBJ", ["خطا در ذخیره سازی کالا", str(e)]), 0, 0

    def set_m2m_data(self, obj, sheet, row, m2m_fields):
        messages = []
        for field_name, value in m2m_fields:
            try:
                obj.__getattribute__(field_name).set(value)
            except Exception as e:
                column = self.columns.get(field_name)
                raw_value = sheet.cell(row=row, column=column).value
                messages.append(self.generate_message(row, column, raw_value, ["خطا در ذخیره سازی", str(e)]))

        return messages
