import hashlib
from io import Bytes<PERSON>
from random import choice, randint

from core.cache import cache
from core.utils import Base64
from PIL import Image, ImageDraw, ImageFont


def random_color(color, base_color, base_multiplier=3):
    return choice((color, *(base_color,) * base_multiplier))


def random_position(index):
    positions = [(10, 18), (30, 38), (50, 58), (72, 80)]
    return (randint(*positions[index]), randint(-4, 6))


def base64_image(image):
    buffer = BytesIO()
    image.save(buffer, "JPEG")
    return Base64.from_file(buffer.getvalue())


def create_image(
    code,
    width=None,
    height=None,
    font=None,
    font_color=None,
    font_size=None,
    color=None,
    base_color=None,
    base_multiplier=None,
):
    width = int(width) or 105
    height = int(height) or 35
    font = font or "core/static/core/fonts/yekan/iranyekanwebregularfanum.ttf"
    font_color = font_color or "#17245f"
    font_size = int(font_size) or 26
    color = color or "#06df70"
    base_color = base_color or "#ffffff"
    base_multiplier = int(base_multiplier) or 3

    image = Image.new("RGB", (width, height), base_color)
    font = ImageFont.truetype(font, font_size)
    draw = ImageDraw.Draw(image)

    for x in range(width):
        for y in range(height):
            draw.point(
                (x, y),
                fill=random_color(
                    color=color,
                    base_color=base_color,
                    base_multiplier=base_multiplier,
                ),
            )

    for i in range(4):
        draw.text(random_position(i), code[i], font=font, fill=font_color)

    for i in range(-50, 150, 5):
        draw.line((i, 0, 0, i), fill=color, width=1)
        draw.line((i, 0, 150, 150 - i), fill=color, width=1)

    img_str = base64_image(image)
    hash_code = hashlib.sha3_256(code.encode()).hexdigest()
    return {"img_str": img_str, "hash_code": hash_code}


def check_captcha(uuid, code):
    hash_code = cache.get(uuid)
    cache.delete(uuid)
    return hashlib.sha3_256(str(code).encode()).hexdigest() == hash_code
