from constants import SMSType
from notification.models import SMS

import celery

logger = celery.utils.log.get_logger("celery")


class AbstractBaseCelery(celery.Task):
    def run(self, *args, **kwargs):
        super().run(*args, **kwargs)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        logger.error("message={}".format(einfo))
        text = "FAILURE CELERY TASK:\n{}\nCELERY ID:\n{}\nPLEASE CHECK LOGS".format(self.name, task_id)
        SMS.objects.create(user_id=1115, text=text, type=SMSType.ADMIN, send_signal=True)
