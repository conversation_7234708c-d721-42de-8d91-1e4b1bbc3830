from rest_framework_simplejwt.tokens import AccessToken
from rest_framework.authentication import Base<PERSON><PERSON><PERSON>ication
from rest_framework.exceptions import AuthenticationFailed
from rest_framework_simplejwt.authentication import JWTAuthentication
from accounts.models import User





class CookieJWTAuthentication(JWTAuthentication):

    def authenticate(self, request):

        access_token = request.COOKIES.get('agent_access_token')


        if not access_token:
            return None
        

        try:
            user_id = AccessToken(access_token)['user_id']
            user = User.objects.get(id=user_id)

            return user, None


        except Exception as e:
            raise AuthenticationFailed("Invalid or expired access token")
        




