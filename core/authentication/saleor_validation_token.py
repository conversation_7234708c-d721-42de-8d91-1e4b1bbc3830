from django.contrib.auth.models import AnonymousUser
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.exceptions import AuthenticationFailed, PermissionDenied
from accounts.models import User
from saleor.utils import Saleor
from saleor.utils import get_mobile_number
import logging

logger = logging.getLogger("service_center")


def get_site(request):
    domain = request.headers.get("Origin").replace('http://', '').replace('https://', '')
    domain_map = {
        "garajmall": "gmall",
        "higaraj": "hig",
    }
    for keyword, site in domain_map.items():
        if keyword in domain:
            return site , domain
    raise AuthenticationFailed("Authentication Failed")


class CustomAuthentication(JWTAuthentication):

    def authenticate(self, request):
        token = request.headers.get("Authorization")
        if token:
            token = token.replace("Bearer ", "")
            saleor = Saleor(site="gmall")
            is_valid, email = saleor.token_verify(token=token)
            if is_valid:
                return User.objects.only("id").get(username=get_mobile_number(email)), token
            else:
                raise AuthenticationFailed("Authentication Failed")
        else:
            raise AuthenticationFailed("Authentication Failed")


class NotifCustomAuthentication(JWTAuthentication):

    def authenticate(self, request):
        token = request.headers.get("Authorization")
        site, domain = get_site(request)
        request.domain = domain
        if token:
            token = token.replace("Bearer ", "")
            saleor = Saleor(site=site)
            is_valid, email = saleor.token_verify(token=token)
            if is_valid:
                mobile_number = get_mobile_number(email)
                return User.objects.only("id").get_or_create(
                    username=mobile_number,
                    defaults={
                        'country_code': mobile_number[:4],
                    }
                )[0], token
            else:
                raise AuthenticationFailed("Authentication Failed")
        else:
            return AnonymousUser(), None
