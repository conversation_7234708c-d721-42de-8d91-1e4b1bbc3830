from rest_framework.exceptions import AuthenticationFailed
from rest_framework_simplejwt.authentication import JW<PERSON>uthentication
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.authentication import CSR<PERSON><PERSON>ck
from rest_framework import exceptions


# def enforce_csrf(request):
#     """
#     Enforce CSRF validation.
#     """
#     check = CSRFCheck()
#     check.process_request(request)
#     reason = check.process_view(request, None, (), {})
#     if reason:
#         # CSRF failed, bail with explicit error message
#         raise exceptions.PermissionDenied('CSRF Failed: %s' % reason)


class CooKieAuthentication(JWTAuthentication):

    def authenticate(self, request):
        access_token = request.COOKIES.get("access_token")
        refresh_token = request.COOKIES.get("refresh_token")
        new_access_token = None

        if access_token is None:
            return None

        try:
            validated_token = self.get_validated_token(access_token)
            # enforce_csrf(request)
            return self.get_user(validated_token), validated_token

        except InvalidToken or TokenError:

            if refresh_token is None:
                raise None
            try:
                refresh = RefreshToken(refresh_token)
                new_access_token = str(refresh.access_token)

                validated_token = self.get_validated_token(new_access_token)
                # enforce_csrf(request)
                # for accessing request in middleware we should assign value to _request
                request._request.new_access_token = new_access_token

                return self.get_user(validated_token), validated_token

            except TokenError:
                raise None
