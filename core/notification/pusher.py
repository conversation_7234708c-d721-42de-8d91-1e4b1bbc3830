import pusher
import os
import settings

PUSHER_CONFIG = {
    "app_id": settings.PUSHER_APP_ID,
    "key": settings.PUSHER_KEY,
    "secret": settings.PUSHER_SECRET,
    "cluster": settings.PUSHER_CLUSTER,
}


class PusherInterface:
    def __init__(self):
        self.pusher_client = pusher.Pusher(**PUSHER_CONFIG)

    def trigger_notification(self, channel_name: str, event_name: str, message: str):
        """
        :param channel_name : channel name in pusher its default by system base on public/private notif
        :param message: message of notification
        :param event_name: name of event that we want to trigger
        """

        try:
            self.pusher_client.trigger(
                channel_name,
                event_name,
                message,
            )
        except Exception as e:
            raise Exception(
                f"Error occurred while triggering notification in event : {event_name} and error is \n{e}"
            )

    def authenticate_pusher(self, channel_name: str, socket_id: str):
        try:
            payload = self.pusher_client.authenticate(
                channel=channel_name,
                socket_id=socket_id,
            )
            return payload

        except Exception as e:
            raise Exception(
                f"error occurred while authenticating user to pusher error message is : \n{e}"
            )
