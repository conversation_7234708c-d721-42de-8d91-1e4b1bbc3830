import logging

from pusher_push_notifications import PushNotifications
import os
import settings

BEAM_CONFIGS = {
    "instance_id": settings.BEAM_INSTANCE_ID,
    "secret_key": settings.BEAM_SECRET_KEY,
}

logger = logging.getLogger("notification")


class BeamsInterface:
    def __init__(self):
        self.beams_client = PushNotifications(**BEAM_CONFIGS)

    def trigger_notification(self, message: str, site, users=None, interests=None, data=None, notif_id=None):
        deep_link = "https://" + site.domain + "/notifications?id=" + str(notif_id)
        icon, title = "", ""
        if site.name == "hig" or site.name == "tasleem":
            icon = "https://core.higaraj.com/media/configs/logo76-hig.png"
            title = "HiGaraj"
        if site.name == "gmall":
            icon = "https://core.higaraj.com/media/configs/logo76-gmall.png"
            title = "GarajMall"


        try:
            if users:
                response = self.beams_client.publish_to_users(
                    user_ids=users,
                    publish_body={
                        'web': {
                            'notification': {
                                'title': title,
                                'body': message,
                                'deep_link': deep_link,
                                'icon': icon

                            },
                            'data': data
                        },
                    },
                )
                return response
            response = self.beams_client.publish_to_interests(
                interests=interests,
                publish_body={
                    'web': {
                        'notification': {
                            'title': title,
                            'body': message,
                            'deep_link': deep_link,
                            'icon': icon

                        },
                        'data': data
                    },
                },

            )
            return response
        except Exception as e:
            logger.error(
                f"Error occurred while triggering notification in event and error is {e}"
            )
            raise Exception(
                f"Error occurred while triggering notification in event and error is \n{e}"
            )

    def authenticate_beams(self, user_id: str):
        try:
            payload = self.beams_client.generate_token(user_id)
            return payload

        except Exception as e:
            logger.error(
                f"Error occurred while authenticating  user to beams error message is {e}"
            )
            raise Exception(
                f"error occurred while authenticating user to beams error message is : \n{e}"
            )
