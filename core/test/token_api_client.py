from django.contrib.auth import get_user_model
from django.db import transaction
from django.shortcuts import reverse
from rest_framework.authtoken.models import Token
from rest_framework.test import APIClient as BaseAPIClient

User = get_user_model()


class TokenAPIClient(BaseAPIClient):
    def __init__(self, *args, **kwargs):
        with transaction.atomic():
            self.active_superuser = User.objects.get_or_create(
                is_superuser=True, is_staff=True, is_active=True, username="active_superuser"
            )[0]
            self.inactive_superuser = User.objects.get_or_create(
                is_superuser=True, is_staff=True, is_active=False, username="inactive_superuser"
            )[0]
            self.active_staff = User.objects.get_or_create(
                is_superuser=False, is_staff=True, is_active=True, username="active_staff"
            )[0]
            self.inactive_staff = User.objects.get_or_create(
                is_superuser=False, is_staff=True, is_active=False, username="inactive_staff"
            )[0]
            self.active_user = User.objects.get_or_create(
                is_superuser=False, is_staff=False, is_active=True, username="active_user"
            )[0]
            self.inactive_user = User.objects.get_or_create(
                is_superuser=False, is_staff=False, is_active=False, username="inactive_user"
            )[0]
        super().__init__(*args, **kwargs)

    def login(self, url="accounts:v1:login", key="token", **credentials):
        try:
            url = reverse(url)
        except Exception as e:
            pass
        response = self.post(path=url, data=credentials, format="json")
        if response.status_code == 200:
            try:
                self.credentials(HTTP_AUTHORIZATION="{0} {1}".format(key, response.json()["data"][key]))
            except Exception as e:
                pass
        return response

    def set_token(self, user):
        self.credentials(HTTP_AUTHORIZATION="token {}".format(Token.objects.get_or_create(user=user)[0]))

    def use_active_superuser(self):
        self.set_token(self.active_superuser)

    def use_inactive_superuser(self):
        self.set_token(self.inactive_superuser)

    def use_active_staff(self):
        self.set_token(self.active_staff)

    def use_inactive_staff(self):
        self.set_token(self.inactive_staff)

    def use_active_user(self):
        self.set_token(self.active_user)

    def use_inactive_user(self):
        self.set_token(self.inactive_user)
