from django.contrib.contenttypes.models import ContentType
from django.db.models import Model as DjangoModel
from django.urls import reverse

from .queryset import QuerySet


class Model(DjangoModel):
    objects = QuerySet.as_manager()

    def getattribute(self, filed):
        obj = self
        for key in filed.split("__"):
            obj = obj.__getattribute__(key)
        return obj

    def get_admin_change(self):
        return reverse("admin:{}_{}_change".format(self._meta.app_label, self._meta.model_name), args=(self.pk,))

    def db_update(self, using=None, refresh_from_db=False, **kwargs):
        self.__class__._base_manager.db_manager(using, hints={"instance": self}).filter(pk=self.pk).update(**kwargs)
        if refresh_from_db:
            self.refresh_from_db()

    def self_queryset(self):
        return self.__class__._base_manager.filter(pk=self.pk)

    def self_select_for_update(self):
        return self.self_queryset().select_for_update().get()

    @classmethod
    def get_admin_changelist(cls):
        return reverse("admin:{}_{}_changelist".format(cls._meta.app_label, cls._meta.model_name))

    @classmethod
    def get_admin_add(cls):
        return reverse("admin:{}_{}_add".format(cls._meta.app_label, cls._meta.model_name))

    @classmethod
    def get_content_type(cls):
        return ContentType.objects.get_for_model(cls)

    class Meta:
        abstract = True
