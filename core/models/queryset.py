from datetime import timedelta

import jdatetime
import pytz
from django.db.models import *
from core.cache import get_cache_key, get_hashed_cache_key
from core.models import functions
from core.utils import timezone
from django.db.models import QuerySet as DjangoQuerySet

utc = pytz.utc


class QuerySet(DjangoQuerySet):
    def utcnow(self, jalali=False):
        if jalali:
            return jdatetime.datetime.utcnow().replace(tzinfo=utc)
        return timezone.now()

    def annotate_sum(self, field, filter=None):
        return self.annotate(**{"{}_sum".format(field): Sum(field, filter=filter)})

    def annotate_count(self, field, filter=None):
        return self.annotate(**{"{}_count".format(field): Count(field, filter=filter)})

    def aggregate_sum(self, *fields):
        return self.aggregate(*[Sum(field) for field in fields])

    def get_sum(self, field):
        return self.aggregate_sum(field)["{}__sum".format(field)] or 0

    def annotate_index(self, key, order_by, desc=True):
        if isinstance(order_by, str):
            if desc:
                order_by = F(order_by).desc()
            else:
                order_by = F(order_by).asc()
        return self.annotate(**{key: Window(expression=functions.RowNumber(), order_by=order_by)})

    def annotate_rank(self, key, order_by, desc=True):
        if isinstance(order_by, str):
            if desc:
                order_by = F(order_by).desc()
            else:
                order_by = F(order_by).asc()
        return self.annotate(**{key: Window(expression=functions.Rank(), order_by=order_by)})

    def filter_today(self, key):
        return self.filter(**{"{}__gte".format(key): timezone.now().date()})

    def filter_yesterday(self, key):
        today = timezone.now().date()
        return self.filter(**{"{}__gte".format(key): today - timedelta(days=1), "{}__lt".format(key): today})

    def filter_days_ago(self, key, days):
        return self.filter(**{"{}__gte".format(key): timezone.now().date() - timedelta(days=days)})

    def filter_date(self, key, date):
        if isinstance(date, jdatetime.datetime):
            date = date.togregorian()
        date = date.date()
        return self.filter(**{"{}__gte".format(key): date, "{}__lt".format(key): date + timedelta(days=1)})

    def filter_weeks_ago(self, key, weeks=0, jalali=False):
        if jalali:
            days = jdatetime.datetime.utcnow().replace(tzinfo=utc).weekday()
            date = timezone.now() - timedelta(days=days)
        else:
            now = timezone.now()
            date = now - timedelta(days=now.weekday())
        if weeks > 0:
            return self.filter(
                **{
                    "{}__gte".format(key): date - timedelta(days=7 * weeks),
                    "{}__lt".format(key): date - timedelta(days=7 * (weeks - 1)),
                }
            )
        return self.filter(**{"{}__gte".format(key): date})

    def filter_months_ago(self, key, months=0, jalali=False):
        if jalali:
            date = jdatetime.datetime.utcnow().replace(tzinfo=utc).date()
        else:
            date = timezone.now().date()
        if months > 0:
            date = date.replace(day=15)
            start = (date - timedelta(days=30 * months)).replace(day=1)
            end = (date - timedelta(days=30 * (months - 1))).replace(day=1)
            if jalali:
                start = start.togregorian()
                end = end.togregorian()
            return self.filter(**{"{}__gte".format(key): start, "{}__lt".format(key): end})
        return self.filter(
            **{"{}__gte".format(key): date.replace(day=1).togregorian() if jalali else date.replace(day=1)}
        )

    def get_cached(self, *args, **kwargs):
        print(get_cache_key(self, *args, **kwargs))
        print(get_hashed_cache_key(self, *args, **kwargs))
