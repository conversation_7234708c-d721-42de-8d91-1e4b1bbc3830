from django.utils.safestring import mark_safe


def link_button(url, text, bold=True):
    if bold:
        text = "<b>{}</b>".format(text)
    return mark_safe('<a href="{}"><button type="button" class="button">{}</button></a>'.format(url, text))


def link(url, text, bold=True):
    if bold:
        text = "<b>{}</b>".format(text)
    return mark_safe('<a href="{}">{}</a>'.format(url, text))


def link_image(image, url=None, width=100, height=100):
    url = url if url else image.url
    width = 'width="{}"'.format(width)
    height = 'height="{}"'.format(height)
    return mark_safe('<a href="{}"><img src="{}" {} {}/></a>'.format(url, image.url, width, height))


def colored_text(text, color):
    return mark_safe('<b style="color: {}">{}</b>'.format(color, text))
