from constants import Grade
from core import models


class AgentRegion(models.AbstractBaseModel):
    agent = models.ForeignKey("agent.Agent", on_delete=models.CASCADE, related_name="regions")
    grade = models.CharField(max_length=100, choices=Grade.choices, default=Grade.C)
    region = models.ForeignKey("locations.Region", on_delete=models.PROTECT, related_name="agent_regions")
    is_selected = models.BooleanField(default=False)

    class Meta:
        unique_together = (("agent", "region", "grade"),)
        db_table = "matrix_agent_agent_region"

    def __str__(self):
        return "[{}] {}".format(self.id, self.agent)
