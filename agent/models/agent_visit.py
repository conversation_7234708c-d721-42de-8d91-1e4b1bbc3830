from constants import VisitStatus
from core import models


class AgentVisit(models.AbstractBaseModel):
    status = models.CharField(max_length=100, choices=VisitStatus.choices, default=VisitStatus.VISIT)
    agent = models.ForeignKey("agent.Agent", on_delete=models.PROTECT, related_name="visits")
    service_center = models.ForeignKey("service_center.ServiceCenter", on_delete=models.PROTECT, related_name="visits")
    start_at = models.DateTimeField()
    end_at = models.DateTimeField(null=True, blank=True)
    poll_question = models.ManyToManyField("rates.PollQuestion", related_name="agent_visits", blank=True)
    note = models.TextField(null=True, blank=True)
    test_mode = models.BooleanField(default=False)

    class Meta:
        db_table = "matrix_agent_agent_visit"
