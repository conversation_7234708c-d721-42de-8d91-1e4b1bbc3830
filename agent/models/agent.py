from core import models


class Agent(models.AbstractBaseModel):
    user = models.OneToOneField("accounts.User", on_delete=models.PROTECT, related_name="agent")
    head = models.ForeignKey("agent.Agent", on_delete=models.SET_NULL, null=True, blank=True, related_name="colleagues")
    state = models.ForeignKey("locations.state", on_delete=models.PROTECT, related_name="agents")
    fast_visit = models.BooleanField(default=False)
    check_location = models.BooleanField(default=True)
    test_mode = models.BooleanField(default=False)

    first_login = models.DateTimeField(blank=True, null=True)
    last_login = models.DateTimeField(blank=True, null=True)

    class Meta:
        db_table = "matrix_agent_agent"
        permissions = (
            ("can_update_location_address", "Can Update Location Address"),
        )

    def __str__(self):
        return self.user.username
