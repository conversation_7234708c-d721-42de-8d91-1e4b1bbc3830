from settings.celery_app import celery_app
from agent.models import Agent
import logging
import datetime
import re
from saleor.utils import Saleor

logger = logging.getLogger("saleor")


@celery_app.task(name="check_synced_saleor_addresses", autoregister=True)
def check_synced_saleor_addresses():
    logger.info("CHECK-SYNCED-ADDRESSES| datetime {}".format(str(datetime.datetime.now())))
    agents = Agent.objects.all()
    for agent in agents:
        if not agent.synced_saleor_addresses.filter().exists():
            continue
        else:
            for sc in agent.synced_saleor_addresses.all():
                _, user = Saleor.user(agent.user)
                if not _:
                    logger.error("CHECK-SYNCED-ADDRESSES| error {}".format("unable to get "))
                    continue
                address_still_synced = False
                for address in user["addresses"]:
                    if address["companyName"]:
                        company_id = re.search(r'\((\d+)\)', address["companyName"]).group(1)
                        if int(company_id) == int(sc.id):
                            address_still_synced = True
                if not address_still_synced:
                    sc.synced_saleor_address.remove(agent)
                    sc.save()



