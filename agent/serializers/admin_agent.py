from core import serializers

from agent.models import Agent, AgentRegion


class AdminAgentSerializer(serializers.ModelSerializer):
    service_center_rate = serializers.IntegerField()
    friends_rate = serializers.IntegerField()
    site_rate = serializers.IntegerField()
    rate = serializers.IntegerField()
    head = serializers.SerializerMethodField()
    user = serializers.SerializerMethodField()
    agent_region_count = serializers.IntegerField()
    fast_count = serializers.IntegerField()
    focus_count = serializers.IntegerField()
    colleagues_count = serializers.IntegerField()

    class Meta:
        model = Agent
        fields = (
            "id",
            "user",
            "head",
            "agent_region_count",
            "fast_count",
            "focus_count",
            "colleagues_count",
            "service_center_rate",
            "friends_rate",
            "site_rate",
            "rate",
        )

    def get_agent_region_count(self, obj):
        return AgentRegion.objects.select_related("agent").filter(agent_id=obj.id).count()

    def get_user(self, obj):
        return {
            "username": obj.user.username,
            "fullname": obj.user.get_full_name(),
            "first_name": obj.user.first_name,
            "last_name": obj.user.last_name,
        }

    def get_head(self, obj):
        data = obj.head.user.get_full_name() if obj.head else "-"
        return data

    def get_full_name(self, obj):
        data = obj.user.get_full_name()
        return data
