import jdatetime
import pytz
from core import serializers

from agent.models import AgentVisit


class AgentVisitSerializer(serializers.ModelSerializer):
    date = serializers.SerializerMethodField()
    start = serializers.SerializerMethodField()
    end = serializers.SerializerMethodField()

    class Meta:
        model = AgentVisit
        fields = ("id", "status", "date", "start", "end", "note")

    def get_date(self, obj):
        return jdatetime.date.fromgregorian(
            day=obj.created_at.day, month=obj.created_at.month, year=obj.created_at.year
        ).strftime("%Y/%m/%d")

    def get_start(self, obj):
        try:
            return (
                obj.start_at.replace(tzinfo=pytz.utc).astimezone(pytz.timezone("Asia/Tehran")).time().strftime("%H:%M")
            )
        except:
            return ""

    def get_end(self, obj):
        try:
            return obj.end_at.replace(tzinfo=pytz.utc).astimezone(pytz.timezone("Asia/Tehran")).time().strftime("%H:%M")
        except:
            return ""
