from core import serializers

from agent.models import Agent


class SimpleAgentSerializer(serializers.ModelSerializer):
    username = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = Agent
        fields = "id", "username", "full_name"

    def get_username(self, obj):
        return obj.user.username

    def get_full_name(self, obj):
        return obj.user.full_name
