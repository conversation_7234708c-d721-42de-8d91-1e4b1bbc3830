import jdatetime
from rest_framework import serializers
from rest_framework.fields import Serializer<PERSON>ethod<PERSON>ield

from agent.models import AgentRegion


class AdminAgentRegionSerializer(serializers.ModelSerializer):
    state: SerializerMethodField = serializers.SerializerMethodField()
    city = serializers.SerializerMethodField()
    region = serializers.SerializerMethodField()
    date = serializers.SerializerMethodField()

    class Meta:
        model = AgentRegion
        fields = ("id", "is_selected", "grade", "date", "state", "city", "region")

    def get_date(self, obj):
        return jdatetime.date(day=obj.created_at.day, month=obj.created_at.month, year=obj.created_at.year).strftime(
            "%Y/%m/%d"
        )

    def get_state(self, obj):
        return obj.region.city.state.name

    def get_city(self, obj):
        return obj.region.city.name

    def get_region(self, obj):
        return obj.region.name
