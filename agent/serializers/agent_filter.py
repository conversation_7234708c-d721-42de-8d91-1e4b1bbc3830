from rest_framework import serializers
from agent.models import Agent


class AgentFilterSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()
    username = serializers.SerializerMethodField()

    class Meta:
        model = Agent
        fields = (
            "id",
            "full_name",
            "username"
        )

    def get_username(self, obj):
        return obj.user.username

    def get_full_name(self, obj):
        return obj.user.full_name
