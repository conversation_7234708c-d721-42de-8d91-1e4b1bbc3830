from accounts.serializers import UserSerializer
from core import serializers

from agent.models import Agent


class AdminAgentDetailSerializer(serializers.ModelSerializer):
    head = serializers.SerializerMethodField()
    user = UserSerializer()

    class Meta:
        model = Agent
        fields = ("id", "fast_visit", "user", "head", "is_active", "test_mode", "check_location", "state")

    def get_head(self, obj):
        return UserSerializer(obj.head.user).data if obj.head else ""
