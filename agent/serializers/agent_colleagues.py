from core import serializers
from files.models import Image

from agent.models import Agent


class AgentColleaguesSerializer(serializers.ModelSerializer):
    has_message = serializers.SerializerMethodField()
    has_g_alarm = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()
    point = serializers.SerializerMethodField()
    rank = serializers.SerializerMethodField()
    star = serializers.SerializerMethodField()

    class Meta:
        model = Agent
        fields = ("full_name", "image", "point", "rank", "star", "has_message", "has_g_alarm")

    def get_full_name(self, obj):
        return obj.user.get_full_name()

    def get_image(self, obj):
        try:
            return Image.objects.filter(object_id=obj.user.id, content_type__model="user").last().image.url
        except Exception:
            return ""

    def get_point(self, obj):
        return 0

    def get_rank(self, obj):
        return 0

    def get_star(self, obj):
        return 0

    def get_has_message(self, obj):
        return False

    def get_has_g_alarm(self, obj):
        return False
