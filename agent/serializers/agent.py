from core import serializers
from files.models import Image
from agent.models import Agent
from service_center.models import ServiceCenter

from django.utils.timezone import timedelta
from django.utils import timezone
from django.db.models import Q


class AgentSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()
    email = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()
    total_centers = serializers.SerializerMethodField()
    new_center_this_month = serializers.SerializerMethodField()
    total_orders = serializers.SerializerMethodField()
    new_orders_this_month = serializers.SerializerMethodField()
    username = serializers.SerializerMethodField()

    class Meta:
        model = Agent
        fields = (
            "id",
            "full_name",
            "username",
            "image",
            "email",
            "total_centers",
            "new_center_this_month",
            "total_orders",
            "new_orders_this_month"
        )

    def get_username(self, obj):
        return obj.user.username

    def get_email(self, obj):
        return obj.user.email

    def get_full_name(self, obj):
        return obj.user.get_full_name()

    def get_image(self, obj):
        try:
            return Image.objects.filter(content_type__model="user", object_id=obj.user.id).last().image.url
        except Exception:
            return ""

    def get_total_centers(self, obj):
        # total_centers = obj.fast_service_centers.all().count()
        total_centers = ServiceCenter.objects.filter(Q(agent=obj) | Q(fast_agent=obj)).distinct().count()
        return total_centers

    def get_new_center_this_month(self, obj):
        thirty_days_ago = timezone.now() - timedelta(days=30)
        total_centers = ServiceCenter.objects.filter(Q(agent=obj) | Q(fast_agent=obj))
        new_center_this_month = total_centers.filter(created_at__gte=thirty_days_ago).distinct().count()
        return new_center_this_month

    def get_total_orders(self, obj):
        return 0

    def get_new_orders_this_month(self, obj):
        return 0
