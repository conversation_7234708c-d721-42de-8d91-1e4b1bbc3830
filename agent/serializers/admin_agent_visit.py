import jdatetime
from core import serializers

from agent.models import AgentVisit


class AdminAgentVisitSerializer(serializers.ModelSerializer):
    date = serializers.SerializerMethodField()
    start = serializers.SerializerMethodField()
    end = serializers.SerializerMethodField()
    state = serializers.SerializerMethodField()
    city = serializers.SerializerMethodField()
    region = serializers.SerializerMethodField()
    sc_id = serializers.SerializerMethodField()
    sc_name = serializers.SerializerMethodField()

    class Meta:
        model = AgentVisit
        fields = (
            "id",
            "sc_id",
            "sc_name",
            "status",
            "date",
            "start",
            "end",
            "note",
            "status",
            "state",
            "city",
            "region",
        )

    def get_sc_name(self, obj):
        return obj.service_center.name

    def get_sc_id(self, obj):
        return obj.service_center.id

    def get_state(self, obj):
        return obj.service_center.address.region.city.state.name

    def get_city(self, obj):
        return obj.service_center.address.region.city.name

    def get_region(self, obj):
        return obj.service_center.address.region.name

    def get_date(self, obj):
        return jdatetime.datetime.fromgregorian(datetime=obj.start_at).strftime("%y/%m/%d")

    def get_start(self, obj):
        try:
            return jdatetime.datetime.fromgregorian(datetime=obj.start_at).strftime("%y/%m/%d %H:%M")
        except Exception as e:
            return ""

    def get_end(self, obj):
        try:
            return jdatetime.datetime.fromgregorian(datetime=obj.end_at).strftime("%y/%m/%d %H:%M")
        except Exception as e:
            return ""
