import datetime
import logging

import jdatetime
from accounts.models import Credit
from agent.models import Agent
from constants import CreditType
from core.celery import AbstractBaseCelery
from django.db.models import Q, Sum
from settings.celery_app import celery_app

logger = logging.getLogger("agent")


def agent_credit_checkout_action(agent):
    try:
        credit_queryset = Credit.objects.filter(user=agent.user)
        last_credit = credit_queryset.filter(type=CreditType.AGENT_CHECKOUT, site_id=6).last()

        if last_credit:
            credit_queryset = credit_queryset.filter(created_at__gte=last_credit.created_at)

        sum_credit = (
            credit_queryset.filter(
                Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR)
                | Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR_FIRST_HEAD)
                | Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR_SECOND_HEAD)
                | Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR_THIRD_HEAD)
                | Q(type=CreditType.AGENT_NEW_USER)
                | Q(type=CreditType.AGENT_BILL),
                site_id=6,
            ).aggregate(Sum("amount"))["amount__sum"]
            or 0
        )

        Credit.decrease(
            user=agent.user,
            title="تسویه حساب تا تاریخ {}".format(datetime.date.today().strftime("%Y/%m/%d")),
            desc="SYSTEM ACTION AGENT CHECKOUT USER_ID {}".format(agent.user.id),
            amount=sum_credit,
            credit_type=CreditType.AGENT_CHECKOUT,
            content_type_model="user",
            object_id=agent.user.id,
            site=6,
        )
        logger.info(
            "agent={} checkout {} points successfully".format(
                agent.id, sum_credit
            )
        )
    except Exception as e:
        logger.error("message={}".format(e))


def agent_credit_checkout_periodic(agent):
    try:
        credit_queryset = Credit.objects.filter(user=agent.user)
        last_credit = Credit.objects.filter(user=agent.user, type=CreditType.AGENT_CHECKOUT).last()
        today = jdatetime.datetime.today()

        if last_credit:
            start_date = jdatetime.datetime.fromgregorian(datetime=last_credit.created_at)
            first_end_date = (start_date.replace(day=15) + jdatetime.timedelta(days=20)).replace(day=1)
            credit_queryset = Credit.objects.filter(
                user=agent.user, created_at__gte=start_date.togregorian(), created_at__lt=first_end_date.togregorian()
            )
            get_queryset_and_decrease(queryset=credit_queryset, agent=agent, end_date=first_end_date)

            for month_num in range(first_end_date.month, today.month):
                start_date = first_end_date.replace(month=month_num)
                end_date = (start_date.replace(day=15) + datetime.timedelta(days=20)).replace(day=1)
                credit_queryset = Credit.objects.filter(
                    user=agent.user, created_at__gte=start_date.togregorian(), created_at__lt=end_date.togregorian()
                )
                get_queryset_and_decrease(queryset=credit_queryset, agent=agent, end_date=end_date)
        else:
            get_queryset_and_decrease(queryset=credit_queryset, agent=agent, end_date=today)
    except Exception as e:
        logger.error("message={}".format(e))


def get_queryset_and_decrease(queryset, agent, end_date):
    try:
        sum_credit = (
            queryset.filter(
                Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR)
                | Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR_FIRST_HEAD)
                | Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR_SECOND_HEAD)
                | Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR_THIRD_HEAD)
                | Q(type=CreditType.AGENT_NEW_USER)
                | Q(type=CreditType.AGENT_BILL),
                site_id=6,
            ).aggregate(Sum("amount"))["amount__sum"]
            or 0
        )
        Credit.decrease(
            user=agent.user,
            amount=sum_credit,
            credit_type=CreditType.AGENT_CHECKOUT,
            content_type_model="user",
            object_id=agent.user.id,
            title="تسویه حساب تا تاریخ {}".format(end_date.strftime("%Y/%m/%d")),
            desc="SYSTEM PERIODIC AGENT CHECKOUT OF {} USER_ID {}".format(end_date.strftime("%Y/%m/%d"), agent.user.id),
            site=6,
        )
        logger.info(
            "agent={} checkout {} points successfully".format(
                agent.id, sum_credit
            )
        )
    except Exception as e:
        logger.error("message={}".format(e))


@celery_app.task(name="agent_checkout", base=AbstractBaseCelery)
def checkout_all_agents():
    if jdatetime.date.today().day == 1:
        for agent in Agent.objects.filter(is_active=True):
            agent_credit_checkout_periodic(agent=agent)
