import logging
from math import ceil

from accounts.models import Credit
from constants import CreditType, FactorSite
from django.db.models import Q
from service_center.models import ServiceCenter

logger = logging.getLogger("agent")


def submit_factor_agent_wage(factor):
    if factor.site == FactorSite.T2BCO or factor.site == FactorSite.CARFILLO:
        try:
            agent = ServiceCenter.objects.get(user=factor.user).agent

            object_id = factor.real_order_id if factor.real_order_id else factor.real_order_line_id

            if (
                not Credit.objects.filter(content_type__model="factor", object_id=object_id)
                .filter(
                    Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR)
                    | Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR_FIRST_HEAD)
                    | Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR_SECOND_HEAD)
                    | Q(type=CreditType.AGENT_SERVICE_CENTER_FACTOR_THIRD_HEAD),
                    site_id=6,
                )
                .exists()
            ):
                Credit.increase(
                    user=agent.user,
                    site=6,
                    title="کارمزد مشاور از فاکتور {}".format(object_id),
                    desc="SYSTEM AGENT WAGE FACTOR {}".format(object_id),
                    amount=ceil(factor.payable_amount * 0.005),
                    content_type_model="factor",
                    object_id=object_id,
                    credit_type=CreditType.AGENT_SERVICE_CENTER_FACTOR,
                )

                if agent.head:
                    Credit.increase(
                        user=agent.head.user,
                        site=6,
                        title="کارمزد مشاور از فاکتور {}".format(object_id),
                        desc="SYSTEM AGENT WAGE FACTOR {}".format(object_id),
                        amount=ceil(factor.payable_amount * 0.00125),
                        content_type_model="factor",
                        object_id=object_id,
                        credit_type=CreditType.AGENT_SERVICE_CENTER_FACTOR_FIRST_HEAD,
                    )
                    if agent.head.head:
                        Credit.increase(
                            user=agent.head.head.user,
                            site=6,
                            title="کارمزد مشاور از فاکتور {}".format(object_id),
                            desc="SYSTEM AGENT WAGE FACTOR {}".format(object_id),
                            amount=ceil(factor.payable_amount * 0.000625),
                            content_type_model="factor",
                            object_id=object_id,
                            credit_type=CreditType.AGENT_SERVICE_CENTER_FACTOR_SECOND_HEAD,
                        )
                        if agent.head.head.head:
                            Credit.increase(
                                user=agent.head.head.head.user,
                                site=6,
                                title="کارمزد مشاور از فاکتور {}".format(object_id),
                                desc="SYSTEM AGENT WAGE FACTOR {}".format(object_id),
                                amount=ceil(factor.payable_amount * 0.0003125),
                                content_type_model="factor",
                                object_id=object_id,
                                credit_type=CreditType.AGENT_SERVICE_CENTER_FACTOR_THIRD_HEAD,
                            )
        except Exception as e:
            logger.error("message={}".format(factor.user, e), extra={"user": factor.user})
