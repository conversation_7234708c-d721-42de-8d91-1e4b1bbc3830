# fixme make it dynamic
import datetime
from django.core.validators import validate_email
from unidecode import unidecode

from accounts.models import User
from agent.models import AgentVisit
from constants import VisitStatus
from core.validators import mobile_number_validator, validate_postal_code_based_on_country
from locations.models import Country, Region, Address
from service_center.models import ServiceCenter, ServiceCenterServiceType, ServiceCenterConfig


def create_agent_tr_sc(self, data: dict, agent_country_code, agent):
    self.exception_data = {"status": 400, "message": "Please enter the name of the service center."}
    name = data["name"]
    self.exception_data = {"status": 400,
                           "message": "Please enter the mobile number of the center's incharge person."}

    username = unidecode(str(data["user"]["username"]))

    self.exception_data = {"status": 400, "message": "Please enter the country code of user's mobile number"}
    country_code = str(data["user"]["country_code"])

    self.exception_data = {"status": 400, "message": "Mobile number is not valid."}
    mobile_number_validator(username)

    self.exception_data = {"status": 400, "message": "Please enter the firstname of the center's incharge person."}
    first_name = data["user"]["first_name"]

    email = data["user"].get("email")
    self.exception_data = {"status": 400, "message": "invalid email."}
    if email and not email == '':
        validate_email(email)

    self.exception_data = {"status": 400, "message": "Please enter the lastname of the center's incharge person."}
    last_name = data["user"]["last_name"]

    self.exception_data = {"status": 400, "message": "Please enter valid nationality."}
    birthplace_id = data["user"]["birthplace_id"]
    country = Country.objects.get(id=int(birthplace_id))

    self.exception_data = {"status": 400, "message": "Please enter the type of service center."}
    types = data["types"]

    self.exception_data = {"status": 400, "message": "Please enter the phone number of service center."}
    phone_number = unidecode(str(data["phone_number"]))

    self.exception_data = {"status": 400, "message": "Mobile number is not valid."}
    mobile_number_validator(phone_number)

    self.exception_data = {"status": 400, "message": "Please select the region of service center."}
    region_id = data["region_id"]
    self.exception_data = {"status": 400, "message": "region not found"}
    region = Region.objects.get(id=region_id)
    if region.city.state.country.code != agent_country_code:
        return False, "Agent's country must match the service center's country."

    self.exception_data = {"status": 400, "message": "Please enter the zipcode of service center."}
    postal_code = data["address"]["postal_code"]

    # self.exception_data = {"status": 400, "message": "Please enter the vat number of service center."}
    # vat_number = data['vat_number']

    if not validate_postal_code_based_on_country(postal_code, agent_country_code):
        return False, "Invalid postal code."

    self.exception_data = {"status": 400, "message": "Please enter the address of service center."}
    description = data["address"]["description"]

    self.exception_data = {"status": 400,
                           "message": "Please pinpoint the location of the service center on the map."}
    latitude = data["address"]["latitude"]
    longitude = data["address"]["longitude"]

    commercial_register_code = data.get("commercial_register_code")

    self.exception_data = {"status": 400, "message": "Error in registering user information."}
    user = User.objects.update_or_create(
        username=username,
        defaults={
            "first_name": first_name,
            "last_name": last_name,
            "mobile_number": username,
            "email": email,
            "birthplace_id": int(birthplace_id),
            "country_code": country_code
        }
    )[0]

    try:
        service_center = user.service_center
        return False, "The entered number is already registered for another service center. Please do not register this center again."
    except Exception as e:
        pass

    self.exception_data = {"status": 400, "message": "Error in registering the center's address."}
    address = Address.objects.create(
        region_id=region_id,
        description=description,
        postal_code=postal_code,
        latitude=latitude,
        longitude=longitude
    )

    self.exception_data = {"status": 400, "message": "Error in registering service center license expiration."}
    license_expiration = data.get('license_expiration')
    if license_expiration:
        license_expiration = datetime.date.fromisoformat(data['license_expiration'])

    self.exception_data = {"status": 400, "message": "Error in registering service center sponsor civil number."}
    sponsor_civil_number = data.get('sponsor_civil_number')

    self.exception_data = {"status": 400, "message": "Error in registering service center foreman civil number."}
    foreman_civil_number = data.get('foreman_civil_number')

    self.exception_data = {"status": 400, "message": "Error in registering service center sponsor name"}
    sponsor_name = data.get('sponsor_name')

    self.exception_data = {"status": 400, "message": "Error in experience in year."}
    experience_in_year = data.get('experience_in_year')
    if experience_in_year:
        experience_in_year = int(data['experience_in_year'])

    self.exception_data = {"status": 400, "message": "Error in registering service center information."}
    service_center = ServiceCenter(
        seller_id=1,
        name=name,
        user=user,
        phone_number=phone_number,
        default_location_address=address,
        fast_agent=agent,
        commercial_register_code=commercial_register_code,
        ownership_type=data.get("ownership_type"),
        license_expiration=license_expiration,
        experience_in_year=experience_in_year,
        sponsor_civil_number=sponsor_civil_number,
        foreman_civil_number=foreman_civil_number,
        sponsor_name=sponsor_name,
        # vat_number=vat_number
    )
    service_center.save()
    self.exception_data = {"status": 400, "message": "Error in registering service address."}
    service_center.addresses.add(address)

    self.exception_data = {"status": 400, "message": "Error in registering service types."}
    for type_id in types:
        ServiceCenterServiceType.objects.get_or_create(
            service_center=service_center,
            service_type_id=type_id,
        )

    AgentVisit.objects.create(
        agent=agent,
        status=VisitStatus.FAST,
        service_center=service_center,
        start_at=datetime.datetime.now(),
        end_at=datetime.datetime.now(),
    )

    sc_config = ServiceCenterConfig.objects.get_or_create(service_center=service_center)[0]
    from service_center.models import ServiceCenterShift
    shift_ids = list(ServiceCenterShift.objects.all().values_list("id", flat=True))
    sc_config.mon.set(shift_ids)
    sc_config.tue.set(shift_ids)
    sc_config.wed.set(shift_ids)
    sc_config.thu.set(shift_ids)
    sc_config.fri.set(shift_ids)

    return True, service_center
