import datetime

import jdatetime
import pytz
from agent.models import Agent
from constants import VisitStatus
from core.utils import Excel, jdt_now_numeric


def humanize_timedelta(td):
    return "{}".format(td.seconds // 60)


def agent_report_weekly(agent_ids, start_date, end_date, timedelta):
    wb = Excel()
    wb.remove(wb.get_sheet_by_name("Sheet"))
    sheet1 = wb.create_sheet("Sheet 1")

    wb.insert_excel(sheet1, 1, 1, "username")
    wb.insert_excel(sheet1, 1, 2, "full name")
    wb.insert_excel(sheet1, 1, 3, "date")
    wb.insert_excel(sheet1, 1, 4, "work time start")
    wb.insert_excel(sheet1, 1, 5, "work time end")
    wb.insert_excel(sheet1, 1, 6, "total work time (minutes)")
    wb.insert_excel(sheet1, 1, 7, "total count")
    wb.insert_excel(sheet1, 1, 8, "fast count")
    wb.insert_excel(sheet1, 1, 9, "visit count")
    wb.insert_excel(sheet1, 1, 10, "pending count")
    wb.insert_excel(sheet1, 1, 11, "focus count")

    row = 2
    for id in agent_ids:
        agent = Agent.objects.filter(id=id).first()
        work_time_avg = 0
        work_time_start_list = []
        work_time_finish_list = []
        total_total_count = 0
        total_fast_count = 0
        total_visit_count = 0
        total_pend_count = 0
        total_focus_count = 0
        for day in range(timedelta, -1, -1):
            date = end_date - datetime.timedelta(days=day)
            date_start = date.replace(hour=00, minute=00)
            date_end = date.replace(hour=23, minute=59)
            total_count = agent.visits.filter(created_at__gte=date_start, created_at__lt=date_end).count()
            fast_count = agent.visits.filter(
                created_at__gte=date_start, created_at__lt=date_end, status=VisitStatus.FAST
            ).count()
            visit_count = agent.visits.filter(
                created_at__gte=date_start, created_at__lt=date_end, status=VisitStatus.VISIT
            ).count()
            pend_count = agent.visits.filter(
                created_at__gte=date_start, created_at__lt=date_end, status=VisitStatus.PEND
            ).count()
            focus_count = agent.visits.filter(
                created_at__gte=date_start, created_at__lt=date_end, status=VisitStatus.FOCUS
            ).count()
            work_time_q_last = agent.visits.filter(created_at__gte=date_start, created_at__lt=date_end).last()
            work_time_q_first = agent.visits.filter(created_at__gte=date_start, created_at__lt=date_end).first()
            if work_time_q_first or work_time_q_last:
                work_time_start = (
                    work_time_q_first.created_at.replace(tzinfo=pytz.utc)
                    .astimezone(pytz.timezone("Asia/Tehran"))
                    .replace(day=1)
                )
                work_time_finish = (
                    work_time_q_last.created_at.replace(tzinfo=pytz.utc)
                    .astimezone(pytz.timezone("Asia/Tehran"))
                    .replace(day=1)
                )
                work_time = humanize_timedelta(work_time_q_last.created_at - work_time_q_first.created_at)
                work_time_start_list.append(work_time_start)
                work_time_finish_list.append(work_time_finish)
            else:
                work_time_start = ""
                work_time_finish = ""
                work_time = 0

            wb.insert_excel(sheet1, row, 1, agent.user.username)
            wb.insert_excel(sheet1, row, 2, agent.user.get_full_name())
            wb.insert_excel(sheet1, row, 3, jdatetime.datetime.fromgregorian(datetime=date).strftime("%Y/%m/%d"))
            wb.insert_excel(sheet1, row, 4, work_time_start.strftime("%H:%M:%S") if work_time_start != "" else "")
            wb.insert_excel(sheet1, row, 5, work_time_finish.strftime("%H:%M:%S") if work_time_finish != "" else "")
            wb.insert_excel(sheet1, row, 6, work_time)
            wb.insert_excel(sheet1, row, 7, total_count)
            wb.insert_excel(sheet1, row, 8, fast_count)
            wb.insert_excel(sheet1, row, 9, visit_count)
            wb.insert_excel(sheet1, row, 10, pend_count)
            wb.insert_excel(sheet1, row, 11, focus_count)

            work_time_avg += int(work_time)
            total_total_count += total_count
            total_fast_count += fast_count
            total_visit_count += visit_count
            total_pend_count += pend_count
            total_focus_count += focus_count
            row += 1
        wb.insert_excel(sheet1, row, 6, work_time_avg // 9)
        if work_time_start_list and work_time_finish_list:
            wb.insert_excel(
                sheet1,
                row,
                4,
                datetime.datetime.strftime(
                    datetime.datetime.fromtimestamp(
                        sum(map(datetime.datetime.timestamp, work_time_start_list)) / len(work_time_start_list)
                    ).astimezone(pytz.timezone("Asia/Tehran")),
                    "%H:%M:%S",
                ),
            )

            wb.insert_excel(
                sheet1,
                row,
                5,
                datetime.datetime.strftime(
                    datetime.datetime.fromtimestamp(
                        sum(map(datetime.datetime.timestamp, work_time_finish_list)) / len(work_time_finish_list)
                    ).astimezone(pytz.timezone("Asia/Tehran")),
                    "%H:%M:%S",
                ),
            )

        wb.insert_excel(sheet1, row, 7, total_total_count)
        wb.insert_excel(sheet1, row, 8, total_fast_count)
        wb.insert_excel(sheet1, row, 9, total_visit_count)
        wb.insert_excel(sheet1, row, 10, total_pend_count)
        wb.insert_excel(sheet1, row, 11, total_focus_count)

        if len(work_time_start_list):
            wb.insert_excel(sheet1, row - 2 if row > 2 else row, 12, "active days count")
            wb.insert_excel(sheet1, row - 2 if row > 2 else row, 13, len(work_time_start_list))

        if len(work_time_start_list):
            wb.insert_excel(sheet1, row - 4 if row > 4 else row, 12, "active days time avg")
            wb.insert_excel(sheet1, row - 4 if row > 4 else row, 13, work_time_avg // len(work_time_start_list))

        row += 2

    return wb.http_response("AgentReportWeekly-{}.xls".format(jdt_now_numeric()))
