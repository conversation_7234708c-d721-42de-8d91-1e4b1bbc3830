from core.admin import AbstractBaseAdmin

from agent.forms import AgentRegionAddForm


class AgentRegionAdmin(AbstractBaseAdmin):
    list_display = ("agent", "grade", "get_state", "get_city", "region", "is_selected", "is_active")
    list_editable = ("is_active", "is_selected")
    search_fields = ("id", "agent__user__username", "agent__state__name")
    readonly_fields = ("created_at",)
    raw_id_fields = ("agent", "region")
    list_select_related = ("region__city__state", "agent__user")
    add_form = AgentRegionAddForm

    fieldsets = (
        (None, {"fields": (("is_active", "priority", "is_selected"), ("agent", "grade", "region"), ("created_at",))}),
    )

    def get_state(self, obj):
        return obj.region.city.state

    def get_city(self, obj):
        return obj.region.city

    get_state.short_description = "state"
    get_city.short_description = "city"
