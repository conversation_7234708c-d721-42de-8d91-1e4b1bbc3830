from constants import VisitStatus
from core import admin


class AgentVisitAdmin(admin.AbstractBaseAdmin):
    list_display = ("id", "agent", "_status", "service_center", "_sc_status", "start_at", "end_at")
    list_editable = ()
    readonly_fields = ("created_at", "updated_at")
    raw_id_fields = ("agent", "service_center")
    filter_horizontal = ("poll_question",)
    list_select_related = ("agent__user", "service_center")

    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("created_at", "updated_at"),
                    ("start_at", "end_at"),
                    ("status", "agent", "service_center"),
                    "poll_question",
                    "note",
                )
            },
        ),
    )

    _status = admin.get_display("status", "status", func=VisitStatus.get_colored)
    _sc_status = admin.get_display(
        "service_center__visit_status", "service center status", func=VisitStatus.get_colored
    )
