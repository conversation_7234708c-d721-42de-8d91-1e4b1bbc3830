from constants import Grade
from core.admin import AbstractBaseAdmin
from core.utils import Excel, jdt_now_numeric
from locations.models import Region

from agent.models import AgentRegion


class AgentAdmin(AbstractBaseAdmin):

    list_display = (
        "id",
        "user",
        "get_name",
        "state",
        "head",
        "fast_visit",
        "check_location",
        "test_mode",
        "is_active",
    )
    list_editable = ("is_active", "fast_visit", "check_location", "test_mode")
    search_fields = ("id", "user__username", "head__user__username", "state__name")
    readonly_fields = ("created_at", "updated_at")
    raw_id_fields = ("user", "head", "state")
    list_select_related = ("state", "head__user", "user")

    actions = (
        "unlock_all_regions",
        "export_agent_ids",
        "checkout",
    )

    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("created_at", "updated_at"),
                    ("first_login", "last_login"),
                    ("is_active", "fast_visit", "check_location"),
                    ("user", "state"),
                    ("priority", "head"),
                )
            },
        ),
    )

    def unlock_all_regions(self, request, queryset):
        for agent in queryset:
            state = agent.state
            for region in Region.objects.filter(city__state=state):
                AgentRegion.objects.get_or_create(agent=agent, region=region, grade=Grade.A)
                AgentRegion.objects.get_or_create(agent=agent, region=region, grade=Grade.AP)
                AgentRegion.objects.get_or_create(agent=agent, region=region, grade=Grade.B)
                AgentRegion.objects.get_or_create(agent=agent, region=region, grade=Grade.BP)
                AgentRegion.objects.get_or_create(agent=agent, region=region, grade=Grade.C)
                AgentRegion.objects.get_or_create(agent=agent, region=region, grade=Grade.CP)

    def get_name(self, obj):
        return obj.user.full_name

    def export_agent_ids(self, request, queryset):
        agents_id = queryset.order_by("state").values_list("id", flat=True).distinct()
        wb = Excel()
        sheet1 = wb.active
        for index, id in enumerate(agents_id):
            wb.insert_excel(sheet1, index + 1, 1, id)
        return wb.http_response("AgentIds-{}.xls".format(jdt_now_numeric()))

    get_name.short_description = "name"
    export_agent_ids.short_description = "exprot agent ids"
    unlock_all_regions.short_description = "unlock all regions"

