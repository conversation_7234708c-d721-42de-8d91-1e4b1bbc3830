# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Agent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('fast_visit', models.BooleanField(default=False)),
                ('check_location', models.BooleanField(default=True)),
                ('test_mode', models.BooleanField(default=False)),
                ('first_login', models.DateTimeField(blank=True, null=True)),
                ('last_login', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'matrix_agent_agent',
            },
        ),
        migrations.CreateModel(
            name='AgentRegion',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('grade', models.CharField(choices=[('a', 'A'), ('ap', 'A+'), ('b', 'B'), ('bp', 'B+'), ('c', 'C'), ('cp', 'C+')], default='c', max_length=100)),
                ('is_selected', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'matrix_agent_agent_region',
            },
        ),
        migrations.CreateModel(
            name='AgentVisit',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('visit', 'Visit'), ('fast', 'Fast'), ('pend', 'Pending'), ('focus', 'Focus'), ('pri', 'Pri'), ('pre', 'Pre')], default='visit', max_length=100)),
                ('start_at', models.DateTimeField()),
                ('end_at', models.DateTimeField(blank=True, null=True)),
                ('note', models.TextField()),
                ('test_mode', models.BooleanField(default=False)),
                ('agent', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='visits', to='agent.agent')),
            ],
            options={
                'db_table': 'matrix_agent_agent_visit',
            },
        ),
    ]
