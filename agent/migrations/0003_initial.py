# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('service_center', '0001_initial'),
        ('locations', '0001_initial'),
        ('agent', '0002_agentvisit_poll_question'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='agentvisit',
            name='service_center',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='visits', to='service_center.servicecenter'),
        ),
        migrations.AddField(
            model_name='agentregion',
            name='agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='regions', to='agent.agent'),
        ),
        migrations.AddField(
            model_name='agentregion',
            name='region',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='agent_regions', to='locations.region'),
        ),
        migrations.AddField(
            model_name='agent',
            name='head',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='colleagues', to='agent.agent'),
        ),
        migrations.AddField(
            model_name='agent',
            name='state',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='agents', to='locations.state'),
        ),
        migrations.AddField(
            model_name='agent',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.PROTECT, related_name='agent', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='agentregion',
            unique_together={('agent', 'region', 'grade')},
        ),
    ]
