from rest_framework.pagination import CursorPagination
from urllib.parse import urlparse
from rest_framework.response import Response


class CentersCursorPagination(CursorPagination):
    page_size = 20
    ordering = "-created_at"

    def get_paginated_response(self, data):
        next_link = self.get_next_link()

        if next_link:
            new_path = urlparse(next_link).path.replace("/api/v1/agent", "")
            next_link = new_path + "?" + urlparse(next_link).query

        response_data = {
            "next": next_link,
            "results": data
        }

        return Response(response_data)
