import openpyxl

from accounts.models import User
from agent.models import AgentRegion
from core.validators import mobile_number_validator
from django import forms
from unidecode import unidecode


class AgentRegionAddForm(forms.ModelForm):
    import_excel = forms.FileField(label="Import Agent Region Excel")

    class Meta:
        model = AgentRegion
        fields = ("import_excel",)

    def save(self, commit=True):
        self.save_m2m = self._save_m2m
        data = self.cleaned_data["import_excel"]
        book = openpyxl.load_workbook(data)
        xl_sheet = book.active
        agent_region = None
        for row in range(2, xl_sheet.max_row + 1):
            mobile_number = xl_sheet.cell(row, 1).value
            grade = xl_sheet.cell(row, 2).value
            region_id = xl_sheet.cell(row, 3).value
            if isinstance(mobile_number, float):
                mobile_number = int(mobile_number)
            mobile_number = str(mobile_number)
            if len(mobile_number) < 11:
                mobile_number = "0" + mobile_number
            mobile_number = unidecode(mobile_number)
            mobile_number_validator(mobile_number)

            agent_region, _ = AgentRegion.objects.get_or_create(
                agent=User.objects.get(username=mobile_number).agent, grade=grade.lower(), region_id=region_id
            )

        return agent_region
