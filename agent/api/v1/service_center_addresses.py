import logging

from django.utils.translation import gettext as _

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from core.http import JsonResponse
from service_center.models import ServiceCenter
from locations.models import Address
from saleor.utils import Saleor

from agent.permissions import IsAgent

logger = logging.getLogger("agent")


class ServiceCenterAddresses(APIView):
    permission_classes = (IsAuthenticated, IsAgent)

    def post(self, request):
        self.exception_data = {"status": 400, "message": _("Please enter the ID of service center.")}
        service_center_id = int(request.data["service_center_id"])
        address_id = request.data.get("address_id")
        use_as_billing = request.data.get("use_as_billing")
        use_as_shipping = request.data.get("use_as_shipping")
        add_billing_address = request.data.get("add_billing_address")
        add_shipping_address = request.data.get("add_shipping_address")
        update_location_address = request.data.get("update_location_address")

        self.exception_data = {"status": 400, "message": _("invalid service center ID.")}
        sc = ServiceCenter.objects.get_by_country_code(country_code=request.agent_country_code).get(id=service_center_id)

        self.exception_data = {"status": 400, "message": _("invalid address ID.")}
        if address_id:
            self.exception_data = {"status": 400, "message": _("invalid address ID.")}
            address = Address.objects.get(id=int(address_id))
            if use_as_billing:
                sc.default_billing_address = address
                sc.save()
            elif use_as_shipping:
                sc.default_shipping_address = address
                sc.save()

        elif add_billing_address or add_shipping_address:
            self.exception_data = {"status": 400, "message": _("Please enter the region.")}
            region_id = request.data["region_id"]

            self.exception_data = {"status": 400, "message": _("Please enter the shipping_address of service center.")}
            description = request.data["address"]["description"]

            self.exception_data = {"status": 400, "message": _("Please enter the postal code")}
            postal_code = request.data["address"]["postal_code"]

            self.exception_data = {"status": 400,
                                   "message": _("Please pinpoint the location of the shipping address on the map.")}
            latitude = request.data["address"]["latitude"]
            longitude = request.data["address"]["longitude"]

            self.exception_data = {"status": 400, "message": _("Error in registering the center's address.")}
            logger.info(
                "Shipping-Address-log| region_id:{} description{} postal_code{}latitude{} longitude{}"
                "".format(region_id, description, postal_code, latitude, longitude))
            address = Address.objects.create(
                region_id=region_id,
                description=description,
                postal_code=postal_code,
                latitude=latitude,
                longitude=longitude
            )
            sc.addresses.add(address)
            if add_billing_address:
                sc.default_billing_address = address
                sc.save()
            elif add_shipping_address:
                sc.default_shipping_address = address
                sc.save()
        elif update_location_address :
            if not request.agent.user.has_perm("agent.can_update_location_address"):
                return JsonResponse(message=_("You dont have permission to update location address"), status=400)

            self.exception_data = {"status": 400, "message": _("Please enter the region.")}
            region_id = request.data["region_id"]

            self.exception_data = {"status": 400, "message": _("Please enter the shipping_address of service center.")}
            description = request.data["address"]["description"]

            self.exception_data = {"status": 400, "message": _("Please enter the postal code")}
            postal_code = request.data["address"]["postal_code"]

            self.exception_data = {"status": 400,
                                   "message": _("Please pinpoint the location of the shipping address on the map.")}
            latitude = request.data["address"]["latitude"]
            longitude = request.data["address"]["longitude"]

            sc.default_location_address.description = description
            sc.default_location_address.postal_code = postal_code
            sc.default_location_address.latitude = latitude
            sc.default_location_address.longitude = longitude
            sc.default_location_address.region_id = region_id
            sc.default_location_address.save()


        else:
            return JsonResponse(message=_("lack of required information."), status=400)

        try:
            saleor = Saleor(site='gmall')
            saleor.update_customer(sc)
        except Exception as e:
            logger.error("Update-Saleor-Addresses| error: {}".format(str(e)))

        return JsonResponse(message=_("added successfully."), status=201)
