import logging

from django.utils.translation import gettext as _

from constants.otp_types import OTPType
from core.http import JsonR<PERSON>ponse
from core.utils import timezone
from core.validators import mobile_number_validator
from otp.managers import OtpValidator
from rest_framework.views import APIView
from unidecode import unidecode
from accounts.models import *

from agent.models import Agent

from rest_framework.permissions import IsAuthenticated
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.parsers import JSONParser
import hashlib

from django.utils.translation import activate, get_language, deactivate
from django.core.exceptions import ValidationError
from django.core.cache import cache

from rest_framework_simplejwt.tokens import RefreshToken

logger = logging.getLogger("agent")
login_logger = logging.getLogger("login")


class AgentLoginAPI(APIView):
    parser_classes = (JSONParser,)
    renderer_classes = (JSONRenderer,)

    def post(self, request):
        first_login = ""
        mobile_number = unidecode(str(request.data['mobile_number']))

        self.exception_data = {"message": _("invalid phone number")}
        mobile_number_validator(mobile_number)

        code = request.data.get("code")
        self.exception_data = {"message": _("there is no agent with this phone number")}
        agent = Agent.objects.get(user__username=mobile_number, is_active=True)
        if code:
            confirmed, message = OtpValidator.validate(mobile_number, str(code), OTPType.AGENT_LOGIN)
            if confirmed:
                if not agent.first_login:
                    agent.first_login = timezone.now()
                    first_login = "[FIRST]"

                agent.user.last_login = timezone.now()
                agent.last_login = timezone.now()
                agent.user.save()
                agent.save()
                login_logger.info("[AGENT]{} id={} username={}".format(first_login, agent.id, agent.user.username))
                refresh = RefreshToken.for_user(agent.user)
                access = refresh.access_token

                cache.delete(mobile_number)
                return JsonResponse(
                    message=message,
                    data={"access_token": str(access), "refresh_token": str(refresh)})
            else:
                return JsonResponse(
                    message=message,
                    status=403
                )

        else:
            captcha_uuid = request.data["captcha_uuid"]
            captcha_code = unidecode(str(request.data["captcha"]))
            hash_code = cache.get(captcha_uuid)
            cache.delete(captcha_uuid)

            if hashlib.sha3_256(str(captcha_code).encode()).hexdigest() != hash_code:
                return JsonResponse(status=400, message=_("invalid captcha."))
            messaging_service = request.data.get("messaging_service", "WHATSAPP")

            if messaging_service == "SMS":
                confirmed, message = OtpValidator.send(mobile_number, OTPType.AGENT_LOGIN,
                                                       text="Your login verification code is: {}",
                                                       messaging_service=messaging_service)
            elif messaging_service == "WHATSAPP":
                confirmed, message = OtpValidator.send(mobile_number, OTPType.AGENT_LOGIN,
                                                       text="Your login verification code is: {}",
                                                       messaging_service=messaging_service)
            else:
                return JsonResponse(message=_("invalid messaging_service"), status=400)

            if confirmed:
                return JsonResponse(message=_("otp code sent"), status=200)
            else:
                return JsonResponse(message=message, status=404)
