from django.contrib.contenttypes.models import ContentType
from django.utils.translation import gettext as _
from rest_framework.views import APIView
from core.http import JsonResponse
from rest_framework.permissions import IsAuthenticated

from agent.permissions import IsAgent
from agent.serializers import AgentSerializer
from files.models import Image


class AgentProfile(APIView):
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request):
        serializer = AgentSerializer(request.agent)
        return JsonResponse(serializer.data)

    def post(self, request):
        self.exception_data = {"message": _("Image format is invalid.")}
        image = request.data["image"]

        self.exception_data = {"message": _("Error while saving Image.")}
        user = request.agent.user
        Image.objects.create(
            image=image,
            content_type=ContentType.objects.get(model="user"),
            object_id=user.id,
        )

        return JsonResponse(
            message=_("Image added successfully."),
            data=AgentSerializer(request.agent).data
        )
