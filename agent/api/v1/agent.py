import logging

from core.http import JsonResponse
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.permissions import IsAgent
from agent.serializers import AgentSerializer

logger = logging.getLogger("agent")


class AgentAPI(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request):
        return JsonResponse(data=AgentSerializer(request.agent).data)
