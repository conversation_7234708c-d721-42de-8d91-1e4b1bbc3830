import logging

from core.http import JsonResponse
from rest_framework.views import APIView

from service_center.models import ServiceType
from service_center.serializers import ServiceTypeSerializer

logger = logging.getLogger("agent")


class ServiceTypeAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        return JsonResponse(
            data=ServiceTypeSerializer(
                ServiceType.objects.filter(is_active=True).order_by("-priority"), many=True,
                context={"lang": request.lang}
            ).data
        )
