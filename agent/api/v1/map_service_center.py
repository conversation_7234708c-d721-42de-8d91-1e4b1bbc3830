import logging
from constants import Appearance
from core.http import JsonResponse
from django.db.models import Q, Count
from django.utils.translation import gettext as _

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.permissions import IsAgent
from service_center.models import ServiceCenter
from service_center.serializers import ServiceCenterSerializer
from agent.serializers import MapServiceCenterSerializer

logger = logging.getLogger("agent")


class MapServiceCenterAPI(APIView):
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request, service_center_id=None):
        q, term = request.query_params.get('q'), request.query_params.get('term')

        if service_center_id:
            self.exception_data = {"status": 400, "message": _("Service center not found.")}
            queryset = ServiceCenter.objects.get_by_country_code(country_code=request.agent_country_code).get(
                id=service_center_id)

            return JsonResponse(
                data=ServiceCenterSerializer(queryset, agent=request.agent).data,
                extra={"check_location": request.agent.check_location,
                       "appearance": Appearance.choices},
            )
        else:
            self.exception_data = {"message": _("Error in showing centers")}
            service_centers = ServiceCenter.objects.annotate(
                total_visit=Count(
                    "visits"
                ),
            ).select_related(
                "default_location_address",
            ).get_by_country_code(country_code=request.agent_country_code)

            if not request.agent.test_mode:
                service_centers = service_centers.filter(fast_agent__test_mode=False)

            if q:
                if not term or term == "name":
                    service_centers = service_centers.filter(name__icontains=q)

                elif term == "id":
                    service_centers = service_centers.filter(id__icontains=q)

                elif term == "phone_number":
                    service_centers = service_centers.filter(phone_number__icontains=q)


            serializer = MapServiceCenterSerializer(
                service_centers,
                agent=request.agent,
                many=True).data

        return JsonResponse(
            data=serializer,
            extra={"check_location": request.agent.check_location,
                   "appearance": Appearance.choices},
        )
