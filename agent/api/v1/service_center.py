import datetime
import logging
from django.core.validators import validate_email
from django.db import transaction
from django.utils.translation import gettext as _

from accounts.models import User
from agent.serializers import OmanAgentServiceCenterSerializer, TurkeyAgentServiceCenterSerializer, \
    TurkeyUpdateServiceCenterSerializer, OmanAgentUpdateServiceCenterSerializer
from constants import Appearance
from core.http import JsonResponse
from django.db.models import Q, Count

from agent.pagination import CentersCursorPagination

from locations.models import Country
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.permissions import IsAgent
from saleor.utils import Saleor
from service_center.models import ServiceCenter, ServiceCenterShift, ServiceCenterServiceType, \
    ServiceCenterServiceTypeCapacity, ServiceCenterConfig
from service_center.serializers.service_center import ServiceCenterSerializer, TurkeyAgentResponseSerializer, \
    OmanServiceCenterResponseSerializer

logger = logging.getLogger("agent")


class ServiceCenterAPI(APIView):
    permission_classes = (IsAuthenticated, IsAgent)
    pagination_class = CentersCursorPagination()

    def get(self, request, service_center_id=None):
        _visit_statuses = request.query_params.get("visit_statuses")
        _type_ids = request.query_params.get("center_types")
        _region_ids = request.query_params.get("region_ids")
        _agents_id = request.query_params.get("agents_list")
        _cities_id = request.query_params.get("cities_list")
        _start_date = request.query_params.get("start_date")
        _end_date = request.query_params.get("end_date")

        if service_center_id:
            self.exception_data = {"status": 400, "message": _("Service center not found.")}
            queryset = ServiceCenter.objects.get_by_country_code(country_code=request.agent_country_code).get(
                id=service_center_id)
            if request.agent_country_code == "90":
                serializer = TurkeyAgentResponseSerializer
            elif request.agent_country_code == "255":
                serializer = TurkeyAgentResponseSerializer
            else:
                serializer = OmanServiceCenterResponseSerializer

            return JsonResponse(
                data=serializer(queryset, agent=request.agent, lang=request.lang).data,
                extra={"check_location": request.agent.check_location,
                       "appearance": Appearance.choices},
            )
        else:
            self.exception_data = {"message": _("Error in showing centers")}
            service_centers = ServiceCenter.objects.annotate(
                total_visit=Count(
                    "visits"
                ),
            ).select_related(
                "fast_agent",
                "default_location_address__region__city",
                "default_billing_address__region__city",
                "default_shipping_address__region__city"
            ).get_by_country_code(country_code=request.agent_country_code)

            if not request.agent.test_mode:
                service_centers = service_centers.filter(fast_agent__test_mode=False)

            if _region_ids:
                _region_ids = _region_ids.split(",")
                service_centers = (
                    ServiceCenter.objects.prefetch_related("types")
                    .select_related("user", "agent", "fast_agent")
                    .filter(default_location_address_id__in=_region_ids)
                )

            if request.query_params.get("q"):
                search = request.query_params["q"]
                service_centers = service_centers.filter(
                    Q(name__icontains=search)
                    | Q(user__username__icontains=search)
                    | Q(phone_number__icontains=search)
                    | Q(user__first_name__icontains=search)
                    | Q(user__last_name__icontains=search)
                    | Q(id__icontains=search)

                )

            if _visit_statuses:
                _visit_statuses = _visit_statuses.split(",")
                service_centers = service_centers.filter(visit_status__in=_visit_statuses)

            if _type_ids:
                _type_ids = _type_ids.split(",")
                service_centers = service_centers.filter(service_types__service_type__in=_type_ids)

            if _cities_id:
                _cities_id = _cities_id.split(",")
                service_centers = service_centers.filter(default_location_address__region__city_id__in=_cities_id)

            if _agents_id:
                _agents_id = _agents_id.split(",")
                service_centers = service_centers.filter(fast_agent__id__in=_agents_id)

            if _start_date:
                _start_date = datetime.datetime.strptime(_start_date, "%Y-%m-%d")

            if _end_date:
                _end_date = datetime.datetime.strptime(_end_date, "%Y-%m-%d")

            if _start_date and _end_date:
                if _start_date < _end_date:
                    service_centers = service_centers.filter(created_at__gte=_start_date, created_at__lt=_end_date)
                elif _start_date == _end_date:

                    next_day = _end_date + datetime.timedelta(days=1)
                    service_centers = service_centers.filter(created_at__gte=_start_date, created_at__lt=next_day)
                else:
                    return JsonResponse(message="end date must be greater than or equal to start date", status=400)

            elif _end_date:
                service_centers = service_centers.filter(created_at__lt=_end_date)

            elif _start_date:
                service_centers = service_centers.filter(created_at__gte=_start_date)

            paginated_queryset = self.pagination_class.paginate_queryset(service_centers, request)
            if request.agent_country_code == "90":
                serializer = TurkeyAgentResponseSerializer
            elif request.agent_country_code == "255":
                serializer = TurkeyAgentResponseSerializer
            else:
                serializer = OmanServiceCenterResponseSerializer

            serializer = serializer(
                paginated_queryset,
                agent=request.agent,
                lang=request.lang,
                many=True,
                fields=["fast_agent", "id", "name", "visit_status", "address", "is_address_synced",
                        "visit_counts"]).data

            response = self.pagination_class.get_paginated_response(serializer).data

            return JsonResponse(
                data=response,
                extra={
                    "check_location": request.agent.check_location,
                    "appearance": Appearance.choices
                }
            )

    @transaction.atomic
    def post(self, request):
        if request.agent.test_mode:
            return JsonResponse(status=400, message=_("No access to register a service center."))

        self.exception_data = {"message": _("country code not found"), "status": 400}
        country_code = request.data["user"]["country_code"]
        if country_code == "+90":
            serializer_class = TurkeyAgentServiceCenterSerializer
        elif country_code == "+968":
            serializer_class = OmanAgentServiceCenterSerializer
        elif country_code == "+255":
            serializer_class = TurkeyAgentServiceCenterSerializer

        else:
            return JsonResponse(
                status=400,
                message=_("Invalid code"),
            )
        try:
            serializer = serializer_class(data=request.data, request=request)
            if not serializer.is_valid():
                return JsonResponse(
                    message=_("data is not valid"),
                    status=400,
                    data=serializer.errors,
                )
            service_center = serializer.save()
            try:
                saleor = Saleor(site='gmall')
                saleor.create_customer(service_center=service_center)
            except Exception as e:
                logger.error("Error in syncing service center's data.")

            return JsonResponse(
                status=200,
                message=_("The service center has been successfully created."),
                data=service_center.id
            )
        except Exception as e:
            logger.error("SC AGENT CREATE {}".format(e))
            return JsonResponse(
                message=_("Error in registering service center information."),
                status=400,
            )

    @transaction.atomic
    def put(self, request, service_center_id):
        if request.agent.test_mode:
            return JsonResponse(status=400, message=_("No access to edit a service center."))
        if request.agent_country_code == "90":
            update_serializer = TurkeyUpdateServiceCenterSerializer
        elif request.agent_country_code == "255":
            update_serializer = TurkeyUpdateServiceCenterSerializer
        else:
            update_serializer = OmanAgentUpdateServiceCenterSerializer

        service_center = ServiceCenter.objects.get_by_country_code(country_code=request.agent_country_code).get(
            id=service_center_id)

        update_serializer = update_serializer(data=request.data, instance=service_center, partial=True,
                                              context={"agent": request.agent})
        if not update_serializer.is_valid():
            return JsonResponse(status=400, data=update_serializer.errors)

        service_center = update_serializer.save()

        return JsonResponse(
            message=_("The service center has been successfully updated."),
        )
