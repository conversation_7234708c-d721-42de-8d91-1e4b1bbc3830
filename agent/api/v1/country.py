import logging

from core.http import JsonResponse
from locations.models import Country
from locations.serializers import CountrySerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.permissions import IsAgent

logger = logging.getLogger("agent")


class AgentCountry(APIView):
    # authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request):
        return JsonResponse(
            data=CountrySerializer(
                Country.objects.filter(is_default=False,), many=True,
            ).data
        )
