from django.urls import path

from agent.api.v1 import *
from agent.api.v1.resend_otp import AgentResendOTPAPI

app_name = "agent"

urlpatterns = [
    path("", AgentAPI.as_view(), name="agent"),
    path("login/", AgentLoginAPI.as_view(), name="agent_login"),
    path("cities/", AgentCity.as_view(), name="agent_city"),
    path("regions/", RegionAPI.as_view(), name="agent_region"),
    path("provinces/", AgentProvince.as_view(), name="agent_province"),
    path("service-centers/", ServiceCenterAPI.as_view(), name="agent_service_center"),
    path("service-center-types/", ServiceTypeAPI.as_view(), name="agent_service_center_types"),
    path("near-service-centers/", NearServiceCenters.as_view(), name="near_service_centers"),
    path("service-centers/<int:service_center_id>/", ServiceCenterAPI.as_view(), name="agent_service_center"),
    path(
        "service-centers/<int:service_center_id>/images/",
        ServiceCenterImage.as_view(),
        name="agent_service_center_image",
    ),
    path(
        "service-centers/<int:service_center_id>/images/<int:image_id>/",
        ServiceCenterImage.as_view(),
        name="agent_service_center_image",
    ),
    path("service-centers/<int:service_center_id>/focus/", FocusAPI.as_view(), name="focus"),
    path("captcha/", CaptchaAPI.as_view(), name="captcha-api"),
    path("refresh/", TokenRefreshView.as_view(), name="refresh-view"),

    path("service-centers/shifts/", ServiceCenterShiftsAPI.as_view(), name="shifts"),
    path("agent-profile/", AgentProfile.as_view(), name="agent-profile"),
    path("agent-profile/image/", AgentProfile.as_view(), name="agent-profile-image"),
    path("countries/", AgentCountry.as_view(), name="agent-countries"),
    path("service-center-addresses/", ServiceCenterAddresses.as_view(), name="shipping_address"),
    path("agents/", AgentsAPI.as_view(), name="filter_agent"),
    path("sync-service-center-address/", SyncServiceCenterAddress.as_view(), name="sync_service_center_address"),
    path("visit/", AgentVisitAPI.as_view(), name="agent_visit"),
    path("map-service-centers/", MapServiceCenterAPI.as_view(), name="map_service_centers"),
    path("brands/", BrandAgentsAPI.as_view(), name="agent_brand"),
    path("resend_otp/", AgentResendOTPAPI.as_view(), name="resend_otp"),

    path("import_city_region/", import_city_region, name="import_city_region")
]
