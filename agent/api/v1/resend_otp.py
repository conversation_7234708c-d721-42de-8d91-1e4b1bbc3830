import logging

from django.utils.translation import gettext as _

from constants.otp_types import OTPType
from core.http import JsonR<PERSON>ponse
from core.utils import timezone
from core.validators import mobile_number_validator
from otp.managers import OtpValidator
from rest_framework.views import APIView
from unidecode import unidecode
from accounts.models import *

from agent.models import Agent

from rest_framework.permissions import IsAuthenticated
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.parsers import JSONParser
import hashlib

from django.utils.translation import activate, get_language, deactivate
from django.core.exceptions import ValidationError
from django.core.cache import cache

from rest_framework_simplejwt.tokens import RefreshToken

logger = logging.getLogger("agent")
login_logger = logging.getLogger("login")


class AgentResendOTPAPI(APIView):
    parser_classes = (JSONParser,)
    renderer_classes = (JSONRenderer,)

    def post(self, request):
        mobile_number = unidecode(str(request.data['mobile_number']))

        self.exception_data = {"message": _("invalid phone number")}
        mobile_number_validator(mobile_number)

        self.exception_data = {"message": _("there is no agent with this phone number")}
        agent = Agent.objects.get(user__username=mobile_number, is_active=True)


        messaging_service = request.data.get("messaging_service", False)

        if messaging_service == "SMS":
            confirmed, message = OtpValidator.send(mobile_number, OTPType.AGENT_LOGIN,
                                                   text="Your login verification code is: {}",
                                                   messaging_service=messaging_service)
        elif messaging_service == "WHATSAPP":
            confirmed, message = OtpValidator.send(mobile_number, OTPType.AGENT_LOGIN,
                                                   text="Your login verification code is: {}",
                                                   messaging_service=messaging_service)
        else:
            return JsonResponse(message=_("invalid messaging_service"), status=400)

        if confirmed:
            return JsonResponse(message=_("otp code sent"), status=200)
        else:
            return JsonResponse(message=message, status=404)
