from django.utils.translation import gettext as _
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.views import APIView
from core.http import JsonResponse


class TokenRefreshView(APIView):
    def post(self, request):
        try:
            refresh = RefreshToken(request.COOKIES['refresh_token'])
            access = str(refresh.access_token)

            response = JsonResponse()
            response.set_cookie(key="access_token", value=access)

            return response
        except Exception as e:
            return JsonResponse(message=_("Invalid refresh token"))
