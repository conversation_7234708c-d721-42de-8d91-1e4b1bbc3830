from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from core.http import JsonResponse
from agent.permissions import IsAgent
from quick_book.models import Brand


class BrandAgentsAPI(APIView):
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request):
        brands = Brand.objects.all().values("id", "name")
        return JsonResponse(data=[{"value": brand['id'], "label": brand['name']} for brand in brands], status=200)
