import logging
from django.utils.translation import gettext as _

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from core.http import JsonResponse
from service_center.models import ServiceCenter

from saleor.utils import Saleor

from agent.permissions import IsAgent

logger = logging.getLogger("agent")


class SyncServiceCenterAddress(APIView):
    # authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsAgent)

    def post(self, request):
        agent = request.agent

        self.exception_data = {"status": 400, "message": _("Incorrect Service Center ID.")}
        service_center = ServiceCenter.objects.get_by_country_code(country_code=request.agent_country_code).get(
            id=request.data["service_center_id"])

        self.exception_data = {"status": 409, "message": _("address already added.")}
        saleor = Saleor(site="gmall")
        saleor.create_address(agent, service_center)

        return JsonResponse(status=200, message=_("Address successfully added."))

    def delete(self, request):
        agent = request.agent

        self.exception_data = {"status": 400, "message": _("Incorrect Service Center ID.")}
        service_center = ServiceCenter.objects.get_by_country_code(country_code=request.agent_country_code).get(
            id=request.data["service_center_id"])

        self.exception_data = {"status": 409, "message": _("please try later.")}
        saleor = Saleor(site='gmall')
        saleor.delete_address(agent, service_center)

        return JsonResponse(status=200, message=_("Address successfully deleted."))
