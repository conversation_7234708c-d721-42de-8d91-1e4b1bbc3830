import logging
from django.utils.translation import gettext as _

from core.http import JsonResponse
from locations.models import State
from locations.serializers import StateSerializer
from core.authentication import Cookie<PERSON><PERSON>TAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.permissions import IsAgent
from translation.utils import translation_checker

logger = logging.getLogger("agent")


class AgentProvince(APIView):
    # authentication_classes = (CookieJWTAuthentication,)
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request):
        self.exception_data = {"message": _("error occurred")}
        states = State.objects.select_related("country").filter(
            country__code=int(request.query_params["country_code"]),
            is_default=False,
        ).order_by("priority")
        return JsonResponse(
            data=StateSerializer(
                states,
                many=True,
            ).data,
            extra={"display_name": translation_checker(states[0].display_name.capitalize(), request.lang),
                   "city_display_name": translation_checker(states[0].country.mapping_field_name("city").capitalize(),
                                                            request.lang), }
        )
