import logging

from core.http import JsonResponse
from django.db.models import Q
from geopy.distance import geodesic
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.permissions import IsAgent
from service_center.models import ServiceCenter
from service_center.serializers import ServiceCenterSerializer

logger = logging.getLogger("agent")


class NearServiceCenters(APIView):
    # authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request):
        service_centers = []
        for service_center in ServiceCenter.objects.get_by_country_code(country_code=request.agent_country_code).filter(
                Q(agent__isnull=True) | Q(agent=request.agent) | Q(fast_agent=request.agent),
                is_active=True,
        ):
            if (
                    geodesic(
                        (service_center.address.latitude, service_center.address.longitude),
                        (request.query_params.get("latitude", 0), request.query_params.get("longitude", 0)),
                    ).kilometers
                    < 2
            ):
                service_centers.append(service_center)

        return JsonResponse(
            data=ServiceCenterSerializer(
                service_centers, many=True, fields=("id", "name", "grade", "type", "address", "is_car_rental")
            ).data
        )
