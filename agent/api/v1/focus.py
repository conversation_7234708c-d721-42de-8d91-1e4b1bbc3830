import datetime
import logging

from django.utils.translation import gettext as _

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from constants import VisitStatus
from core.http import JsonResponse

from agent.models import AgentVisit
from agent.permissions import IsAgent
from agent.utils import focus_sms
from core.utils.sms_microservice_facade import SMSServiceFacade
from service_center.models import (
    ServiceCenter
)
from saleor.utils import Saleor

logger = logging.getLogger("agent")


class FocusAPI(APIView):
    # authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsAgent)

    def put(self, request, service_center_id):
        service_center = ServiceCenter.objects.get(id=service_center_id)

        try:
            saleor = Saleor(site='gmall')
            saleor.create_customer(service_center=service_center)
        except Exception as e:
            logger.error("Error in syncing service center's data.")

        if (
                (not service_center.fast_agent and not service_center.agent)
                or request.agent.test_mode or service_center.visit_status != VisitStatus.FAST
        ):
            return JsonResponse(status=400, message=_("Lack of access to register the focus for service point."))
        if (
                service_center.name == None or
                service_center.phone_number == None or
                service_center.appearance == None or
                service_center.shelves_count == None or
                service_center.pits_count == None or
                service_center.employees_count == None or
                service_center.service_bay_count == None or
                service_center.jac_equipment_count == None or
                service_center.center_size == None or
                service_center.inventory_storage_size == None or
                service_center.customer_lounge_size == None or
                service_center.storefront_area_size == None or
                service_center.license_expiration == None or
                service_center.administrative_area_size == None or
                service_center.default_shipping_address == None or
                service_center.default_billing_address == None
        ):
            return JsonResponse(status=400, message=_("All required information must be filled in to focus."))

        # check if agent needs to be around auto service.
        # if request.agent.check_location:
        #     if (
        #             geodesic(
        #                 (service_center.address.latitude, service_center.address.longitude),
        #                 (request.data.get("user_latitude", 0), request.data.get("user_longitude", 0)),
        #             ).kilometers
        #             > 1
        #     ):
        #         return JsonResponse(status=400,
        #                             message="To register a focus, you must be within the center's vicinity.")

        AgentVisit.objects.create(
            agent=request.agent,
            service_center=service_center,
            status=VisitStatus.FOCUS,
            start_at=datetime.datetime.now(),
            end_at=datetime.datetime.now(),
        )

        service_center.visit_status = VisitStatus.FOCUS
        service_center.is_active = True
        service_center.agent = request.agent
        service_center.save()
        data = {
            "phone_number": str(service_center.user),
            "text": "Your center's Focus visit has been successfully completed.",
            "messaging_service": "WHATSAPP"
        }
        sms_service_facade_instance = SMSServiceFacade()
        response, _status = sms_service_facade_instance.create_message_request(**data)
        # focus_sms(user=service_center.user, link="garajmall.com")
        return JsonResponse(message=_("The center's service focus operation has been successfully completed."))
