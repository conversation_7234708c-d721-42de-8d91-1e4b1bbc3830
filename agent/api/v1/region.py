import logging

from core.http import JsonResponse
from locations.models import Region
from locations.serializers import RegionSerializer
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.permissions import IsAgent
from translation.utils import translation_checker

logger = logging.getLogger("agent")


class RegionAPI(APIView):
    # authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request):
        city_id = request.query_params["city_id"]
        regions = Region.objects.filter(is_default=False).order_by("priority")
        if city_id:
            regions = regions.filter(city_id=city_id)
        display_name = regions[0].display_name
        return JsonResponse(data=RegionSerializer(regions, many=True).data,
                            extra={"display_name": translation_checker(display_name.capitalize(), request.lang)})
