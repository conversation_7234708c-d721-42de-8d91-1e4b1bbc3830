import logging
import uuid
from random import randint
from django.utils.translation import gettext as _

from core.cache import cache, get_hashed_cache_key
from core.captcha import create_image
from core.http import JsonResponse
from rest_framework.views import APIView

logger = logging.getLogger("agent")


class CaptchaAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        self.exception_data = {"message": _("error in loading captcha.")}

        width = request.query_params.get("width", 105)
        height = request.query_params.get("height", 35)
        font = request.query_params.get("font", "core/static/core/fonts/roboto/Samuel-font.ttf")
        font_color = request.query_params.get("font_color", "#17245f")
        font_size = request.query_params.get("font_size", 26)
        color = request.query_params.get("color", "#06df70")
        base_color = request.query_params.get("base_color", "#ffffff")
        base_multiplier = request.query_params.get("base_multiplier", 3)

        if not font_color.startswith("#"):
            font_color = "#" + font_color

        if not color.startswith("#"):
            color = "#" + color

        if not base_color.startswith("#"):
            base_color = "#" + base_color

        code = str(randint(1111, 9999))
        key = get_hashed_cache_key("Captcha", code, request.query_params)
        value = cache.get(key)
        captcha_uuid = uuid.uuid4()
        if value:
            cache.set(captcha_uuid, value["hash_code"], 100)
            data = {"image_str": value["img_str"], "captcha_uuid": captcha_uuid}
            return JsonResponse(data=data)
        value = create_image(
            code=code,
            width=width,
            height=height,
            font=font,
            font_color=font_color,
            font_size=font_size,
            color=color,
            base_color=base_color,
            base_multiplier=base_multiplier,
        )
        cache.set(captcha_uuid, value["hash_code"], 100)
        cache.set(key, value, None)
        data = {"image_str": value["img_str"], "captcha_uuid": captcha_uuid}
        return JsonResponse(data=data)
