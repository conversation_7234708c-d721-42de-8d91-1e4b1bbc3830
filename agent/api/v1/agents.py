from rest_framework.views import APIView
from core.http import JsonResponse
from agent.models import Agent
from agent.serializers import AgentFilterSerializer
from rest_framework.permissions import IsAuthenticated
from agent.permissions import IsAgent

class AgentsAPI(APIView):
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request):
        agents = Agent.objects.filter(test_mode=False)
        serializer = AgentFilterSerializer(agents, many=True)
        return JsonResponse(serializer.data)
