import logging

from django.contrib.contenttypes.models import ContentType
from django.utils.translation import gettext as _

from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from core.http import JsonResponse
from files.models import Image
from files.serializers import ImageSerializer

from agent.permissions import IsAgent

logger = logging.getLogger("agent")


class ServiceCenterImage(APIView):
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request, service_center_id):
        return JsonResponse(
            data=ImageSerializer(
                Image.objects.filter(content_type__model="ServiceCenter", object_id=service_center_id), many=True
            ).data
        )

    def post(self, request, service_center_id):
        if request.agent.test_mode:
            return JsonResponse(status=400, message=_("no accesses to add image."))

        try:
            Image.objects.create(
                image=request.data["image"],
                content_type=ContentType.objects.get(model="servicecenter"),
                object_id=service_center_id,
                name=request.data["name"]
            )
        except Exception as e:
            logger.error("IMAGE-API| error {}".format(str(e)))
            return JsonResponse(message=str(e), status=405)

        return JsonResponse(message=_("Image added successfully."))

    def delete(self, request, service_center_id, image_id):
        if request.agent.test_mode:
            return JsonResponse(status=400, message="no access to check ")

        Image.objects.get(content_type__model="ServiceCenter", object_id=service_center_id, pk=image_id).delete()
        return JsonResponse(message=_("image deleted successfully."))
