import logging

from core.http import JsonResponse
from locations.models import City
from locations.serializers import CitySerializer
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.permissions import IsAgent
from translation.utils import translation_checker

logger = logging.getLogger("agent")


class AgentCity(APIView):
    # authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsAgent)

    def get(self, request):
        cities = City.objects.select_related("state").filter(is_default=False).order_by('priority')
        if request.query_params.get('state_id'):
            cities = cities.filter(state_id=request.query_params.get('state_id'))
        display_name = cities[0].display_name
        return JsonResponse(
            CitySerializer(cities, many=True).data,
            extra={"display_name": translation_checker(display_name.capitalize(), request.lang)}
        )
