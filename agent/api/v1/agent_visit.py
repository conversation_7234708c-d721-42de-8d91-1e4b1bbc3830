import datetime
import logging
from django.utils import timezone
from django.utils.translation import gettext as _
from core.utils import timezone

from core.http import JsonResponse
from geopy.distance import geodesic
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from agent.models import AgentVisit
from agent.permissions import IsAgent
from service_center.models import (
    ServiceCenter
)

logger = logging.getLogger("agent")


class AgentVisitAPI(APIView):
    # authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsAgent)

    def post(self, request):
        service_center_id = request.data["service_center_id"]
        service_center = ServiceCenter.objects.get(id=service_center_id)

        if (
                (not service_center.fast_agent and not service_center.agent)
                # or request.agent.test_mode
        ):
            return JsonResponse(status=400, message=_("Lack of access to visit service point."))
        # check if agent needs to be around auto service.
        if request.agent.check_location:
            if (
                    geodesic(
                        (service_center.default_location_address.latitude, service_center.default_location_address.longitude),
                        (request.data.get("user_latitude", 0), request.data.get("user_longitude", 0)),
                    ).kilometers
                    > .5
            ):
                return JsonResponse(status=400,
                                    message=_("To visit a center, you must be within the center's vicinity."))

        previous_visit = AgentVisit.objects.filter(service_center=service_center)
        if previous_visit:
            previous_visit = previous_visit.last()
            if ((timezone.now() - previous_visit.start_at).total_seconds() / 3600) < 24:
                return JsonResponse(status=400, message=_("more than one visit per day is impossible."))

        AgentVisit.objects.create(
            agent=request.agent,
            service_center=service_center,
            start_at=datetime.datetime.now(),
            end_at=datetime.datetime.now(),
        )
        return JsonResponse(message=_("visit done."))
