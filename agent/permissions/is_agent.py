import logging
from agent.models import Agent
from core.permissions import BasePermission

logger = logging.getLogger("agent")

class IsAgent(BasePermission):
    message = "Access denied! Only agents are permitted."

    def has_permission(self, request, view):
        try:
            request.agent = Agent.objects.select_related("user", "state").get(user=request.user, is_active=True)
            request.agent_country_code = request.agent.user.country_code.replace("+", "")
            return True
        except Exception as e:
            logger.error("agent check permission error: {}".format(e))
            request.agent = None
            return False
