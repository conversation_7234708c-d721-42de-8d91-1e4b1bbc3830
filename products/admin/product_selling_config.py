from core import admin


class ProductSellingConfigAdmin(admin.ModelAdmin):
    list_display = ("id", 'selling_config', 'product')
    list_editable = ()
    search_fields = (
        "id", "product__name", "selling_config__name", "selling_config__stock_type", "selling_config__type")
    list_filter = ("selling_config__stock_type", "selling_config__type")
    raw_id_fields = ("selling_config", "product")

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related('selling_config', 'product__site')
