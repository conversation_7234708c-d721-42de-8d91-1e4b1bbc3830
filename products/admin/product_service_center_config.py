from core.admin import AbstractBaseAdmin


class ProductServiceCenterConfigAdmin(AbstractBaseAdmin):
    list_display = ("id", "service_center", 'product_name', "is_active", "priority")
    raw_id_fields = ("service_center", "product_selling_config")
    search_fields = ("service_center__id", "service_center__name", "product_selling_config__product__name",
                     "product_selling_config__product__sku",)
    list_filter = (
    "product_selling_config__selling_config__stock_type", "product_selling_config__selling_config__type", "is_active")

    def product_name(self, obj):
        return obj.product_selling_config.product.name
