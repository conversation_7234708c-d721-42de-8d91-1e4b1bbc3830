# Generated by Django 3.2.10 on 2024-07-21 13:17

from django.db import migrations, models
import django.db.models.deletion
import products.utils.delivery


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('service_center', '0011_servicecenter_synced_saleor_address'),
        ('sites', '0002_alter_domain_unique'),
        ('quick_book', '0005_auto_20240422_0837'),
        ('products', '0004_auto_20240407_0628'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductSellingConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=50)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('stock_type', models.CharField(choices=[('b2b_purchase', 'B2B Purchase'), ('self_stock', 'Self Stock'), ('trusty', 'Trusty')], default='self_stock', max_length=50)),
                ('type', models.CharField(choices=[('service', 'service in center'), ('delivery', 'pickup in center')], default='service', max_length=50)),
                ('charge_price', models.PositiveIntegerField(default=0)),
                ('shift_delay', models.PositiveIntegerField(default=0)),
                ('service_amount', models.PositiveIntegerField(default=0)),
                ('happy_hour_service_amount', models.PositiveIntegerField(default=0)),
                ('delivery_amount', models.PositiveIntegerField(default=0)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='product_selling_configs', to='quick_book.product')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ProductServiceCenterConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('auto_balance_percentage', models.PositiveIntegerField(blank=True, null=True)),
                ('price', models.PositiveIntegerField(blank=True, null=True)),
                ('selectable_days', models.PositiveIntegerField(default=4)),
                ('product_selling_config', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='product_service_center_configs', to='products.productsellingconfig')),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_service_center_configs', to='service_center.servicecenter')),
                ('sites', models.ManyToManyField(related_name='product_service_center_configs', to='sites.Site')),
            ],
            options={
                'unique_together': {('service_center', 'product_selling_config')},
            },
            bases=(models.Model, products.utils.delivery.DeliveryUtils),
        ),
    ]
