# Generated by Django 3.2.10 on 2023-09-17 08:58

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('content', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PeriodLimit',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('value', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)])),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
            ],
            options={
                'db_table': 'matrix_products_period_limit',
            },
        ),
        migrations.CreateModel(
            name='ProductSeller',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('stock', models.PositiveIntegerField()),
                ('stock_type', models.CharField(choices=[('b2b_purchase', 'B2B Purchase'), ('self_stock', 'Self Stock')], default='self_stock', max_length=50)),
                ('metadata', models.JSONField(blank=True, null=True)),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('service', models.BooleanField(default=False)),
                ('delivery', models.BooleanField(default=False)),
                ('holidays', models.BooleanField(default=False)),
                ('selectable_days', models.PositiveIntegerField(default=4)),
                ('shifts_delay', models.PositiveIntegerField(default=0)),
                ('extra_charge', models.PositiveIntegerField(default=0)),
                ('shifts_start_date', models.DateTimeField(blank=True, null=True)),
                ('service_amount', models.PositiveIntegerField(default=0)),
                ('delivery_amount', models.PositiveIntegerField(default=0)),
                ('stock_percentage', models.PositiveIntegerField(default=0)),
                ('shift_limit', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)])),
                ('sat', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('sun', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('mon', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('tue', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('wed', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('thu', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('fri', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
            ],
            options={
                'db_table': 'matrix_products_product_seller',
            },
        ),
        migrations.CreateModel(
            name='ProductServiceCenter',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('start_date', models.DateTimeField(blank=True, null=True)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('service', models.BooleanField(default=False)),
                ('delivery', models.BooleanField(default=False)),
                ('holidays', models.BooleanField(default=False)),
                ('selectable_days', models.PositiveIntegerField(default=4)),
                ('shifts_delay', models.PositiveIntegerField(default=0)),
                ('extra_charge', models.PositiveIntegerField(default=0)),
                ('shifts_start_date', models.DateTimeField(blank=True, null=True)),
                ('service_amount', models.PositiveIntegerField(default=0)),
                ('delivery_amount', models.PositiveIntegerField(default=0)),
                ('stock_percentage', models.PositiveIntegerField(default=0)),
                ('shift_limit', models.PositiveIntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)])),
                ('sat', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('sun', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('mon', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('tue', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('wed', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('thu', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('fri', models.CharField(blank=True, choices=[('first', 'First'), ('second', 'Second'), ('all', 'All')], max_length=30, null=True)),
                ('off_days', models.ManyToManyField(blank=True, related_name='product_service_centers', to='content.Date')),
                ('period_limits', models.ManyToManyField(blank=True, related_name='product_service_centers', to='products.PeriodLimit')),
                ('product_seller', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='product_scs', to='products.productseller')),
            ],
            options={
                'db_table': 'matrix_products_product_service_center',
            },
        ),
    ]
