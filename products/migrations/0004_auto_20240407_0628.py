# Generated by Django 3.2.10 on 2024-04-07 06:28

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0003_productseller_product'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='productseller',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='productseller',
            name='off_days',
        ),
        migrations.RemoveField(
            model_name='productseller',
            name='product',
        ),
        migrations.RemoveField(
            model_name='productseller',
            name='seller',
        ),
        migrations.RemoveField(
            model_name='productseller',
            name='site',
        ),
        migrations.RemoveField(
            model_name='productservicecenter',
            name='off_days',
        ),
        migrations.RemoveField(
            model_name='productservicecenter',
            name='period_limits',
        ),
        migrations.RemoveField(
            model_name='productservicecenter',
            name='product_seller',
        ),
        migrations.RemoveField(
            model_name='productservicecenter',
            name='service_center',
        ),
        migrations.DeleteModel(
            name='PeriodLimit',
        ),
        migrations.DeleteModel(
            name='ProductSeller',
        ),
        migrations.DeleteModel(
            name='ProductServiceCenter',
        ),
    ]
