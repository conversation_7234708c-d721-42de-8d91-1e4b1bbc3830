# Generated by Django 3.2.10 on 2024-12-03 13:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('saleor', '0018_alter_orderline_items'),
        ('products', '0008_productservicecenterstocktransaction_service_center_balance_percentage'),
    ]

    operations = [
        migrations.AddField(
            model_name='productservicecenterstocktransaction',
            name='order_line',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transaction_stocks', to='saleor.orderline'),
        ),
        migrations.AlterField(
            model_name='productservicecenterstocktransaction',
            name='state',
            field=models.CharField(choices=[('increase', 'Increase'), ('decrease', 'Decrease')], db_index=True, default='increase', max_length=56),
        ),
        migrations.AlterField(
            model_name='productservicecenterstocktransaction',
            name='stock_type',
            field=models.CharField(blank=True, choices=[('b2b_purchase', 'B2B Purchase'), ('self_stock', 'Self Stock'), ('trusty', 'Trusty')], db_index=True, max_length=56, null=True),
        ),
    ]
