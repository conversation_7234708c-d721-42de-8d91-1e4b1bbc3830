# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('sites', '0002_alter_domain_unique'),
        ('service_center', '0001_initial'),
        ('content', '0001_initial'),
        ('sellers', '0001_initial'),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='productservicecenter',
            name='service_center',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='product_service_centers', to='service_center.servicecenter'),
        ),
        migrations.AddField(
            model_name='productseller',
            name='off_days',
            field=models.ManyToManyField(blank=True, related_name='product_sellers', to='content.Date'),
        ),
        migrations.AddField(
            model_name='productseller',
            name='seller',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='product_sellers', to='sellers.seller'),
        ),
        migrations.AddField(
            model_name='productseller',
            name='site',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='product_sellers', to='sites.site'),
        ),
        migrations.AlterUniqueTogether(
            name='productseller',
            unique_together={('seller', 'site', 'stock_type')},
        ),
    ]
