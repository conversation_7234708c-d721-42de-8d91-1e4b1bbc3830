# Generated by Django 3.2.10 on 2024-11-02 13:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('service_center', '0015_auto_20240826_0815'),
        ('quick_book', '0010_alter_product_category'),
        ('products', '0006_auto_20240730_1041'),
    ]

    operations = [
        migrations.RenameField(
            model_name='productservicecenterconfig',
            old_name='price',
            new_name='charge_price',
        ),
        migrations.RenameField(
            model_name='productservicecenterstock',
            old_name='quantity',
            new_name='stock',
        ),
        migrations.RemoveField(
            model_name='productsellingconfig',
            name='charge_price',
        ),
        migrations.RemoveField(
            model_name='productsellingconfig',
            name='delivery_amount',
        ),
        migrations.RemoveField(
            model_name='productsellingconfig',
            name='happy_hour_service_amount',
        ),
        migrations.RemoveField(
            model_name='productsellingconfig',
            name='metadata',
        ),
        migrations.RemoveField(
            model_name='productsellingconfig',
            name='name',
        ),
        migrations.RemoveField(
            model_name='productsellingconfig',
            name='service_amount',
        ),
        migrations.RemoveField(
            model_name='productsellingconfig',
            name='shift_delay',
        ),
        migrations.RemoveField(
            model_name='productsellingconfig',
            name='stock_type',
        ),
        migrations.RemoveField(
            model_name='productsellingconfig',
            name='type',
        ),
        migrations.RemoveField(
            model_name='productservicecenterconfig',
            name='stock',
        ),
        migrations.AddField(
            model_name='productservicecenterstock',
            name='name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='productservicecenterstock',
            name='stock_type',
            field=models.CharField(choices=[('b2b_purchase', 'B2B Purchase'), ('self_stock', 'Self Stock'), ('trusty', 'Trusty')], max_length=56, null=True),
        ),
        migrations.AlterUniqueTogether(
            name='productservicecenterstock',
            unique_together={('service_center', 'product', 'stock_type')},
        ),
        migrations.CreateModel(
            name='SellingConfig',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=50)),
                ('stock_type', models.CharField(choices=[('b2b_purchase', 'B2B Purchase'), ('self_stock', 'Self Stock'), ('trusty', 'Trusty')], default='self_stock', max_length=50)),
                ('type', models.CharField(choices=[('service', 'service in center'), ('delivery', 'pickup in center')], default='service', max_length=50)),
                ('charge_price', models.PositiveIntegerField(default=0)),
                ('shift_delay', models.PositiveIntegerField(default=0)),
                ('service_amount', models.PositiveIntegerField(default=0)),
                ('happy_hour_service_amount', models.PositiveIntegerField(default=0)),
                ('delivery_amount', models.PositiveIntegerField(default=0)),
            ],
            options={
                'unique_together': {('name', 'stock_type', 'type', 'charge_price', 'shift_delay', 'service_amount', 'happy_hour_service_amount', 'delivery_amount')},
            },
        ),
        migrations.CreateModel(
            name='ProductServiceCenterStockTransaction',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.IntegerField(default=0)),
                ('state', models.CharField(choices=[('increase', 'Increase'), ('decrease', 'Decrease')], default='increase', max_length=56)),
                ('stock_type', models.CharField(blank=True, choices=[('b2b_purchase', 'B2B Purchase'), ('self_stock', 'Self Stock'), ('trusty', 'Trusty')], max_length=56, null=True)),
                ('creator_type', models.CharField(choices=[('administrator', 'Administrator'), ('auto', 'Auto')], default='auto', max_length=56)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transaction_sc_stocks', to='quick_book.product')),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transaction_stocks', to='service_center.servicecenter')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='productservicecenterstock',
            name='state',
        ),
        migrations.AddField(
            model_name='productsellingconfig',
            name='selling_config',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, related_name='product_selling_configs', to='products.sellingconfig'),
        ),
    ]
