# Generated by Django 3.2.10 on 2024-07-30 10:41

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('quick_book', '0006_category_image'),
        ('service_center', '0013_bill_billitem'),
        ('products', '0005_productsellingconfig_productservicecenterconfig'),
    ]

    operations = [
        migrations.AddField(
            model_name='productservicecenterconfig',
            name='stock',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.CreateModel(
            name='ProductServiceCenterStock',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.IntegerField(default=0)),
                ('state', models.CharField(choices=[('increase', 'Increase'), ('decrease', 'Decrease')], default='increase', max_length=56)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sc_stocks', to='quick_book.product')),
                ('service_center', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stocks', to='service_center.servicecenter')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
