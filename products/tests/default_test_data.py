# from faker import Faker
# from accounts.models import User
# from django.contrib.sites.models import Site
#
# from agent.models import Agent
# from locations.models import Country, State, City, Region, Address
# from products.models import ProductSellingConfig
# from quick_book.models import Category, Product
# from sellers.models import Seller
# from service_center.models import ServiceCenter
#
#
# def create_duplicated_data():
#     faker = Faker()
#     staff_user = User.objects.create_staff(username=faker.unique.name(), password=faker.password())
#     user = User.objects.create_user(username=faker.unique.name(), password=faker.password())
#     hig = Site.objects.get_or_create(domain="hig.com", name="hig")[0]
#     site = Site.objects.create(domain=faker.url(), name=faker.name())
#
#     return staff_user, user, hig, site
#
#
# def create_duplicated_product_data():
#     faker = Faker()
#
#     staff_user, user, hig, site = create_duplicated_data()
#
#     category = Category.objects.create(name=faker.name(), quickbooks_id=faker.unique.name(), site=site)
#     hig_category = Category.objects.create(name=faker.name(), quickbooks_id=faker.unique.name(), site=hig)
#
#     product = Product.objects.create(name=faker.name(), quickbooks_id=faker.unique.name(), site=site,
#                                      category=category)
#     product_hig = Product.objects.create(name=faker.name(), quickbooks_id=faker.unique.name(), site=hig,
#                                          category=hig_category)
#
#     return staff_user, user, product, product_hig
#
#
# def create_address_data():
#     faker = Faker()
#     country = Country.objects.create(
#         name=faker.name(),
#         phone_code=faker.phone_number()[:9],
#         language_code=faker.language_code(),
#         code=faker.country_code()
#     )
#     state = State.objects.create(
#         name=faker.name(),
#         country=country,
#         code=faker.country_code()
#     )
#     city = City.objects.create(
#         name=faker.name(),
#         state=state,
#     )
#     region = Region.objects.create(
#         name=faker.name(),
#         city=city,
#     )
#     address = Address.objects.create(
#         region=region,
#         description=faker.text(max_nb_chars=100),
#     )
#     return country, state, city, region, address
#
#
# def create_sc_test_data(number: int = 1):
#     faker = Faker()
#     staff_user, user, product, product_hig = create_duplicated_product_data()
#     service_centers, service_centers_objs = [], []
#     for i in range(number):
#         country, state, city, region, address = create_address_data()
#         sc_user = User.objects.create_staff(username=faker.unique.name(), password=faker.password())
#         agent = Agent.objects.create(user=sc_user, state=state)
#         service_centers.append(ServiceCenter(
#             name=faker.unique.name(),
#             phone_number=faker.phone_number(),
#             user=sc_user,
#             fast_agent=agent,
#             agent=agent,
#             seller=Seller.objects.create(user=sc_user, business_name=faker.name(), address=address,
#                                          contact_number=faker.unique.phone_number()[:9],
#                                          business_registration_number=faker.unique.phone_number(),
#                                          ),
#             default_location_address=address
#
#         ))
#
#     for sc in ServiceCenter.objects.bulk_create(service_centers):
#         sc.sites.set([product_hig.site])
#         service_centers_objs.append(sc)
#
#     return service_centers_objs
