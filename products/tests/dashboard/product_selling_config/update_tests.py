# from django.urls import reverse
# from rest_framework.test import APITestCase
# from rest_framework import status
#
# from constants import ServiceDeliveryType, SellerStockType
# from products.models import ProductSellingConfig
# from django.utils.translation import gettext as _
#
# from products.tests.default_test_data import create_duplicated_product_data
#
#
# class ProductSellingConfigUpdateApiTest(APITestCase):
#
#     def setUp(self):
#         self.staff_user, self.user, self.product, self.product_hig = create_duplicated_product_data()
#
#         self.hig_config = ProductSellingConfig.objects.create(name="product selling config test",
#                                                               product=self.product_hig)
#
#         self.update_url = reverse('products:dashboard_v1:product_selling_config', args=[self.hig_config.id])
#
#     def test_update_no_auth(self):
#         response = self.client.put(self.update_url, data={})
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_update_no_perm(self):
#         self.client.force_authenticate(user=self.user)
#         response = self.client.put(self.update_url, data={})
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_update_no_products(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.put(self.update_url, data={})
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("invalid data"))
#
#     def test_update_no_name(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.put(self.update_url, data={"product": self.product_hig.pk})
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("invalid data"))
#
#     def test_update_with_product(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "update_in_test"}
#         response = self.client.put(self.update_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         updated_config = ProductSellingConfig.objects.get(pk=self.hig_config.id)
#         self.assertEqual(updated_config.name, data["name"])
#         self.assertEqual(updated_config.product.id, data["product"])
#
#     def test_update_wrong_format_name(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "A" * 51}
#         response = self.client.put(self.update_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_update_wrong_stock_type(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "A", "stock_type": "wrong_stock_type"}
#         response = self.client.put(self.update_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_update_wrong_type(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "A", "type": "wrong_type"}
#         response = self.client.put(self.update_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_update_wrong_charge_price(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "A", "charge_price": -3}
#         response = self.client.put(self.update_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_update_wrong_shift_delay(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "A", "shift_delay": -3}
#         response = self.client.put(self.update_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_update_wrong_service_amount(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "A", "service_amount": -3}
#         response = self.client.put(self.update_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_update_wrong_happy_hour_service_amount(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "A", "happy_hour_service_amount": -3}
#         response = self.client.put(self.update_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_update_wrong_delivery_amount(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "A", "delivery_amount": -3}
#         response = self.client.put(self.update_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_with_all_optional_data(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product": self.product_hig.id, "name": "A", "delivery_amount": 1,
#                 "happy_hour_service_amount": 2, "service_amount": 3, "shift_delay": 4, "charge_price": 5,
#                 "type": ServiceDeliveryType.SERVICE, "stock_type": SellerStockType.SELF_STOCK}
#         response = self.client.put(self.update_url, data=data)
#         updated_config = ProductSellingConfig.objects.get(pk=self.hig_config.id)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(updated_config.name, data["name"])
#         self.assertEqual(updated_config.delivery_amount, data["delivery_amount"])
#         self.assertEqual(updated_config.happy_hour_service_amount, data["happy_hour_service_amount"])
#         self.assertEqual(updated_config.service_amount, data["service_amount"])
#         self.assertEqual(updated_config.shift_delay, data["shift_delay"])
#         self.assertEqual(updated_config.charge_price, data["charge_price"])
#         self.assertEqual(updated_config.type, data["type"])
#         self.assertEqual(updated_config.stock_type, data["stock_type"])
#         self.assertEqual(updated_config.product.id, data["product"])
