# from faker import Faker
# from django.urls import reverse
# from rest_framework.test import APITestCase, APIClient
# from rest_framework import status
# from products.models import ProductSellingConfig
# from products.serializers.product_selling_config import ProductSellingConfigSerializer
# from django.utils.translation import gettext as _
#
# from products.tests.default_test_data import create_duplicated_product_data
#
#
# class ProductSellingConfigApiTest(APITestCase):
#
#     def setUp(self):
#         faker = Faker()
#
#         self.staff_user, self.user, self.product, self.product_hig = create_duplicated_product_data()
#         self.config1 = ProductSellingConfig.objects.create(name=faker.name(), product=self.product)
#         self.hig_config = ProductSellingConfig.objects.create(name=faker.name(), product=self.product_hig)
#         self.hig_config_2 = ProductSellingConfig.objects.create(name=faker.name(), product=self.product_hig)
#         self.list_url = reverse('products:dashboard_v1:product_selling_configs')
#
#     def test_get_single_product_selling_config_no_auth(self):
#         url = reverse('products:dashboard_v1:product_selling_config', args=[self.config1.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_get_single_product_selling_config_no_perm(self):
#         self.client.force_authenticate(user=self.user)
#         url = reverse('products:dashboard_v1:product_selling_config', args=[self.config1.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_get_single_product_selling_config_wrong_site(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('products:dashboard_v1:product_selling_config', args=[self.config1.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
#         self.assertEqual(response.json()["message"], _("product selling config not found"))
#
#     def test_get_single_product_selling_config_wrong_id(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('products:dashboard_v1:product_selling_config', args=[600000000000000])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
#         self.assertEqual(response.json()["message"], _("product selling config not found"))
#
#     def test_get_single_product_selling_config(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('products:dashboard_v1:product_selling_config', args=[self.hig_config.id])
#         response = self.client.get(url)
#
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         expected_data = ProductSellingConfigSerializer(self.hig_config).data
#         self.assertEqual(response.json()["data"], expected_data)
#
#     def test_unauthenticated_user_access(self):
#         """Test that unauthenticated users cannot access the API."""
#         response = self.client.get(self.list_url)
#
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_no_perm_user_list(self):
#         """Test that unauthenticated users cannot access the API."""
#         self.client.force_authenticate(user=self.user)
#         response = self.client.get(self.list_url)
#
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_get_all_product_selling_configs_with_pagination(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 1, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIn('data', response.json())
#
#         self.assertEqual(len(response.json()['data']), 1)
#
#     def test_get_all_product_selling_configs_with_pagination_check_next_page(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 1, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIsNotNone(response.json()["pagination"]["next"])
#         self.assertIsNotNone(response.json()["pagination"]["next_url"])
#
#     def test_get_all_product_selling_configs_with_pagination_check_previous_page(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 2, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIsNotNone(response.json()["pagination"]["previous"])
#         self.assertIsNotNone(response.json()["pagination"]["previous_url"])
#
#     def test_get_all_product_selling_configs_with_default_pagination(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(len(response.json()['data']), 2)
#         query_set = ProductSellingConfig.objects.filter(product__site__name="hig")
#         expected_data = ProductSellingConfigSerializer(query_set, many=True).data
#         self.assertEqual(response.json()["data"], expected_data)
