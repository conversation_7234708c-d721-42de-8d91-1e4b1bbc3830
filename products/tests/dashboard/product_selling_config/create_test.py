# from faker import Faker
# from django.urls import reverse
# from rest_framework.test import APITestCase, APIClient
# from rest_framework import status
#
# from constants import ServiceDeliveryType, SellerStockType
# from products.models import ProductSellingConfig
# from django.utils.translation import gettext as _
#
# from products.tests.default_test_data import create_duplicated_product_data
# from quick_book.models import Product, Category
#
#
# class ProductSellingConfigCreateApiTest(APITestCase):
#
#     def setUp(self):
#         faker = Faker()
#         self.staff_user, self.user, self.product, self.product_hig = create_duplicated_product_data()
#         self._category = Category.objects.create(name=faker.name(), quickbooks_id=faker.unique.name(),
#                                                  site=self.product_hig.site)
#         Product.objects.create(name=faker.name(), quickbooks_id=faker.unique.name(), site=self.product_hig.site,
#                                category=self._category)
#         self.create_url = reverse('products:dashboard_v1:product_selling_configs')
#
#     def test_create_no_auth(self):
#         response = self.client.post(self.create_url, data={})
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_create_no_perm(self):
#         self.client.force_authenticate(user=self.user)
#         response = self.client.post(self.create_url, data={})
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_create_no_products(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.post(self.create_url, data={})
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("product is required"))
#
#     def test_create_with_wrong_category(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self.product.category.id]}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("product is required"))
#
#     def test_create_with_category(self):
#         self.client.force_authenticate(user=self.staff_user)
#         count_objects = ProductSellingConfig.objects.count()
#         data = {"categories": [self._category.id], "name": "create_in_test"}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#         self.assertEqual(count_objects + 1, ProductSellingConfig.objects.count())
#
#     def test_create_with_wrong_product(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"products": [self.product.id], "name": "create_in_test"}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("product is required"))
#
#     def test_create_with_product(self):
#         self.client.force_authenticate(user=self.staff_user)
#         count_objects = ProductSellingConfig.objects.count()
#         data = {"products": [self.product_hig.id], "name": "create_in_test"}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#         self.assertEqual(count_objects + 1, ProductSellingConfig.objects.count())
#         created_obj = ProductSellingConfig.objects.last()
#         self.assertEqual(created_obj.name, data["name"])
#         self.assertEqual(created_obj.delivery_amount, 0)
#         self.assertEqual(created_obj.happy_hour_service_amount, 0)
#         self.assertEqual(created_obj.service_amount, 0)
#         self.assertEqual(created_obj.shift_delay, 0)
#         self.assertEqual(created_obj.charge_price, 0)
#         self.assertEqual(created_obj.type, ServiceDeliveryType.SERVICE)
#         self.assertEqual(created_obj.stock_type, SellerStockType.SELF_STOCK)
#
#     def test_create_with_product_category(self):
#         self.client.force_authenticate(user=self.staff_user)
#         count_objects = ProductSellingConfig.objects.count()
#         data = {"products": [self.product_hig.id, ], "categories": [self._category.id],
#                 "name": "create_in_test"}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#         self.assertEqual(count_objects + 2, ProductSellingConfig.objects.count())
#
#     def test_create_without_name(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self._category.id]}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_create_wrong_format_name(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self._category.id], "name": "A" * 51}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_create_wrong_stock_type(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self._category.id], "name": "A", "stock_type": "wrong_stock_type"}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_create_wrong_type(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self._category.id], "name": "A", "type": "wrong_type"}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_create_wrong_charge_price(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self._category.id], "name": "A", "charge_price": -3}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_create_wrong_shift_delay(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self._category.id], "name": "A", "shift_delay": -3}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_create_wrong_service_amount(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self._category.id], "name": "A", "service_amount": -3}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_create_wrong_happy_hour_service_amount(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self._category.id], "name": "A", "happy_hour_service_amount": -3}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_create_wrong_delivery_amount(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"categories": [self._category.id], "name": "A", "delivery_amount": -3}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(_("invalid data"), response.json()["message"])
#
#     def test_with_all_optional_data(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"products": [self.product_hig.id], "name": "A", "delivery_amount": 1,
#                 "happy_hour_service_amount": 2, "service_amount": 3, "shift_delay": 4, "charge_price": 5,
#                 "type": ServiceDeliveryType.SERVICE, "stock_type": SellerStockType.SELF_STOCK}
#         response = self.client.post(self.create_url, data=data)
#         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#         created_obj = ProductSellingConfig.objects.last()
#         self.assertEqual(created_obj.name, data["name"])
#         self.assertEqual(created_obj.delivery_amount, data["delivery_amount"])
#         self.assertEqual(created_obj.happy_hour_service_amount, data["happy_hour_service_amount"])
#         self.assertEqual(created_obj.service_amount, data["service_amount"])
#         self.assertEqual(created_obj.shift_delay, data["shift_delay"])
#         self.assertEqual(created_obj.charge_price, data["charge_price"])
#         self.assertEqual(created_obj.type, data["type"])
#         self.assertEqual(created_obj.stock_type, data["stock_type"])
#         self.assertEqual(created_obj.product.id, data["products"][0])
