# from django.urls import reverse
# from rest_framework.test import APITestCase, APIClient
# from rest_framework import status
# from django.utils.translation import gettext as _
#
# from products.models import ProductServiceCenterConfig, ProductSellingConfig
# from products.serializers.product_service_center_config import CreateProductServiceCenterConfigSerializer
# from products.tests.default_test_data import create_duplicated_data, create_sc_test_data, create_duplicated_product_data
#
#
# class ProductServiceCenterConfigApiTest(APITestCase):
#
#     def setUp(self):
#         self.staff_user, self.user, self.product, self.product_hig = create_duplicated_product_data()
#         self.sc = create_sc_test_data(number=3)
#         self.hig_config = ProductSellingConfig.objects.create(name="product selling config test",
#                                                               product=self.product_hig)
#
#         self.pscc = ProductServiceCenterConfig.objects.create(
#             service_center=self.sc[0],
#             product_selling_config=self.hig_config,
#
#         )
#         self.pscc.sites.set([self.product_hig.site])
#
#         self.pscc1 = ProductServiceCenterConfig.objects.create(
#             service_center=self.sc[1],
#             product_selling_config=self.hig_config,
#
#         )
#         self.pscc1.sites.set([self.product_hig.site])
#
#         self.pscc2 = ProductServiceCenterConfig.objects.create(
#             service_center=self.sc[2],
#             product_selling_config=self.hig_config,
#
#         )
#         self.list_url = reverse('products:dashboard_v1:product_sc_configs')
#
#     def test_get_single_product_sc_config_no_auth(self):
#         url = reverse('products:dashboard_v1:product_sc_config', args=[self.pscc.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_get_single_product_sc_config_no_perm(self):
#         self.client.force_authenticate(user=self.user)
#         url = reverse('products:dashboard_v1:product_sc_config', args=[self.pscc.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_get_single_product_sc_config_wrong_site(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('products:dashboard_v1:product_sc_config', args=[self.pscc2.id])
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
#         self.assertEqual(response.json()["message"], _("product service center config not found"))
#
#     def test_get_single_product_sc_config_wrong_id(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('products:dashboard_v1:product_sc_config', args=[999])
#
#         response = self.client.get(url)
#         self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
#         self.assertEqual(response.json()["message"], _("product service center config not found"))
#
#     def test_get_single_product_sc_config(self):
#         self.client.force_authenticate(user=self.staff_user)
#         url = reverse('products:dashboard_v1:product_sc_config', args=[self.pscc.id])
#         response = self.client.get(url)
#
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         expected_data = CreateProductServiceCenterConfigSerializer(self.pscc).data
#         self.assertEqual(response.json()["data"], expected_data)
#
#     def test_unauthenticated_user_access(self):
#         response = self.client.get(self.list_url)
#
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_no_perm_user_list(self):
#         self.client.force_authenticate(user=self.user)
#         response = self.client.get(self.list_url)
#
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_get_all_product_sc_config_with_pagination(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 1, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIn('data', response.json())
#
#         self.assertEqual(len(response.json()['data']), 1)
#
#     def test_get_all_product_sc_config_with_pagination_check_next_page(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 1, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIsNotNone(response.json()["pagination"]["next"])
#         self.assertIsNotNone(response.json()["pagination"]["next_url"])
#
#     def test_get_all_product_sc_config_with_pagination_check_previous_page(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url, {'page': 2, 'limit': 1})
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertIsNotNone(response.json()["pagination"]["previous"])
#         self.assertIsNotNone(response.json()["pagination"]["previous_url"])
#
#     def test_get_all_product_sc_config_with_default_pagination(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.get(self.list_url)
#         self.assertEqual(response.status_code, status.HTTP_200_OK)
#         self.assertEqual(len(response.json()['data']), 2)
#         query_set = ProductServiceCenterConfig.objects.filter(sites__name="hig")
#         expected_data = CreateProductServiceCenterConfigSerializer(query_set, many=True).data
#         self.assertEqual(response.json()["data"], expected_data)
