# from faker import Faker
# from django.urls import reverse
# from rest_framework.test import APITestCase
# from rest_framework import status
#
# from products.models import ProductSellingConfig, ProductServiceCenterConfig
# from django.utils.translation import gettext as _
#
# from products.tests.default_test_data import create_duplicated_product_data, create_sc_test_data
#
#
# class ProductScConfigCreateApiTest(APITestCase):
#
#     def setUp(self):
#         faker = Faker()
#         self.staff_user, self.user, self.product, self.product_hig = create_duplicated_product_data()
#         self.sc = create_sc_test_data(number=3)
#         self.hig_config = ProductSellingConfig.objects.create(name=faker.name(),
#                                                               product=self.product_hig)
#         self.create_url = reverse('products:dashboard_v1:product_sc_configs')
#
#     def test_create_no_auth(self):
#         response = self.client.post(self.create_url, data={})
#         self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
#
#     def test_create_no_perm(self):
#         self.client.force_authenticate(user=self.user)
#         response = self.client.post(self.create_url, data={})
#         self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
#
#     def test_create_no_sc_no_product_sc(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.post(self.create_url, data={})
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("invalid data"))
#
#     def test_create(self):
#         self.client.force_authenticate(user=self.staff_user)
#         data = {"product_selling_configs": [self.hig_config.pk],
#                 "service_centers": [self.sc[0].pk]}
#         response = self.client.post(self.create_url,
#                                     data=data)
#         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#         last = ProductServiceCenterConfig.objects.last()
#         self.assertEqual(last.stock, 0)
#         self.assertEqual(last.selectable_days, 4)
#         self.assertEqual(last.price, None)
#         self.assertEqual(last.auto_balance_percentage, None)
#         self.assertEqual(last.sites.first().name, "hig")
#         self.assertEqual(last.product_selling_config.pk, data["product_selling_configs"][0])
#         self.assertEqual(last.service_center.pk, data["service_centers"][0])
#
#     def test_create_wrong_format_auto_balance_percentage(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.post(self.create_url,
#                                     data={"product_selling_configs": [self.hig_config.pk],
#                                           "service_centers": [self.sc[0].pk], "auto_balance_percentage": -3})
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("invalid data"))
#
#     def test_create_wrong_format_price(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.post(self.create_url,
#                                     data={"product_selling_configs": [self.hig_config.pk],
#                                           "service_centers": [self.sc[0].pk], "price": -3})
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("invalid data"))
#
#     def test_create_wrong_format_selectable_days(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.post(self.create_url,
#                                     data={"product_selling_configs": [self.hig_config.pk],
#                                           "service_centers": [self.sc[0].pk], "selectable_days": -3})
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("invalid data"))
#
#     def test_create_wrong_format_stock(self):
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.post(self.create_url,
#                                     data={"product_selling_configs": [self.hig_config.pk],
#                                           "service_centers": [self.sc[0].pk], "stock": -3})
#         self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
#         self.assertEqual(response.json()["message"], _("invalid data"))
#
#     def test_create_all_fields(self):
#         data = {"product_selling_configs": [self.hig_config.pk],
#                 "service_centers": [self.sc[0].pk],
#                 "auto_balance_percentage": 3,
#                 "price": 3,
#                 "stock": 3,
#                 "selectable_days": 3,
#
#                 }
#         self.client.force_authenticate(user=self.staff_user)
#         response = self.client.post(self.create_url,
#                                     data=data)
#         self.assertEqual(response.status_code, status.HTTP_201_CREATED)
#         last = ProductServiceCenterConfig.objects.last()
#         self.assertEqual(last.stock, data["stock"])
#         self.assertEqual(last.selectable_days, data["selectable_days"])
#         self.assertEqual(last.price, data["price"])
#         self.assertEqual(last.auto_balance_percentage, data["auto_balance_percentage"])
#         self.assertEqual(last.sites.first().name, "hig")
#         self.assertEqual(last.product_selling_config.pk, data["product_selling_configs"][0])
#         self.assertEqual(last.service_center.pk, data["service_centers"][0])
