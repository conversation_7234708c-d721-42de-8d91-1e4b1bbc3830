from core import serializers

from service_center.models import ServiceCenter
from .product_selling_config import SingleProductSellingConfigSerializer
from ..models import ProductServiceCenterConfig


class ProductServiceCenterConfigSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        self.dates = kwargs.pop("dates", [])
        self.stock_type = kwargs.pop("stock_type", None)
        super().__init__(*args, **kwargs)

    user = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    dates = serializers.SerializerMethodField()
    stock_type = serializers.SerializerMethodField()

    class Meta:
        model = ServiceCenter
        fields = ("id", "name", "phone_number", "address", "user", "dates", "stock_type")

    def get_user(self, obj):
        return {"full_name": obj.user.get_full_name()}

    def get_address(self, obj):
        return {
            "state": obj.default_location_address.region.city.state.name,
            "city": obj.default_location_address.region.city.name,
            "region": obj.default_location_address.region.name,
            "address": obj.default_location_address.description,
            "latitude": obj.default_location_address.latitude,
            "longitude": obj.default_location_address.longitude,
        }

    def get_dates(self, obj):
        return self.dates

    def get_stock_type(self, obj):
        return self.stock_type


class CreateProductServiceCenterConfigSerializer(serializers.ModelSerializer):
    service_center = serializers.SerializerMethodField(read_only=True)
    product_selling_config = SingleProductSellingConfigSerializer(read_only=True)

    class Meta:
        model = ProductServiceCenterConfig
        exclude = ["sites"]

    def create(self, validated_data):
        return ProductServiceCenterConfig(**validated_data)

    @staticmethod
    def get_service_center(obj):
        return {
            "id": obj.service_center.id,
            "name": obj.service_center.name,
        }
