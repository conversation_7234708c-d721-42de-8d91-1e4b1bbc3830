from core import serializers
from products.models import ProductSellingConfig, SellingConfig
from quick_book.serializers.product import ProductSerializer


class SellingConfigSerializer(serializers.ModelSerializer):
    products = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = SellingConfig
        fields = '__all__'

    @staticmethod
    def get_products(obj):
        products_selling_configs = ProductSellingConfig.objects.filter(product__site__name="hig", is_active=True,
                                                                       selling_config=obj).select_related("product")
        products = [products_selling_config.product for products_selling_config in products_selling_configs]

        return ProductSerializer(products, fields=["id", "name"], many=True).data


class SingleProductSellingConfigSerializer(serializers.ModelSerializer):
    selling_config = SellingConfigSerializer(fields=[
        "name",
        "stock_type",
        "type",
        "charge_price",
        "shift_delay",
        "service_amount",
        "happy_hour_service_amount",
        "delivery_amount",
        "id"

    ], read_only=True)
    product = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = ProductSellingConfig
        fields = '__all__'

    @staticmethod
    def get_product(obj):
        return obj.product.name


class ProductSellingConfigSerializer(serializers.ModelSerializer):
    selling_config = SellingConfigSerializer(read_only=True)
    product = ProductSerializer(read_only=True)

    class Meta:
        model = ProductSellingConfig
        fields = ["selling_config", "product"]
