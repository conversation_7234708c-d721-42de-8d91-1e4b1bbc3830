from products.models import ProductServiceCenterConfig


def calculate_charge_price(product, service_center):
    """
    get charge price of a product for a service center
    first try to get from Product Service center config if its not
    get from selling config
    """
    charge_price = 0
    pscc = ProductServiceCenterConfig.objects.filter(product_selling_config__product=product, service_center=service_center,
                                                     is_active=True).first()
    if pscc:
        if pscc.charge_price:
            charge_price = pscc.charge_price

    if not charge_price:
        charge_price = pscc.product_selling_config.selling_config.charge_price

    return charge_price


