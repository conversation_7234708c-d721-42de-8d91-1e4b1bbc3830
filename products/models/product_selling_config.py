from core import models


class ProductSellingConfig(models.AbstractBaseModel):
    selling_config = models.ForeignKey(
        "products.SellingConfig",
        on_delete=models.PROTECT,
        related_name="product_selling_configs",
        null=True
    )
    product = models.ForeignKey(
        "quick_book.Product",
        on_delete=models.PROTECT,
        related_name="product_selling_configs"
    )

    def __str__(self):
        return "{}".format(self.id)
