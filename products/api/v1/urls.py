from django.contrib import admin
from django.urls import include, path

from .product_service_types import ProductServiceTypesAPI
from .check_product_stock import CheckProductStock
from .import_sc_product_transaction import import_sc_product_transaction

app_name = "products"

urlpatterns = [
    path("product-service-types/", ProductServiceTypesAPI.as_view(), name="product_service_types"),
    path("check-product-stock/", CheckProductStock.as_view(), name="check_product_stock"),
    path("import-sc-product/", import_sc_product_transaction, name="import_sc_product_transaction"),
]
