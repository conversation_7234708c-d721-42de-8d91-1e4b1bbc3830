import logging
from rest_framework.views import APIView
from core.http import JsonResponse
from core.models import Site
from products.utils.stock import check_sc_stock, extract_bundle_products
from quick_book.models import Product
from locations.models import State
from products.models import ProductServiceCenterConfig
from constants import ServiceDeliveryType
from service_center.models import ServiceCenter
from translation.utils.translate import translation_checker as tc

logger = logging.getLogger("products")


class ProductServiceTypesAPI(APIView):
    permission_classes = ()
    authentication_classes = ()

    def post(self, request):
        state_id = request.data["state_id"]
        products_data = request.data["products"]
        site_name = request.data["site"]

        # get all variants of saleor product and get the seller id of each and variant's metadata
        # and pass the data based on each seller

        self.exception_data = {"status": 400, "message": "invalid site."}
        _site = Site.objects.get(name__icontains=site_name)

        self.exception_data = {"status": 400, "message": "invalid state."}
        state = State.objects.get(id=int(state_id))

        data = {}
        errors = {}

        for product in products_data:
            self.exception_data = {"status": 400, "message": "invalid product."}
            product_id, quantity, metadata, bundle = product.get("product_id"), product.get("quantity"), product.get(
                "metadata"), \
                product.get("bundle")
            try:
                products = extract_bundle_products(_site=_site, saleor_product_id=product_id, quantity=quantity)
            except Exception as e:
                logger.error(f"Check Stock product | product id : {product_id} | error is |{str(e)}")
                return JsonResponse(
                    message="Not enough stock available for this product",
                    status=400
                )

            service_types = set(ServiceDeliveryType.values)
            for _product in products:
                psc_query = ProductServiceCenterConfig.objects.product_sc_configs(product=_product["product"],
                                                                                  state=state, site=_site)
                try:
                    check_sc_stock(
                        product=_product["product"],
                        service_center_ids=list(psc_query.values_list("service_center_id", flat=True)),
                        quantity=_product["quantity"],
                    )
                except Exception:
                    errors[product_id] = tc("Stock is insufficient. Please remove this product",
                                            request.lang)

                service_types = set(
                    types[0] for types in
                    psc_query
                    .values_list(
                        "product_selling_config__selling_config__type",
                        "service_center__service_types__service_type__type"
                    ).distinct() if len(set(types)) == 1
                ) & service_types

            _service_types = []
            for service_type in service_types:
                _service_types.append(
                    {
                        ServiceDeliveryType(service_type).value:
                            tc(ServiceDeliveryType(service_type).label, request.lang)
                    }
                )
            if not _service_types and product_id not in errors:
                errors[product_id] = tc("No service type available. Please remove this product",
                                        request.lang)
            else:
                data[product_id] = _service_types

        if errors:
            return JsonResponse(
                data=errors,
                status=400
            )
        return JsonResponse(
            data=data
        )
