import json
import os
import requests
from django.http import HttpResponse
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.db import transaction
from django.contrib import messages
from django import forms

from core.http import JsonResponse
from products.utils.import_sc_product_transaction import add_product_sc_trans_data


class DataUploadForm(forms.Form):
    file = forms.FileField()


@login_required(login_url='/admin/login/')
def import_sc_product_transaction(request):
    if request.method == 'POST':
        with transaction.atomic():
            form = DataUploadForm(request.POST, request.FILES)
            if form.is_valid():
                file = form.cleaned_data['file']
                errors = add_product_sc_trans_data(file)
                if errors :
                    return JsonResponse(errors, safe=False, status=400)

                messages.success(request, "File uploaded and processed successfully!")
                return redirect('products:v1:import_sc_product_transaction')
    else:
        form = DataUploadForm()

    return render(request, 'products/import_sc_transaction.html', {'form': form})
