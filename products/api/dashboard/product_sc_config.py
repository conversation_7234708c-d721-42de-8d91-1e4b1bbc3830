from copy import deepcopy
import logging

from django.db.models import Q
from django.utils.translation import gettext
from django.contrib.sites.models import Site
from django.db import transaction
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import Coo<PERSON>ie<PERSON>uthentication
from core.http import paginate, JsonResponse
from core.authentication import CustomAuthentication
from products.models import ProductServiceCenterConfig, ProductSellingConfig, SellingConfig
from products.serializers import ProductServiceCenterConfigSerializer
from products.serializers.product_service_center_config import CreateProductServiceCenterConfigSerializer
from service_center.models import ServiceCenter

logger = logging.getLogger("dashboard")


class ProductScConfigApi(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request):
        if request.query_params.get("pscc"):
            self.exception_data = {"message": gettext("product service center config not found"), "status": 404}
            psc_obj = ProductServiceCenterConfig.objects.get(id=request.query_params.get("pscc"), sites__name="hig")
            return JsonResponse(data=CreateProductServiceCenterConfigSerializer(psc_obj).data, status=200)
        self.exception_data = {"message": gettext("Error occurred while showing product service center config")}
        page, limit = request.query_params.get('page', 1), request.query_params.get('limit', 20)
        q = request.query_params.get("q", None)
        query_set = ProductServiceCenterConfig.objects.filter(sites__name="hig").order_by("-updated_at")
        if q:
            query_set = query_set.filter(Q(product_selling_config__product__name__icontains=q) |
                                         Q(product_selling_config__product__sku__icontains=q) |
                                         Q(service_center__name__icontains=q) |
                                         Q(service_center__id__icontains=q)
                                         )
        return JsonResponse(
            **paginate(data=query_set, serializer=CreateProductServiceCenterConfigSerializer,
                       serializer_kwargs={"fields": ["service_center", "product_selling_config", "id", "is_active"],
                                          },
                       page=page, limit=limit))

    @transaction.atomic()
    def post(self, request):
        self.exception_data = {"status": 400, "message": "invalid data"}
        hig = Site.objects.get(name="hig").pk
        data = deepcopy(request.data)
        logger.info(f"CREATE PRODUCT SC CONFIG DATA :{data}")
        product_selling_configs = data.pop("product_selling_configs")
        service_centers = data.pop("service_centers")

        pscc = []
        for product in product_selling_configs:
            product_obj = ProductSellingConfig.objects.get(id=product)
            for service_center in service_centers:
                service_center_obj = ServiceCenter.objects.get(id=service_center)
                data["product_selling_config"] = product
                data["service_center"] = service_center
                serializer = CreateProductServiceCenterConfigSerializer(data=data)
                if not serializer.is_valid():
                    self.exception_data = {"status": 400, "message": "invalid data"}
                    logger.error(
                        f"CREATE_PRODUCT_SC_CONFIG|invalid data for product : {product} | error : {serializer.errors}")
                    return JsonResponse(data=serializer.errors, message="invalid data", status=400)
                ProductServiceCenterConfig.objects.filter(product_selling_config__product=product_obj.product,
                                                          is_active=True, service_center=service_center_obj).update(
                    is_active=False)
                pscc.append(serializer.save(product_selling_config=product_obj, service_center=service_center_obj))

        if not pscc:
            return JsonResponse(message=gettext("invalid data"), status=400)

        self.exception_data = {"status": 400, "message": "failed to save"}
        pscc_objects = ProductServiceCenterConfig.objects.bulk_create(pscc)
        for pscc_object in pscc_objects:
            pscc_object.sites.set([hig])
        return JsonResponse(status=201)

    @transaction.atomic()
    def patch(self, request):
        self.exception_data = {"message": gettext("product service center config not send"), "status": 400}
        pscc = request.query_params["pscc"]
        self.exception_data = {"message": gettext("product service center config not found"), "status": 400}
        psc_obj = ProductServiceCenterConfig.objects.get(id=pscc, sites__name="hig")
        data = request.data
        selling_config = data.pop("selling_config", None)
        if selling_config:
            selling_config.pop("name", None)
            selling_config.pop("product", None)
            self.exception_data = {"message": gettext("selling config did not save"), "status": 400}
            selling_config_obj = SellingConfig.objects.get_or_create(**selling_config)[0]
            psc_obj.product_selling_config.selling_config = selling_config_obj
            psc_obj.product_selling_config.save()
        psc_obj.balance_percentage = data.get("balance_percentage", psc_obj.balance_percentage)
        psc_obj.charge_price = data.get("charge_price", psc_obj.charge_price)
        psc_obj.selectable_days = data.get("selectable_days", psc_obj.selectable_days)
        psc_obj.is_active = data.get("is_active", psc_obj.is_active)
        psc_obj.save()

        return JsonResponse(status=200)
