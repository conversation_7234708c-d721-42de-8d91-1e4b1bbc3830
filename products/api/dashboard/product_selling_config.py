from copy import deepcopy
import logging

from django.db import transaction
from django.utils.translation import gettext
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff
from constants import ServiceDeliveryType
from core.authentication.cookie_auth import Coo<PERSON>ieAuthentication
from core.http import JsonResponse, paginate
from products.models import ProductSellingConfig, SellingConfig
from products.serializers.product_selling_config import ProductSellingConfigSerializer, SellingConfigSerializer
from quick_book.models import Category, Product

logger = logging.getLogger("dashboard")


class ProductSellingConfigApi(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request):
        if request.query_params.get("psc"):
            self.exception_data = {"message": gettext("product selling config not found"), "status": 404}
            psc_obj = SellingConfig.objects.get(id=request.query_params.get("psc"), is_active=True)
            return JsonResponse(data=SellingConfigSerializer(psc_obj).data, status=200)

        self.exception_data = {"message": gettext("Error occurred while showing product selling config")}
        page, limit = request.query_params.get('page', 1), request.query_params.get("limit", 20)
        query_set = SellingConfig.objects.filter(is_active=True)
        q = request.query_params.get("q", None)
        if q:
            query_set = query_set.filter(name__icontains=q)
        return JsonResponse(
            **paginate(data=query_set.order_by("-id"), serializer=SellingConfigSerializer,
                       page=page, limit=limit))

    @transaction.atomic()
    def post(self, request):
        data = deepcopy(request.data)
        logger.info(f"CREATE PRODUCT SELLING CONFIG DATA :{data}")
        products_id = data.pop("products", [])
        product_selling_configs = []
        if data["type"] == ServiceDeliveryType.SERVICE:
            data["service_amount"] = data.pop("amount")
        else:
            data["delivery_amount"] = data.pop("amount")

        selling_config, created = SellingConfig.objects.get_or_create(
            **data
        )

        for product in products_id:
            self.exception_data = {"status": 400, "message": "error on saving product selling config"}
            product_selling_configs.append(
                ProductSellingConfig.objects.get_or_create(product_id=product, selling_config=selling_config)[0].pk)

        return JsonResponse(data=product_selling_configs, status=201)

    @transaction.atomic()
    def delete(self, request, sc_id):
        self.exception_data = {"message": gettext("product selling config not found")}
        sc_obj = SellingConfig.objects.get(id=sc_id, is_active=True)
        sc_obj.is_active = False
        sc_obj.save(update_fields=["is_active"])
        for psc in sc_obj.product_selling_configs.all():
            psc.product_service_center_configs.all().update(is_active=False)
            psc.is_active = False
            psc.save(update_fields=["is_active"])
        return JsonResponse(status=200)
