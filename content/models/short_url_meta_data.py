import uuid
from math import ceil

from core import models


class ShortUrlMetaData(models.AbstractBaseModel):
    short_url = models.ForeignKey('content.ShortUrl', on_delete=models.CASCADE)
    browser = models.CharField(max_length=255, null=True, blank=True)
    browser_version = models.CharField(max_length=255, null=True, blank=True)
    os = models.CharField(max_length=255, null=True, blank=True)
    os_version = models.CharField(max_length=255, null=True, blank=True)
    ip = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        db_table = "matrix_content_short_url_meta_data"

    def __str__(self):
        return self.short_url.code
