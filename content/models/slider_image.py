from constants import BannerLocation, BannerType
from core import models
from translation.models import Translation


class SliderImage(models.AbstractBaseModel):
    image = models.ImageField(upload_to="slider_images/")
    image_2 = models.ImageField(upload_to="slider_images/", null=True, blank=True)
    type = models.CharField(max_length=30, choices=BannerType.choices, default=BannerType.BANNER)
    location = models.CharField(max_length=30, choices=BannerLocation.choices, default=BannerLocation.MAIN_PAGE)
    color = models.CharField(max_length=30, null=True, blank=True)
    desktop = models.BooleanField(default=True)
    mobile = models.BooleanField(default=True)
    header = models.BooleanField(default=True)
    sites = models.ManyToManyField(models.Site, related_name="slider_items")
    cities = models.ManyToManyField("locations.City", related_name="slider_items")
    url = models.TextField(null=True, blank=True, max_length=1000)
    url_2 = models.TextField(null=True, blank=True, max_length=1000)

    def __str__(self):
        return "[{}] slider item".format(self.id)


class SliderImageTranslation(Translation):
    slider_image = models.ForeignKey(SliderImage, on_delete=models.CASCADE, related_name="translations")
    image = models.ImageField(upload_to="slider_images/", null=True, blank=True)
    image_2 = models.ImageField(upload_to="slider_images/", null=True, blank=True)

    def __str__(self):
        return "{}".format(self.slider_image_id)

    class Meta:
        unique_together = (("lang_code", "slider_image"),)

    @property
    def get_translation_fields(self):
        resp = {}
        if self.image:
            resp["image"] = self.image.url
        elif self.image_2:
            resp["image_2"] = self.image_2.url

        return resp