from django.contrib.sites.models import Site
from django.db import models
from django.utils.crypto import get_random_string


def generate_token():
    return get_random_string(50)


class AppToken(models.Model):
    name = models.CharField(max_length=255)
    key = models.CharField(max_length=50, editable=False, default=generate_token, unique=True)
    enabled = models.BooleanField(default=True)
    site = models.ForeignKey(Site, on_delete=models.PROTECT, related_name='tokens')

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    class Meta:
        indexes = [
            models.Index(fields=['enabled', 'key']),
        ]
