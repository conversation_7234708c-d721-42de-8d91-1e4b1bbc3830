from constants import GiftTypes
from core.models import AbstractBaseModel, AbstractPropertyModel
from django.db import models


class Gift(AbstractBaseModel, AbstractPropertyModel):
    type = models.CharField(max_length=50, choices=GiftTypes.choices, default=GiftTypes.INTERNET)
    name = models.CharField(max_length=250)
    credit = models.PositiveIntegerField(default=0)
    price = models.PositiveIntegerField(default=0)
    operator = models.CharField(max_length=250)
    code = models.CharField(max_length=250)

    class Meta:
        db_table = "matrix_content_gift"

    def __str__(self):
        return self.name
