import json

from core.models import AbstractBaseModel, AbstractPropertyModel
from django.db import models


class UserGift(AbstractBaseModel, AbstractPropertyModel):
    user = models.ForeignKey("accounts.User", on_delete=models.PROTECT, related_name="user_gift")
    gift = models.ForeignKey("content.Gift", on_delete=models.PROTECT, related_name="user_gift")
    gift_mobile_number = models.CharField(max_length=20)
    user_credit = models.PositiveIntegerField(default=0)
    used_credit = models.PositiveIntegerField(default=0)
    reference_id = models.CharField(max_length=255, editable=False)
    is_charged = models.BooleanField(default=False)

    class Meta:
        db_table = "matrix_content_user_gift"

    def __str__(self):
        return self.user.username

    @property
    def status_message(self):
        try:
            return json.loads(self.properties).get("ResponseMessage", "check")
        except Exception:
            return "check"
