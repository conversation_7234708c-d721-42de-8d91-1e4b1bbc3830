from core import models
from translation.models import Translation
from translation.utils import get_object_translation


class ConfigQuerySet(models.QuerySet):
    def filter_site_configs(self, site):
        if isinstance(site, int):
            _site = {"sites__id": site}
        else:
            _site = {"sites": site}
        return self.filter(is_active=True, **_site).order_by("priority", "id")

    def get_site_configs(self, site, lang):
        configs = self.filter_site_configs(site)
        data = {}
        for config in configs:
            data.update(config.json(lang))
        return data

    def get_site_config(self, site, key, lang):
        config = self.filter_site_configs(site).filter(key=key).last()
        return config.json(lang)


class Config(models.AbstractBaseModel):
    key = models.SlugField(max_length=250, unique=True)
    value = models.JSONField(null=True, blank=True)
    sites = models.ManyToManyField(models.Site, related_name="configs")
    file = models.FileField(upload_to="configs/", null=True, blank=True)

    objects = ConfigQuerySet.as_manager()

    class Meta:
        db_table = "matrix_content_config"

    def __str__(self):
        return self.key

    def json(self, lang):
        data = get_object_translation(self, lang)
        if data:
            return data
        return {self.key: self.file.url if self.file else self.value}


class ConfigTranslation(Translation):
    config = models.ForeignKey(Config, on_delete=models.CASCADE, related_name="translations")
    key = models.SlugField(max_length=250, null=True, blank=True)
    value = models.JSONField(null=True, blank=True)
    file = models.FileField(upload_to="configs/", null=True, blank=True)

    class Meta:
        db_table = "matrix_content_config_translation"
        unique_together = (("lang_code", "config"),)

    def __str__(self):
        return "{}|{}".format(self.lang_code, self.config.key)

    @property
    def get_translation_fields(self):
        return {self.key: self.file.url if self.file else self.value}
