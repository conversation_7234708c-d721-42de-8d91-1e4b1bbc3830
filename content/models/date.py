import jdatetime
from core.models import AbstractBaseModel
from django.db import models

from translation.models import Translation


class Date(AbstractBaseModel):
    name = models.CharField(max_length=250, blank=True, null=True)
    date = models.DateField(unique=True)
    is_holiday = models.BooleanField(default=False)

    class Meta:
        db_table = "matrix_content_date"

    def __str__(self):
        return "{}{}{}".format(
            self.get_jalali_display(),
            " | {}".format("تعطیل") if self.is_holiday else "",
            " | {}".format(self.name) if self.name else "",
        )

    def get_jalali(self):
        return jdatetime.datetime.fromgregorian(date=self.date).date()

    def get_jalali_display(self):
        return self.get_jalali().aslocale("fa_IR").strftime("%A%-d%B%y")


class DateTranslation(Translation):
    date = models.ForeignKey(Date, on_delete=models.CASCADE, related_name="translations")
    name = models.Char<PERSON>ield(max_length=250, blank=True, null=True)

    class Meta:
        db_table = "matrix_content_date_translation"
        unique_together = (("lang_code", "date"),)

    def __str__(self):
        return "{}|{}".format(self.name, self.date_id)

    @property
    def get_translation_fields(self):
        return {
            "name": self.name
        }
