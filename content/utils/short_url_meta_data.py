from user_agents import parse


def get_user_agent_info(request):
    user_agent_string = request.META.get('HTTP_USER_AGENT', '')
    user_agent = parse(user_agent_string)

    browser = user_agent.browser.family  # Browser name
    browser_version = user_agent.browser.version_string  # Browser version
    os = user_agent.os.family  # Operating system name
    os_version = user_agent.os.version_string  # OS version

    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')

    return {
        "browser": browser,
        "browser_version": browser_version,
        "os": os,
        "os_version": os_version,
        "ip": ip
    }
