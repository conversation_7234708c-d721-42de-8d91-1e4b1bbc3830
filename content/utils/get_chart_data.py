def month_chart_data(queryset, current):
    months = ["فروردین", "اردیبهشت", "خرد<PERSON>", "تیر", "مرد<PERSON>", "شهریور", "مهر", "آ<PERSON><PERSON>", "آذر", "دی", "بهمن", "اسفند"]
    month_set = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12}
    list_queryset = list(queryset)

    for month_num in month_set - set(queryset.values_list("month", flat=True)):
        list_queryset.append({"month": month_num, "total": 0})

    list_queryset = sorted(list_queryset, key=lambda i: i["month"])
    categories = []
    series1 = []
    series2 = []
    index_month = current - 2
    for i in range(len(list_queryset) // 2):
        categories.append("{} / {}".format(months[index_month], months[index_month - 6]))
        series1.append(list_queryset[index_month]["total"] if list_queryset[index_month]["total"] else 0)
        series2.append(list_queryset[index_month + 6]["total"] if list_queryset[index_month + 6]["total"] else 0)
        index_month -= 1
    categories.reverse()
    series2.reverse()
    series1.reverse()
    return {
        "categories": categories,
        "series": [{"name": "شش ماه فعلی", "data": series1}, {"name": "شش ماه قبلی", "data": series2}],
    }


def day_chart_data(queryset, current):
    days = ["شنبه", "یکشنبه", "دوشنبه", "سه شنبه", "چهارشنبه", "پنجشنبه", "جمعه"]
    days_set = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13}
    list_queryset = list(queryset)
    for day_num in days_set - set(queryset.values_list("day", flat=True)):
        list_queryset.append({"day": day_num, "total": 0})
    list_queryset = sorted(list_queryset, key=lambda i: i["day"])
    categories = []
    series1 = []
    series2 = []
    for item in list(zip(list_queryset[:7], list_queryset[7:])):
        categories.append(days[(current - 1) % 7])
        current -= 1
        series1.append(item[0]["total"] if item[0]["total"] else 0)
        series2.append(item[1]["total"] if item[1]["total"] else 0)
    categories.reverse()
    return {
        "categories": categories,
        "series": [{"name": "این هفته", "data": series2}, {"name": "هفته قبل", "data": series1}],
    }


def chart_data(queryset, current, period):
    if period == "day":
        return day_chart_data(queryset, current)
    return month_chart_data(queryset, current)
