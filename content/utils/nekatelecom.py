import json
import logging

import requests
from django.conf import settings

logger = logging.getLogger("content")


class Nekatelecom:
    voice_url = "https://api.nekatel.com/Api/TopupService/{}‬‬"
    internet_url = "https://api.nekatel.com/Api/DataPlanService/{}"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Basic {}".format(settings.NEKATELECOM_TOKEN),
        "cache-control": "no-cache",
    }

    @staticmethod
    def reserve_voice(reserve_number, amount, mobile_number, operator, charge_type=0, bank_id=1, device_type=5):
        res = requests.post(
            url=Nekatelecom.voice_url.format("Reserve"),
            headers=Nekatelecom.headers,
            verify=False,
            data=json.dumps(
                {
                    "ReserveNumber": reserve_number,
                    "Amount": int(amount),
                    "CellNumbers": mobile_number,
                    "ChargeType": charge_type,
                    "BankId": bank_id,
                    "DeviceType": device_type,
                    "operator": operator,
                    "‫‪Merchant‬‬": "",
                }
            ),
        )
        if res.status_code == 200 and res.json().get("ResponseCode") == 0:
            return True, res.json() if res else {}
        else:
            logger.error("message={}".format(res.json()))
            logger.error("data={}".format(
                        json.dumps(
                            {
                                "ReserveNumber": reserve_number,
                                "Amount": int(amount),
                                "CellNumbers": mobile_number,
                                "ChargeType": charge_type,
                                "BankId": bank_id,
                                "DeviceType": device_type,
                                "operator": operator,
                                "‫‪Merchant‬‬": "",
                            }
                            )
                        )
                    )
            return False, res.json() if res else {}

    @staticmethod
    def approve_voice(reserve_number, reference_number):
        res = requests.post(
            url=Nekatelecom.voice_url.format("‫‪Approve‬‬"),
            headers=Nekatelecom.headers,
            verify=False,
            data=json.dumps({"ReserveNumber": reserve_number, "ReferenceNumber": reference_number}),
        )
        if res.status_code == 200 and res.json().get("ResponseCode") == 0:
            return True, res.json() if res else {}
        else:
            logger.error("message={}".format(res.json()))
            logger.error(
                "data={}".format(
                    json.dumps({"ReserveNumber": reserve_number, "ReferenceNumber": reference_number})
                )
            )
            return False, res.json() if res else {}

    @staticmethod
    def reserve_internet(reserve_number, mobile_number, operator, charge_type, bank_id=1, device_type=5):
        res = requests.post(
            url=Nekatelecom.internet_url.format("Reserve"),
            headers=Nekatelecom.headers,
            verify=False,
            data=json.dumps(
                {
                    "ReserveNumber": reserve_number,
                    "CellNumbers": mobile_number,
                    "ChargeType": charge_type,
                    "BankId": bank_id,
                    "DeviceType": device_type,
                    "operator": operator,
                    "‫‪Merchant‬‬": "",
                }
            ),
        )
        if res.status_code == 200 and res.json().get("ResponseCode") == 0:
            return True, res.json() if res else {}
        else:
            logger.error("message={}".format(res.json()))
            logger.error(
                "data={}".format(
                    json.dumps(
                        {
                            "ReserveNumber": reserve_number,
                            "CellNumbers": mobile_number,
                            "ChargeType": charge_type,
                            "BankId": bank_id,
                            "DeviceType": device_type,
                            "operator": operator,
                            "‫‪Merchant‬‬": "",
                        }
                    )
                )
            )
            return False, res.json() if res else {}

    @staticmethod
    def approve_internet(reserve_number, reference_number):
        res = requests.post(
            url=Nekatelecom.internet_url.format("‫‪Approve‬‬"),
            headers=Nekatelecom.headers,
            verify=False,
            data=json.dumps({"ReserveNumber": reserve_number, "ReferenceNumber": reference_number}),
        )
        if res.status_code == 200 and res.json().get("ResponseCode") == 0:
            return True, res.json() if res else {}
        else:
            logger.error("message={}".format(res.json()))
            logger.error("data={}".format(
                json.dumps({"ReserveNumber": reserve_number, "ReferenceNumber": reference_number})
                )
            )
            return False, res.json() if res else {}
