# Generated by Django 3.2.10 on 2024-11-30 06:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0005_auto_20241102_1433'),
    ]

    operations = [
        migrations.CreateModel(
            name='ConfigTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lang_code', models.CharField(max_length=35)),
                ('key', models.SlugField(blank=True, max_length=250, null=True)),
                ('value', models.JSONField(blank=True, null=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='configs/')),
                ('config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='content.config')),
            ],
            options={
                'db_table': 'matrix_content_config_translation',
                'unique_together': {('lang_code', 'config')},
            },
        ),
    ]
