# Generated by Django 3.2.10 on 2024-11-02 14:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0004_alter_shorturl_code'),
    ]

    operations = [
        migrations.AddField(
            model_name='shorturl',
            name='click_count',
            field=models.IntegerField(default=0),
        ),
        migrations.CreateModel(
            name='ShortUrlMetaData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('browser', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('browser_version', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('os', models.CharField(blank=True, max_length=255, null=True)),
                ('os_version', models.CharField(blank=True, max_length=255, null=True)),
                ('ip', models.CharField(blank=True, max_length=255, null=True)),
                ('short_url', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='content.shorturl')),
            ],
            options={
                'db_table': 'matrix_content_short_url_meta_data',
            },
        ),
    ]
