# Generated by Django 3.2.10 on 2023-12-04 08:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sites', '0002_alter_domain_unique'),
        ('locations', '0001_initial'),
        ('content', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SliderImage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('image', models.ImageField(upload_to='slider_images/')),
                ('image_2', models.ImageField(blank=True, null=True, upload_to='slider_images/')),
                ('type', models.Char<PERSON>ield(choices=[('banner', 'banner'), ('slider', 'slider'), ('header', 'header')], default='banner', max_length=30)),
                ('location', models.CharField(choices=[('main_page', 'main_page'), ('category_page', 'category_page')], default='main_page', max_length=30)),
                ('color', models.CharField(blank=True, max_length=30, null=True)),
                ('desktop', models.BooleanField(default=True)),
                ('mobile', models.BooleanField(default=True)),
                ('header', models.BooleanField(default=True)),
                ('url', models.TextField(blank=True, max_length=1000, null=True)),
                ('url_2', models.TextField(blank=True, max_length=1000, null=True)),
                ('cities', models.ManyToManyField(related_name='slider_items', to='locations.City')),
                ('sites', models.ManyToManyField(related_name='slider_items', to='sites.Site')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
