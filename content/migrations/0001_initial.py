# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('sites', '0002_alter_domain_unique'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Date',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=250, null=True)),
                ('date', models.DateField(unique=True)),
                ('is_holiday', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'matrix_content_date',
            },
        ),
        migrations.CreateModel(
            name='Gift',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('properties', models.TextField(blank=True, default='[]')),
                ('type', models.CharField(choices=[('internet', 'Internet'), ('voice', 'Voice'), ('cash', 'Cash')], default='internet', max_length=50)),
                ('name', models.CharField(max_length=250)),
                ('credit', models.PositiveIntegerField(default=0)),
                ('price', models.PositiveIntegerField(default=0)),
                ('operator', models.CharField(max_length=250)),
                ('code', models.CharField(max_length=250)),
            ],
            options={
                'db_table': 'matrix_content_gift',
            },
        ),
        migrations.CreateModel(
            name='ShortUrl',
            fields=[
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.CharField(max_length=15, primary_key=True, serialize=False)),
                ('url', models.URLField(max_length=250)),
            ],
            options={
                'db_table': 'matrix_content_short_url',
            },
        ),
        migrations.CreateModel(
            name='UserGift',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('properties', models.TextField(blank=True, default='[]')),
                ('gift_mobile_number', models.CharField(max_length=20)),
                ('user_credit', models.PositiveIntegerField(default=0)),
                ('used_credit', models.PositiveIntegerField(default=0)),
                ('reference_id', models.CharField(editable=False, max_length=255)),
                ('is_charged', models.BooleanField(default=False)),
                ('gift', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='user_gift', to='content.gift')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='user_gift', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'matrix_content_user_gift',
            },
        ),
        migrations.CreateModel(
            name='FAQ',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(max_length=250)),
                ('keyword', models.CharField(blank=True, max_length=250, null=True)),
                ('description', models.TextField()),
                ('sites', models.ManyToManyField(blank=True, related_name='faqs', to='sites.Site')),
            ],
            options={
                'db_table': 'matrix_content_faq',
            },
        ),
        migrations.CreateModel(
            name='Config',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('key', models.SlugField(max_length=250, unique=True)),
                ('value', models.JSONField(blank=True, null=True)),
                ('file', models.FileField(blank=True, null=True, upload_to='configs/')),
                ('sites', models.ManyToManyField(related_name='configs', to='sites.Site')),
            ],
            options={
                'db_table': 'matrix_content_config',
            },
        ),
    ]
