# Generated by Django 3.2.10 on 2024-07-17 08:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('content', '0002_sliderimage'),
    ]

    operations = [
        migrations.CreateModel(
            name='DateTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lang_code', models.<PERSON>r<PERSON>ield(max_length=35)),
                ('name', models.Char<PERSON>ield(blank=True, max_length=250, null=True)),
                ('date', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='content.date')),
            ],
            options={
                'db_table': 'matrix_content_date_translation',
                'unique_together': {('lang_code', 'date')},
            },
        ),
    ]
