# Generated by Django 3.2.10 on 2024-12-14 08:29

import content.models.app_token
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('sites', '0002_alter_domain_unique'),
        ('content', '0006_configtranslation'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppToken',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('key', models.CharField(default=content.models.app_token.generate_token, editable=False, max_length=50, unique=True)),
                ('enabled', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('site', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='tokens', to='sites.site')),
            ],
        ),
        migrations.AddIndex(
            model_name='apptoken',
            index=models.Index(fields=['enabled', 'key'], name='content_app_enabled_9a1498_idx'),
        ),
    ]
