import logging

from constants import BannerType
from content.models import SliderImage
from content.serializers import SliderImageSerializer
from core.authentication import ExistTokenAuthentication
from core.http import JsonResponse
from core.permissions import ExistIsActiveUser
from rest_framework.views import APIView

logger = logging.getLogger("content")


class SliderAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        queryset = SliderImage.objects.filter(is_active=True, sites=request.site)
        if request.query_params.get("desktop"):
            queryset = queryset.filter(desktop=True)
        if request.query_params.get("mobile"):
            queryset = queryset.filter(mobile=True)
        if request.query_params.get("city_id"):
            queryset = queryset.filter(cities__id=request.query_params["city_id"])
        if request.query_params.get("location"):
            queryset = queryset.filter(location=request.query_params.get("location"))
        if request.query_params.get("type") == "banner":
            queryset = queryset.filter(type=BannerType.BANNER)
        if request.query_params.get("type") == "slider":
            queryset = queryset.filter(type=BannerType.SLIDER)
        data = SliderImageSerializer(
            queryset.distinct().order_by("-priority"),
            many=True,
            context={
                "site_id": request.site.id,
                "city_id": request.query_params.get("city_id"),
                "lang": request.lang
            },
        ).data

        return JsonResponse(data=data)
