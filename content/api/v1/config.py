import logging

from content.models import Config
from core.cache import cache, get_hashed_cache_key
from core.http import JsonResponse
from rest_framework.views import APIView

logger = logging.getLogger("content")


class ConfigAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request, key=None):
        cache_key = get_hashed_cache_key("SITE_CONFIG", request.site.id, f"{request.lang}-{key}")
        data = cache.get(cache_key)
        if data:
            return JsonResponse(data=data)

        if key:
            self.exception_data = {"status": 404, "message": "key not found"}
            data = Config.objects.get_site_config(request.site, key, request.lang)

        else:
            data = Config.objects.get_site_configs(request.site, request.lang)

        cache.set(cache_key, data, 600)
        return JsonResponse(data=data)
