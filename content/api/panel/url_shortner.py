from rest_framework import viewsets

from content.models import ShortUrl
from content.permissions.is_app import IsApp
from content.serializers.short_url import ShortUrlSerializer
from core.pagination.limit_offset import StandardLimitOffsetPagination


class ShortUrlViewSet(viewsets.ModelViewSet):
    queryset = ShortUrl.objects.all()
    pagination_class = StandardLimitOffsetPagination
    serializer_class = ShortUrlSerializer
    permission_classes = [IsApp]
