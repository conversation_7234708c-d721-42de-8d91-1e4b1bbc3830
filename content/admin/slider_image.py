from core import admin
from translation.utils.admin_form import AdminTranslationInline
from content.models import SliderImageTranslation


class SliderImageTranslationInline(AdminTranslationInline):
    model = SliderImageTranslation


class SliderImageAdmin(admin.AbstractBaseAdmin):
    list_display = ("id", "thumb_1", "thumb_2", "location", "type", "priority", "desktop", "mobile", "is_active")
    list_editable = ("desktop", "mobile", "is_active", "location", "type", "priority")
    filter_horizontal = ("sites", "cities",)
    inlines = [SliderImageTranslationInline]
    raw_id_fields = ()
    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("is_active", "desktop", "mobile"),
                    ("details_thumb_1", "details_thumb_2"),
                    ("image", "image_2"),
                    ("url",),
                    ("url_2",),
                    ("location", "type"),
                    ("color", "priority"),
                    "sites",
                    "cities",
                )
            },
        ),
    )
    readonly_fields = ("details_thumb_1", "details_thumb_2") + admin.AbstractBaseAdmin.readonly_fields

    def thumb_1(self, obj):
        return admin.link_image(obj.image, height=50)

    def thumb_2(self, obj):
        if obj.image_2:
            return admin.link_image(obj.image_2, height=50)
        return "-"

    def details_thumb_1(self, obj):
        return admin.link_image(obj.image, width=440, height=200)

    def details_thumb_2(self, obj):
        return admin.link_image(obj.image_2, width=440, height=200)

    details_thumb_1.short_description = "thumb 1"
    details_thumb_2.short_description = "thumb 2"
