from core import admin
from translation.utils.admin_form import AdminTranslationInline
from ..models import DateTranslation


class DateTranslationInline(AdminTranslationInline):
    model = DateTranslation




class DateAdmin(admin.AbstractBaseAdmin):
    list_display = ("__str__", "date", "_jalali", "is_holiday")
    list_editable = ()
    search_fields = ("name",)
    readonly_fields = ("_jalali",) + admin.AbstractBaseAdmin.readonly_fields
    fieldsets = ((None, {"fields": ("_jalali", "date", ("name", "is_holiday"))}),)

    _jalali = admin.get_display("get_jalali", "jalali")
    inlines = [DateTranslationInline]

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.prefetch_related("translations")
