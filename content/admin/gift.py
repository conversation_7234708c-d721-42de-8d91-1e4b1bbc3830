from content.forms import GiftForm
from core.admin import AbstractBaseAdmin
from files.admin import ImageInline


class GiftAdmin(AbstractBaseAdmin):
    list_display = ("name", "operator", "code", "type", "is_active", "priority")
    inlines = (ImageInline,)
    list_editable = ("priority", "is_active")
    form = GiftForm

    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("is_active",),
                    ("priority", "credit", "price"),
                    ("type",),
                    ("name", "code", "operator"),
                    "properties",
                )
            },
        ),
    )
