from core import admin


class FAQAdmin(admin.AbstractBaseAdmin):
    list_display = ("title", "keyword", "description", "is_active", "priority")
    list_editable = ("is_active", "priority")
    filter_horizontal = ("sites",)
    sortable_by = ("is_active", "-priority")

    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("created_at", "updated_at"),
                    ("is_active", "priority"),
                    ("title", "keyword"),
                    "description",
                    "sites",
                )
            },
        ),
    )
