from content.forms import UserGiftForm
from core.admin import AbstractBaseAdmin


class UserGiftAdmin(AbstractBaseAdmin):
    list_display = (
        "user",
        "gift",
        "is_charged",
        "status_message",
        "created_at",
    ) + AbstractBaseAdmin.default_list_display
    search_fields = ("user__username", "gift__name")
    raw_id_fields = ("user", "gift")
    readonly_fields = ("priority", "is_active", "reference_id")
    form = UserGiftForm

    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("user", "gift", "gift_mobile_number"),
                    ("user_credit", "used_credit", "reference_id"),
                    "properties",
                )
            },
        ),
    )
