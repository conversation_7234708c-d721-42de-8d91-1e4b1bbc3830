from jsoneditor.forms import J<PERSON>NEditor
from django.db import models
from content.forms import ConfigForm
from core import admin
from ..models import ConfigTranslation
from translation.utils.admin_form import AdminTranslationInline, create_translation_form


class ConfigTranslationInline(AdminTranslationInline):
    model = ConfigTranslation

    def get_formset(self, request, obj=None, **kwargs):
        if obj:
            self.form = create_translation_form(self.model, model_widgets={"value": JSONEditor(
                init_options={"mode": "code"},
            )})
        return super().get_formset(request, obj, **kwargs)



class ConfigAdmin(admin.AbstractBaseAdmin):
    list_display = ("id", "key", "value", "file", "is_active", "priority")
    list_editable = ("is_active", "priority")
    filter_horizontal = ("sites",)
    search_fields = ("key", "value")
    ordering = ("key", "-is_active", "-priority", "-id")
    form = ConfigForm
    inlines = [ConfigTranslationInline]
    fieldsets = (
        (
            None,
            {
                "fields": (
                    ("created_at", "updated_at"),
                    ("is_active", "priority"),
                    "sites",
                    "key",
                    "file",
                    "value",
                )
            },
        ),
    )
