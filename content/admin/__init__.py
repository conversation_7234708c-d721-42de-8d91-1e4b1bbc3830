from content.models import FAQ, Config, Date, Gift, ShortUrl, UserGift, Slider<PERSON>mage, ShortUrlMetaData, AppToken
from core import admin

from .app_token import AppTokenAdmin
from .config import ConfigAdmin
from .date import DateAdmin
from .faq import FAQAdmin
from .gift import GiftAdmin
from .short_url import ShortUrlAdmin, ShortMetaDataAdmin
from .slider_image import SliderImageAdmin
from .user_gift import UserGiftAdmin
from .slider_image import SliderImageAdmin

admin.site.register(AppToken, AppTokenAdmin)
admin.site.register(Gift, GiftAdmin)
admin.site.register(UserGift, UserGiftAdmin)
admin.site.register(Date, DateAdmin)
admin.site.register(ShortUrl, ShortUrlAdmin)
admin.site.register(FAQ, FAQAdmin)
admin.site.register(Config, ConfigAdmin)
admin.site.register(SliderImage, SliderImageAdmin)
admin.site.register(Short<PERSON>rlMetaData,ShortMetaDataAdmin)
