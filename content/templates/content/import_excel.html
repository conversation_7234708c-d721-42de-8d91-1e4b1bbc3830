{% extends "base_bootstrap.html" %}

{% block head_title_tag %}<title>Import Data From Excel</title>{% endblock head_title_tag %}

{% block head_tag_end %}
    <style>
        body {
            background-color: #152b39;
            color: white;
        }
        .card {
            border: none;
            border-radius: 20px;
            padding: 5px;
        }
        .content-style {
            background-color: #283b4c;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
            color: white;
        }
        .btn-upload {
            background-color: #283b4c;
            color: white;
        }
        select, option, select:active {
            background-color: #283b4c !important;
            color: white !important;
        }
        .error-item {
            background-color: red;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            margin-bottom: 7px;
            padding: 5px;
        }
        .success-item {
            background-color: darkgreen;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            margin-bottom: 7px;
            padding: 5px;
        }

        .drag-drop {
            width: 100%;
            margin: 15px 0;
            height: 150px;
            position: relative;
            overflow: hidden;
        }

        #file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
            opacity: 0;
            z-index: 2;
        }

        #label {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            color: white;
            background-color: #283b4c;
            border-radius: 5px;
            border: 2px dashed #ccc;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            cursor: pointer;
            z-index: 1;
            transition: all 0.3s ease-in-out;
        }

        #label:hover {
            background-color: #eee;
        }

        #label i {
            display: block;
            font-size: 32px;
            margin-bottom: 10px;
        }

        #label span {
            display: block;
            margin-top: 10px;
        }

        .drag-drop.active #label {
            border-color: #2196f3;
        }

        .drag-drop.active #label i {
            color: #2196f3;
        }

        .drag-drop.success #label i {
            color: #28f321;
        }

        .drag-drop.success #label {
            border-color: #28f321;
        }

        .drag-drop.error #label i {
            color: red;
        }

        .drag-drop.error #label {
            border-color: red;
        }
    </style>
{% endblock head_tag_end %}

{% block body %}
    <div class="container mt-5 font-yekan">
        <div class="row">
            <div class="col-md-6 offset-md-3">
                <div class="card content-style">
                    <div class="card-header special-color">
                        {% if models %}
                            <h3 class="card-title text-center">Import Data From Excel</h3>
                        {% else %}
                            <h3 class="card-title text-center">
                                <span class="fa fa-exclamation-triangle"></span>
                            </h3>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if not models %}
                            <h4 class="text-center">شما دسترسی کافی برای دیدن این صفحه را ندارید</h4>
                        {% else %}
                            <form method="post" enctype="multipart/form-data">
                                {% csrf_token %}
                                <div class="form-group">
                                    <label for="content-type">Model:</label>
                                    <select class="form-control content-style" id="content-type" name="content-type">
                                        {% for model in models %}
                                            <option value="{{ model.id }}" {% if content_type_id == model.id %}selected{% endif %}>
                                                {{ model.name.title }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="form-group mb-3 mt-2">
                                    <div class="drag-drop content-style">
                                        <input type="file" name="excel-file" id="file-input" multiple>
                                        <label for="file-input" id="label">
                                            <i id="input-icon" class="fas fa-cloud-upload-alt"></i>
                                            <span id="input-help-text">Drag & Drop files here or click to select</span>
                                        </label>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-center">
                                    <button type="submit" class="btn btn-primary btn-upload w-50">Submit</button>
                                </div>
                            </form>
                        {% endif %}
                    </div>
                </div>

                <div class="mt-2">
                    {% for message in messages %}
                        <div class="success-item" dir="{% if message.is_bidi %}rtl{% else %}ltr{% endif %}">
                            <span class="fa fa-check-circle"></span>
                            {{ message.text }}
                        </div>
                    {% endfor %}
                </div>
                <div class="errors mt-2">
                    {% for error in errors %}
                        <div class="error-item" dir="{% if error.is_bidi %}rtl{% else %}ltr{% endif %}">
                            <span class="fa fa-exclamation-triangle"></span>
                            {{ error.text }}
                        </div>
                    {% endfor %}
                </div>

            </div>
        </div>
    </div>

    {% block body_script_tags %}
        <script>
            const dragDrop = document.querySelector('.drag-drop');
            const fileInput = document.getElementById("file-input")
            const label = document.getElementById('label');
            const inputFileHelpText = document.getElementById("input-help-text")
            const inputFileIcon = document.getElementById("input-icon")

            function dragDropZoneSuccess(fileName) {
                dragDrop.classList.remove('active', 'error');
                dragDrop.classList.add('success')
                inputFileIcon.classList.remove('fa-spinner', 'fa-spin', 'fa-cloud-upload-alt');
                inputFileIcon.classList.add('fa-check-circle');
                inputFileHelpText.textContent = fileName;
            }

            function dragDropZoneError(errorMessage) {
                dragDrop.classList.remove('active', "success");
                dragDrop.classList.add('error');
                inputFileIcon.classList.remove('fa-cloud-upload-alt', 'fa-spinner', 'fa-spin');
                inputFileIcon.classList.add("fa-check-circle");
                inputFileHelpText.textContent = errorMessage;
            }

            dragDrop.addEventListener('dragover', (e) => {
                dragDrop.classList.add('active');
                inputFileIcon.classList.remove("fa-cloud-upload-alt");
                inputFileIcon.classList.add('fa-spinner', 'fa-spin');
            });

            dragDrop.addEventListener('dragleave', () => {
                dragDrop.classList.remove('active');
                inputFileIcon.classList.remove('fa-spinner', 'fa-spin');
                if (dragDrop.classList.contains("success"))
                    inputFileIcon.classList.add("fa-check-circle");
                else
                    inputFileIcon.classList.add('fa-cloud-upload-alt');
            });

            dragDrop.addEventListener('drop', (e) => {
                dragDropZoneSuccess(e.dataTransfer.files[0].name)
            });

            fileInput.addEventListener("change", (e) => {
                dragDropZoneSuccess(e.target.files.item(0).name)
            })
        </script>
    {% endblock body_script_tags %}

{% endblock body %}
