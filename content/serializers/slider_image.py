from content.models import SliderImage
from core.utils import timezone
from core.models import Q, F
from core import serializers
from translation.utils import get_object_translation


class SliderImageSerializer(serializers.ModelSerializer):
    url = serializers.SerializerMethodField()
    url_2 = serializers.SerializerMethodField()

    class Meta:
        model = SliderImage
        fields = (
            "id",
            "type",
            "image",
            "image_2",
            "color",
            "url",
            "url_2",
        )

    def get_url(self, obj):
       return obj.url

    def get_url_2(self, obj):
        return obj.url_2


    def to_representation(self, instance):
        data = super().to_representation(instance)
        data.update(get_object_translation(instance, self.context.get("lang", "en")))
        return data