import json

from content.models import Gift
from core.serializers import ModelSerializer
from files.models import Image
from rest_framework import serializers


class GiftSerializer(ModelSerializer):
    extra = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()

    class Meta:
        model = Gift
        fields = ("id", "type", "name", "image", "credit", "price", "operator", "code", "extra")

    def get_extra(self, obj):
        return json.loads(obj.properties)

    def get_image(self, obj):
        try:
            return (
                Image.objects.filter(object_id=obj.id, content_type__model="gift")
                .order_by("-priority")
                .first()
                .image.url
            )
        except Exception as e:
            return ""
