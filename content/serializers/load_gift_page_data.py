import datetime

from accounts.models import User
from content.models import Gift, UserGift
from content.serializers import GiftSerializer
from core import models
from core.serializers import ModelSerializer
from django.db.models import Count, Sum
from rest_framework import serializers


class LoadGiftPageDataSerializer(ModelSerializer):
    total_credit = serializers.SerializerMethodField()
    month_credit = serializers.SerializerMethodField()
    credit_types = serializers.SerializerMethodField()
    recommended_gifts = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ("total_credit", "month_credit", "credit_types", "recommended_gifts")

    def get_total_credit(self, obj):
        return obj.credit

    def get_month_credit(self, obj):
        return (
            obj.credit_items.filter(
                site=models.Site.objects.get_current(),
                created_at__gte=datetime.datetime.now() - datetime.timedelta(days=30),
            ).aggregate(models.Sum("amount"))["amount__sum"]
            or 0
        )

    def get_credit_types(self, obj):
        # I could get them in one query but only if all the fields had data, you can see query in #b0a8b8ae commit
        return [
            {
                "title": "ثبت سرویس",
                "sum": obj.credit_items.filter(desc__contains="submit_service").aggregate(Sum("amount"))["amount__sum"]
                or 0,
                "icon": "fal fa-car-mechanic",
                "modal": "",
            },
            {
                "title": "ثبت فاکتور",
                "sum": obj.credit_items.filter(desc__contains="submit_factor").aggregate(Sum("amount"))["amount__sum"]
                or 0,
                "icon": "fal fa-file-invoice-dollar",
                "modal": "",
            },
            {
                "title": "دعوت از دوستان",
                "sum": obj.credit_items.filter(desc__contains="invite_friend").aggregate(Sum("amount"))["amount__sum"]
                or 0,
                "icon": "fal fa-user-plus",
                "modal": "invite",
            },
            {
                "title": "کد محصول",
                "sum": obj.credit_items.filter(desc__contains="product_code").aggregate(Sum("amount"))["amount__sum"]
                or 0,
                "icon": "fal fa-barcode-scan",
                "modal": "serial_number",
            },
        ]

    def get_recommended_gifts(self, obj):
        return GiftSerializer(
            Gift.objects.filter(
                id__in=list(
                    UserGift.objects.filter(user=obj)
                    .values("gift__name")
                    .annotate(c=Count("gift"))
                    .order_by("-c")[:3]
                    .values_list("gift__id", flat=True)
                )
            ),
            many=True,
            fields=("id", "name", "image", "credit", "price", "extra"),
        ).data
