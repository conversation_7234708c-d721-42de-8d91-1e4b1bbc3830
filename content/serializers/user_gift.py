import json

from content.models import UserGift
from core.serializers import ModelSerializer
from rest_framework import serializers


class UserGiftSerializer(ModelSerializer):
    gift_name = serializers.SerializerMethodField()
    extra = serializers.SerializerMethodField()

    class Meta:
        model = UserGift
        fields = ("id", "user", "gift_name", "extra", "gift_mobile_number", "used_credit", "is_charged", "reference_id")

    def get_gift_name(self, obj):
        return obj.gift.name

    def get_extra(self, obj):
        return json.loads(obj.properties)
