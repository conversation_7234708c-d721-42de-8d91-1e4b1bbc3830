from rest_framework import serializers

from content.models import ShortUrl


class ShortUrlSerializer(serializers.ModelSerializer):
    code = serializers.CharField(required=False)
    click_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = ShortUrl
        fields = '__all__'

    def validate(self, attrs):
        if len(attrs.get('code') or '') == 0:
            attrs['code'] = ShortUrl.generate_code(5, 10)
        if ShortUrl.objects.filter(code=attrs['code']).exists():
            raise serializers.ValidationError({
                'code': 'This code is already in use.'
            })
        return super().validate(attrs)
