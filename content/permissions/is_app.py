from content import models
from core.permissions import BasePermission


class IsApp(BasePermission):
    def has_permission(self, request, view):
        authorization_header = request.headers.get('Authorization')

        if authorization_header is None:
            return False

        if not authorization_header.lower().startswith('token '):
            return False

        token_key = authorization_header.split(' ')[1]

        app_token = models.AppToken.objects.filter(
            key=token_key,
            enabled=True,
        ).select_related(
            'site'
        ).first()

        if app_token is None:
            return False

        request.site = app_token.site
        return True
