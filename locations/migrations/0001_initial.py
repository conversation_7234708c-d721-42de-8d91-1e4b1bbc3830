# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('latitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('longitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('is_default', models.BooleanField(default=False, editable=False)),
            ],
            options={
                'db_table': 'matrix_locations_city',
            },
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('latitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('longitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('name', models.CharField(max_length=200, unique=True)),
                ('phone_code', models.CharField(max_length=10, unique=True)),
                ('language_code', models.CharField(max_length=10)),
                ('code', models.CharField(max_length=10)),
                ('is_default', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'matrix_locations_country',
            },
        ),
        migrations.CreateModel(
            name='State',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('latitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('longitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('name', models.CharField(max_length=200)),
                ('is_default', models.BooleanField(default=False, editable=False)),
                ('code', models.CharField(max_length=10)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='states', to='locations.country')),
            ],
            options={
                'db_table': 'matrix_locations_state',
                'unique_together': {('country', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('latitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('longitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('name', models.CharField(max_length=200)),
                ('code', models.PositiveSmallIntegerField(blank=True, null=True)),
                ('is_default', models.BooleanField(default=False, editable=False)),
                ('city', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='regions', to='locations.city')),
            ],
            options={
                'db_table': 'matrix_locations_region',
                'unique_together': {('city', 'name', 'code')},
            },
        ),
        migrations.AddField(
            model_name='city',
            name='state',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cities', to='locations.state'),
        ),
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('latitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('longitude', models.DecimalField(decimal_places=6, default=0, max_digits=8)),
                ('street_number', models.CharField(max_length=10)),
                ('route', models.CharField(max_length=50)),
                ('postal_code', models.CharField(blank=True, max_length=10, null=True)),
                ('is_default', models.BooleanField(default=False, editable=False)),
                ('description', models.TextField(max_length=150)),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addresses', to='locations.region')),
            ],
            options={
                'db_table': 'matrix_locations_address',
            },
        ),
        migrations.AlterUniqueTogether(
            name='city',
            unique_together={('state', 'name')},
        ),
    ]
