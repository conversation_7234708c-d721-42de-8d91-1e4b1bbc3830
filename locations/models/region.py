from core import models


class Region(models.AbstractBaseModel, models.AbstractLatitudeLongitudeModel):
    name = models.CharField(max_length=200)
    city = models.ForeignKey("locations.City", on_delete=models.CASCADE, related_name="regions")
    code = models.PositiveSmallIntegerField(null=True, blank=True)
    is_default = models.BooleanField(default=False, editable=False)

    class Meta:
        unique_together = ("city", "name", "code")
        db_table = "matrix_locations_region"

    def __str__(self):
        return "[{}] {}".format(self.id, self.name)

    @property
    def display_name(self):
        return self.city.state.country.mapping_field_name("region")