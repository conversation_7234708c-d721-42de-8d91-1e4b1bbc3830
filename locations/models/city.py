from core import models


class City(models.AbstractBaseModel, models.AbstractLatitudeLongitudeModel):
    name = models.CharField(max_length=200)
    state = models.ForeignKey("locations.State", on_delete=models.CASCADE, related_name="cities")
    is_default = models.BooleanField(default=False, editable=False)

    class Meta:
        unique_together = ("state", "name")
        db_table = "matrix_locations_city"

    def __str__(self):
        return "[{}] {}".format(self.id, self.name)

    @property
    def display_name(self):
        return self.state.country.mapping_field_name("city")
