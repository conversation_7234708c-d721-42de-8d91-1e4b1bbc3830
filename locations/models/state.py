from core import models
from django.utils.text import slugify


class State(models.AbstractBaseModel, models.AbstractLatitudeLongitudeModel):
    name = models.CharField(max_length=200)
    country = models.ForeignKey("locations.Country", on_delete=models.CASCADE, related_name="states")
    is_default = models.BooleanField(default=False, editable=False)
    code = models.CharField(max_length=10)
    saleor_attribute_value_id = models.CharField(max_length=64, editable=True, null=True, blank=True)
    slug = models.SlugField(max_length=200, allow_unicode=True, null=True, blank=True)

    class Meta:
        unique_together = ("country", "name")
        db_table = "matrix_locations_state"

    def __str__(self):
        return "[{}] {}".format(self.id, self.name)

    @property
    def display_name(self):
        return self.country.mapping_field_name("state")

    def save(self, *args, **kwargs):
        # if self._state.adding:
        self.slug = slugify(self.name)
        super().save(*args, **kwargs)

