from core.admin.abstract_base_admin import AbstractBaseAdmin


class CityAdmin(AbstractBaseAdmin):
    list_display = ("id", "name", "state", "is_default", "is_active", "priority")
    list_filter = ("is_active", "state")
    list_select_related = ("state",)
    raw_id_fields = ("state",)
    search_fields = ("id", "name")
    list_editable = ("is_active", "priority")
    actions = ("delete_selected_cities",)

    fieldsets = (
        AbstractBaseAdmin.default_fieldset,
        (
            None,
            {"fields": ("name", "state")},
        ),
        ("Coordinates", {"fields": (("latitude", "longitude"),), "classes": ("collapse",)}),
    )

    def delete_selected_cities(self, request, queryset):
        queryset = queryset.exclude(is_active=True).exclude(is_default=True)
        queryset.delete()
        return self.message_user(request, "{} cities deleted successfully.".format(queryset.count()))

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        if obj is not None and obj.is_default:
            return False
        return super().has_change_permission(request, obj)

    def get_queryset(self, request):
        """Optimize queryset by prefetching related data to reduce queries."""
        queryset = super().get_queryset(request)
        return queryset.select_related(
            "state__country",)
