from core import serializers
from locations.models import Address


class AddressSerializer(serializers.ModelSerializer):
    region = serializers.CharField(source="region.name")
    city_id = serializers.IntegerField(source="region.city_id")
    city = serializers.CharField(source="region.city.name")
    province_id = serializers.IntegerField(source="region.city.province_id")
    province = serializers.CharField(source="region.city.province.name")

    class Meta:
        model = Address
        fields = (
            "id",
            "address",
            "zip_code",
            "latitude",
            "longitude",
            "region",
            "region_id",
            "city",
            "city_id",
            "province",
            "province_id",
        )
