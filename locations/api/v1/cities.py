import logging

from core.cache import cache, get_hashed_cache_key
from core.http import JsonResponse
from locations.models import City
from locations.serializers import CitySerializer
from rest_framework.views import APIView

logger = logging.getLogger("locations")


class CitiesAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        key = get_hashed_cache_key("CitiesAPI", request.query_params.get("province_id"))
        data = cache.get(key)
        if data:
            return JsonResponse(data=data)

        cities = City.objects.filter(is_default=False)
        if request.query_params.get("province_id"):
            cities = cities.filter(province_id=request.query_params.get("province_id"))
        data = CitySerializer(cities.distinct().order_by("-priority"), many=True).data
        cache.set(key, data, 600)
        return JsonResponse(data=data)
