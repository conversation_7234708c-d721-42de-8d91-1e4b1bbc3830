import logging

from core.cache import cache, get_hashed_cache_key
from core.http import JsonResponse
from locations.models import Region
from locations.serializers import RegionSerializer
from rest_framework.views import APIView

logger = logging.getLogger("locations")


class RegionsAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        key = get_hashed_cache_key("RegionsAPI", request.query_params.get("city_id"))
        data = cache.get(key)
        if data:
            return JsonResponse(data=data)

        regions = Region.objects.filter(is_default=False, is_active=True, city_id=request.query_params.get("city_id"))
        data = RegionSerializer(regions.order_by("-priority"), many=True).data
        cache.set(key, data, 600)
        return JsonResponse(data=data)
