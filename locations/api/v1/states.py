import logging

from core.cache import cache
from core.http import JsonResponse
from locations.models import State
from locations.serializers import StateSerializer
from rest_framework.views import APIView

logger = logging.getLogger("locations")


class StatesAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        country_id = request.query_params.get("country_id")
        if country_id:
            states = State.objects.filter(
                is_default=False,
                is_active=True,
                country_id=int(country_id)
            ).order_by("-priority")
        else:
            states = State.objects.filter(is_default=False, is_active=True).order_by("-priority")
        data = StateSerializer(states.distinct().order_by("-priority"), many=True).data
        return JsonResponse(data=data)
