import logging

from core.cache import cache
from core.http import JsonResponse
from locations.models import Country
from locations.serializers import CountrySerializer
from rest_framework.views import APIView

logger = logging.getLogger("locations")


class CountryAPI(APIView):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request):
        key = "CountryAPI"
        data = cache.get(key)
        if data:
            return JsonResponse(data=data)

        countries = Country.objects.filter(is_default=False, is_active=True).order_by("-priority")
        data = CountrySerializer(countries.distinct().order_by("-priority"), many=True).data
        cache.set(key, data, 600)
        return JsonResponse(data=data)
