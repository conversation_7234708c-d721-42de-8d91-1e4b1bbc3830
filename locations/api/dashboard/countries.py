import logging

from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import CooKieAuthentication
from core.http import JsonResponse
from locations.models import Country
from locations.serializers import CountrySerializer
from rest_framework.views import APIView

from locations.serializers.country import CountryDashboardSerializer

logger = logging.getLogger("dashboard")


class CountriesDashboardAPI(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request):
        countries = Country.objects.filter(is_default=False, states__isnull=False)
        data = CountryDashboardSerializer(countries.distinct("id").order_by("-id"), many=True).data
        return JsonResponse(data=data)
