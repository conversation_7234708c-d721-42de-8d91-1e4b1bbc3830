import logging

from django.db import transaction
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import Coo<PERSON>ieAuthentication
from core.cache import cache, get_hashed_cache_key
from core.http import JsonResponse
from locations.models import Region, Address
from locations.serializers.dashboard.address import AddressSerializer
from rest_framework.views import APIView

from service_center.models import ServiceCenter

logger = logging.getLogger("dashboard")


class AddressDashboardAPI(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    @transaction.atomic
    def post(self, request):
        sc_id = request.query_params.get("sc_id")
        serializer = AddressSerializer(data=request.data)
        if not serializer.is_valid():
            return JsonResponse(
                data=serializer.errors, status=status.HTTP_400_BAD_REQUEST
            )
        self.exception_data = {"message": "error in saving address"}
        address = serializer.save()
        sc = ServiceCenter.objects.get(id=sc_id)
        sc.addresses.add(address)
        return JsonResponse(message=address.pk, status=201)

    @transaction.atomic
    def put(self, request):
        address_id = request.query_params.get("address_id")
        address_obj = Address.objects.get(pk=address_id)
        serializer = AddressSerializer(data=request.data, instance=address_obj)
        if not serializer.is_valid():
            return JsonResponse(
                data=serializer.errors, status=status.HTTP_400_BAD_REQUEST
            )
        self.exception_data = {"message": "error in saving address"}
        address = serializer.save()
        return JsonResponse(message=address.pk, status=200)
