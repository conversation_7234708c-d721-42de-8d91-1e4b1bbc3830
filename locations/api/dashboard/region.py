import logging

from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import Coo<PERSON>ie<PERSON>uthentication
from core.cache import cache, get_hashed_cache_key
from core.http import JsonResponse
from locations.models import Region
from locations.serializers import RegionSerializer
from rest_framework.views import APIView

logger = logging.getLogger("dashboard")


class RegionsDashboardAPI(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request):
        regions = Region.objects.filter(is_active=True, is_default=False)
        if request.query_params.get("cities"):
            regions = regions.filter(city_id__in=request.query_params.get("cities").split(","))
        data = RegionSerializer(regions.order_by("-priority"), many=True).data
        return JsonResponse(data=data)
