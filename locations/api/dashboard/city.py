import logging

from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import Coo<PERSON>ie<PERSON>uthentication
from core.http import JsonResponse
from locations.models import City
from locations.serializers import CitySerializer
from rest_framework.views import APIView

logger = logging.getLogger("dashboard")


class CitiesDashboardAPI(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request):
        cities = City.objects.filter(is_active=True, is_default=False, regions__isnull=False)
        if request.query_params.get("states"):
            cities = cities.filter(state_id__in=request.query_params.get("states").split(","))
        data = CitySerializer(cities.distinct().order_by("-id"), many=True).data
        return JsonResponse(data=data)
