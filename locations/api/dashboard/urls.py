from django.urls import path

from .region import RegionsDashboardAPI
from .state import StatesDashboardAPI
from .city import CitiesDashboardAPI
from .countries import CountriesDashboardAPI
from .address import AddressDashboardAPI

app_name = "locations"

urlpatterns = [
    path("states/", StatesDashboardAPI.as_view(), name="states-dashboard"),
    path("cities/", CitiesDashboardAPI.as_view(), name="cities-dashboard"),
    path("countries/", CountriesDashboardAPI.as_view(), name="countries-dashboard"),
    path("regions/", RegionsDashboardAPI.as_view(), name="regions-dashboard"),
    path("address/", AddressDashboardAPI.as_view(), name="address-dashboard"),
]
