import logging

from rest_framework.permissions import IsAuthenticated

from accounts.permissions.is_staff import IsStaff
from core.authentication.cookie_auth import Coo<PERSON>ie<PERSON>uthentication
from core.http import JsonResponse
from locations.models import State
from locations.serializers import StateSerializer
from rest_framework.views import APIView

logger = logging.getLogger("dashboard")


class StatesDashboardAPI(APIView):
    authentication_classes = (CooKieAuthentication,)
    permission_classes = (IsAuthenticated, IsStaff)

    def get(self, request):
        countries = request.query_params.get("countries", None)
        states = State.objects.filter(is_active=True, is_default=False, cities__isnull=False)
        if countries:
            states = states.filter(country_id__in=countries.split(","))
        data = StateSerializer(states.distinct().order_by("-id"), fields=["id", "name"], many=True).data
        return JsonResponse(data=data)
