from django.db.models.signals import post_save
from django.dispatch import receiver
from locations.models import City, State


@receiver(post_save, sender=State)
def state_signal(sender, instance, **kwargs):
    try:
        city = City.objects.get(state=instance, is_default=True)
        city.name = "dci_{}".format(instance.name)
        city.save()
    except City.DoesNotExist:
        City.objects.create(name="dci_{}".format(instance.name), state=instance, is_default=True)
    except Exception as e:
        pass
