from django.db.models.signals import post_save
from django.dispatch import receiver
from locations.models import Address, Region


@receiver(post_save, sender=Region)
def region_signal(sender, instance, **kwargs):
    try:
        Address.objects.get(region=instance, is_default=True)
    except Address.DoesNotExist:
        Address.objects.create(region=instance, is_default=True)
    except Exception as e:
        pass
