from django.db.models.signals import post_save
from django.dispatch import receiver
from locations.models import City, Region


@receiver(post_save, sender=City)
def city_signal(sender, instance, **kwargs):
    try:
        region = Region.objects.get(city=instance, is_default=True)
        region.name = "dre_{}".format(instance.name)
        region.save()
    except Region.DoesNotExist:
        Region.objects.create(name="dre_{}".format(instance.name), city=instance, is_default=True)
    except Exception as e:
        pass
