from django.db.models.signals import post_save
from django.dispatch import receiver
from locations.models import Country, State


@receiver(post_save, sender=Country)
def country_signal(sender, instance, **kwargs):
    try:
        state = State.objects.get(country=instance, is_default=True)
        state.name = "dpr_{}".format(instance)
        state.save()
    except State.DoesNotExist:
        State.objects.create(name="dpr_{}".format(instance), country=instance, is_default=True)
    except Exception as e:
        pass
