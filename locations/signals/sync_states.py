import logging
from django.dispatch import receiver
from django.db.models.signals import post_save
from locations.models import State
from saleor.utils import Saleor

logger = logging.getLogger("saleor")


@receiver(post_save, sender=State)
def sync_states(sender, instance, created, **kwargs):
    if not instance.is_default:
        saleor = Saleor(site="hig")
        response, attr_value_id = saleor.create_attribute_value(instance.name)
        if response:
            try:
                instance.saleor_attribute_value_id = attr_value_id
                instance.save()
            except Exception as e:
                logger.info(
                    "SALEOR| GCREATE-ADDRESS-API|, message {}".format(str(e))
                )


