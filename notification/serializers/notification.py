from core import serializers
from notification.models import Notification, UserNotification
from translation.utils import get_object_translation


class NotificationSerializer(serializers.ModelSerializer):
    seen = serializers.SerializerMethodField()

    class Meta:
        model = Notification
        fields = '__all__'

    def get_seen(self, obj):
        if self.context.get("user") and not self.context.get("user").is_anonymous:
            try:
                return UserNotification.objects.get(user=self.context.get("user"), notification=obj).seen
            except UserNotification.DoesNotExist:
                return False
        return False

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data.update(get_object_translation(instance, self.context.get("lang", "en")))
        return data
