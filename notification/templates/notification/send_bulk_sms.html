{% extends 'base_bootstrap.html' %}
{% load static %}

{% block head_style_tags %}
    <link href='http://fonts.googleapis.com/css?family=Roboto' rel='stylesheet' type='text/css'>
    <style>
        * {
            font-family: 'Roboto', sans-serif !important;
        }
        body {
            padding: 40px;
        }
    </style>
    {{ block.super }}
{% endblock %}
{% block head_title_tag %}<title>Send Bulk Sms</title>{% endblock %}
{% block body %}
    <script>
        if ( window.history.replaceState ) {
            window.history.replaceState( null, null, window.location.href );
        }
    </script>
    <div>
        <h4>Send Bulk Sms</h4>
    </div>
    <div style="color: lightslategray">
        <form method="POST" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="row form-group">
                <div class="col-md-4">
                    <label style="color: midnightblue">message</label>
                    <textarea style="direction: rtl" class="form-control" name="message" placeholder="Enter text of message" required rows="8">{{  message }}</textarea>
                    <div class="row mt-3">
                        <p>remaining credit: <span class="text-primary">{{ remaining_credit }}</span></p>
                    </div>
                </div>
                <div class="col-md-2">
                    <label style="color: midnightblue">Excel File</label>
                    <input type="file" class="form-control" name="file" required value="{{ file }}">
                </div>
                <div class="col-md-2">
                    <label style="color: midnightblue">Number of Variables</label>
                    <input type="number" class="form-control" name="token_count" value="{{ token_count }}" min="0" max="5">
                </div>
                <div class="col-md-4">
                    <p>To use variables in message add number of variables and  add them with format of `TOKEN{}` to message
                        example message with 3 variables:
                    </p>
                    <hr>
                    <code>
                    ` Hello TOKEN1, This is a test message for TOKEN2. your promo code is TOKEN3`
                    </code>
                    <hr>
                    <p>Your can use up to 5 variable per message.Each variable should be a column in excel file
                        e.g: if you have 3 variables then your excel file should have 4 columns.
                        first are the mobile numbers and 3 columns for your 3 variables.
                    </p>
                    <hr>
                    <code>Remember system is case sensitive so TOKEN1 is valid, Token1, token1,TOKEn1, etc are not valid!!!</code>
                </div>
            </div>
            <div class="row form-group">
                <div class="col-md-6">
                    <button type="submit" class="btn btn-block btn-primary">Submit</button>
                </div>
            </div>
        </form>
    </div>
    {% if responses %}
    <div class="mt-5">
        <h4>Response</h4>
        <p>Total: {{ total }}</p>
        <p>Created: {{ created }}</p>
        <ul>
        {% for d in responses %}
            <li>{{ d }}</li>
        {% endfor %}
        </ul>
    </div>
    {% endif %}
    {% if error %}
    <div class="mt-5">
        <h4 class="text-danger">Exception</h4>
        <p>{{ error_msg | safe }}</p>
    </div>
    {% endif %}
    {% if success %}
    <div class="mt-5">
        <h4 class="text-succes">Messages were queued</h4>
        <p>Total number of users: <small>{{ users_count }}</small></p>
        <p>Total cost: <small>{{ total_cost }}</small></p>
    </div>
    {% endif %}
{% endblock %}