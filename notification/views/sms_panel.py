import logging

import openpyxl
from django.core.exceptions import PermissionDenied

from core.validators import mobile_number_validator
from django.shortcuts import render
from django.views import View
from kavenegar import <PERSON><PERSON>egarAP<PERSON>
from notification.utils import create_sms_object, send_bulk_sms

logger = logging.getLogger("notification")


class SendSmsAdminView(View):
    template_name = "notification/send_bulk_sms.html"

    @staticmethod
    def set_kavenegar_api():
        try:
            api = KavenegarAPI("4F3570545351474733357A3352344F68587531656C6F37616F677A4F41733355")
            return api
        except Exception as e:
            logger.exception("[KAVENEGAR] message={}".format(e))
            return False

    @staticmethod
    def parse_message_with_token(message, row_values, token_count):
        try:
            final_msg = message
            for i in range(1, token_count + 1):
                if "TOKEN{}".format(i) in message:
                    final_msg = final_msg.replace("TOKEN{}".format(i), row_values[i])
            return final_msg
        except:
            return False

    def initial_excel(self, file, message, token_count):
        sheet = openpyxl.load_workbook(file).active
        message_list = list()
        err_list = list()
        sender_list = []
        if "TOKEN" in message and token_count == 0:
            err_msg = "If there is no variable then delete the TOKEN from the message"
            err_list.append(err_msg)

        mobile_number_list = []
        for row in range(1, sheet.max_row + 1):
            mobile_number_list.append(self.validate_mobile_number(sheet.cell(row, 1).value))

        for index, mobile in enumerate(mobile_number_list):
            sender_list.append("20000400444")
            if not mobile:
                err_msg = "Skipping row {} cause: mobile_number not valid".format(index + 1)
                err_list.append(err_msg)
                continue
            if token_count <= 0:
                message_list.append(message)
                continue

            row_fields = []
            for col in range(1, sheet.max_column + 1):
                row_fields.append(sheet.cell(index + 1, col).value)

            row_fields = list(filter(lambda x: x not in ["", " ", None], row_fields))

            if len(row_fields) != (token_count + 1):
                err_msg = "Skipping row {} cause: Number of tokens does not match".format(index + 1)
                err_list.append(err_msg)
                continue
            parsed_message = self.parse_message_with_token(message, row_fields, token_count)
            if not parsed_message:
                err_msg = "Skipping row {} cause: one of the tokens is not valid".format(index + 1)
                err_list.append(err_msg)
                continue
            message_list.append(parsed_message)
        if err_list:
            return False, err_list
        final_data = {"receptor": mobile_number_list, "sender": sender_list, "message": message_list}
        return True, final_data

    @staticmethod
    def validate_mobile_number(mobile_number):
        mobile_number = str(mobile_number)
        if len(mobile_number) == 10:
            mobile_number = "0{}".format(mobile_number)
        try:
            mobile_number_validator(mobile_number)
            return mobile_number
        except Exception as e:
            return ""

    def can_we_send(self, api, message, users_count):
        try:
            account_credit = api.account_info()["remaincredit"]
            message_cost = (((len(message) - 1) // 70) + 1) * 143
            total_cost = message_cost * users_count
            self.total_cost = total_cost
            if account_credit > total_cost:
                return True, None
            else:
                txt = "not enough credit for sending this excel"
                return False, txt
        except:
            txt = "error connecting to kavenegar!"
            return False, txt

    def spliter(self, the_list, number=150):
        return [the_list[i * number : (i + 1) * number] for i in range((len(the_list) + number - 1) // number)]

    def get(self, request):
        if not request.user.has_perm("notification.sms_panel"):
            raise PermissionDenied

        context = {}
        api = self.set_kavenegar_api()
        context["remaining_credit"] = api.account_info()["remaincredit"]
        return render(request, self.template_name, context=context)

    @staticmethod
    def _validate_post_data(data, files):
        message = data.get("message")
        file = files.get("file")
        try:
            token_count = int(data.get("token_count", 0))
        except:
            token_count = 0
        return message, file, token_count

    def _make_data_ready(self, data):
        final_mobile_numbers = self.spliter(data["receptor"], number=10)
        final_messages = self.spliter(data["message"], number=10)
        final_senders = self.spliter(data["sender"], number=10)
        final = {"mobiles": final_mobile_numbers, "messages": final_messages, "senders": final_senders}
        return final

    def post(self, request):
        if not request.user.has_perm("notification.sms_panel"):
            raise PermissionDenied

        message, file, token_count = self._validate_post_data(request.POST, request.FILES)
        is_valid, data = self.initial_excel(file, message, token_count)
        exception = False
        exception_msg = ""
        success = True
        if is_valid:
            api = self.set_kavenegar_api()
            status, txt = self.can_we_send(api, message, users_count=len(data["receptor"]))
            if status:
                final_data = self._make_data_ready(data)
                send_bulk_sms.apply_async(
                    (final_data["mobiles"], final_data["messages"], final_data["senders"]),
                    link=create_sms_object.s(),
                    queue="periodic_queue",
                )
            else:
                exception, success, exception_msg = True, False, txt
        else:
            exception, success = True, False
            data.insert(0, "Error in reading excel data...")
            exception_msg = "<br>".join(data)
        context = {
            "message": message,
            "success": success,
            "token_count": token_count,
            "file": file,
            "error": exception,
            "error_msg": exception_msg,
        }
        if success:
            context["total_cost"] = self.total_cost
            context["users_count"] = len(data["receptor"])
        return render(request, self.template_name, context=context)
