from core import admin


class SMSAdmin(admin.AbstractBaseAdmin):
    list_display = ("id", "message_id", "type", "_user", "status_message", "send_date", "created_at")
    list_editable = ()
    raw_id_fields = ("user",)
    search_fields = ("user__username", "message_id")
    readonly_fields = (
        "status_message",
        "message_id",
        "is_received",
        "created_at",
        "updated_at",
        "user",
        "type",
        "_user",
    )
    fieldsets = (
        (
            None,
            {
                "fields": (
                    "send_signal",
                    ("created_at", "updated_at"),
                    ("_user", "is_received", "type"),
                    ("message_id", "status_message"),
                    "send_date",
                    "text",
                )
            },
        ),
    )

    _user = admin.get_object_link("user")
