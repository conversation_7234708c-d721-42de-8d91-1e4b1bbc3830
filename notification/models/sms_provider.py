from django.db import models
from django.contrib.sites.models import Site
from django.utils.text import slugify


class SMSProvider(models.Model):
    name = models.CharField(max_length=255)
    slug = models.SlugField(unique=True, max_length=255, blank=True)
    is_active = models.BooleanField(default=True)
    priority = models.PositiveIntegerField(default=1)
    sites = models.ManyToManyField(Site, related_name="sms_providers", blank=True)

    def __str__(self):
        return "{}".format(self.name)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)
