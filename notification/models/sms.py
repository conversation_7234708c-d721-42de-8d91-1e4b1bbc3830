import logging

from constants import SMSType
from core import models
from django.conf import settings
from django.template.loader import render_to_string
from notification.utils import GetKavenegarAPI, date_to_unix, status_codes

logger = logging.getLogger("notification")


class SMSManager(models.Manager):
    def create_from_template(self, template_name, context={}, **kwargs):
        return self.create(text=render_to_string(template_name=template_name, context={**kwargs, **context}), **kwargs)


class SMS(models.AbstractBaseModel):
    user = models.ForeignKey("accounts.User", on_delete=models.CASCADE, related_name="user_sms")
    type = models.CharField(max_length=50, choices=SMSType.choices, default=SMSType.BILL)
    text = models.TextField()
    send_signal = models.BooleanField(default=False)
    message_id = models.CharField(max_length=250, null=True, blank=True, editable=False)
    status_code = models.IntegerField(default=0, editable=False)
    send_date = models.DateTimeField(null=True, blank=True)
    is_received = models.BooleanField(default=False, editable=False)

    class Meta:
        db_table = "matrix_notification_sms"

    objects = SMSManager()

    @property
    def status_message(self):
        try:
            return status_codes.get(self.status_code)
        except Exception:
            return "not sent by server (check please)"

    def save(self, *args, **kwargs):
        send_sms = self.send_signal and not self.message_id
        self.send_signal = False
        self.is_received = self.status_code in [4, 5, 10]
        super().save(*args, **kwargs)
        if send_sms:
            self.send()

    def send(self):
        try:
            if settings.SEND_SMS:
                response = GetKavenegarAPI.get_api().sms_send(
                    params={
                        # 'sender': settings.KAVENEGAR_SENDER,
                        "receptor": self.user.username,
                        "message": self.text,
                    }
                )
                self.message_id = response[0]["messageid"]
                self.status_code = response[0]["status"]
                self.save()
            return self

        except Exception as e:
            logger.error("[KAVENEGAR] message={}".format(e))
            return self

    def send_at_date(self):
        try:
            unix_time = date_to_unix(datetime=self.send_date)
            if settings.SEND_SMS:
                response = GetKavenegarAPI.get_api().sms_send(
                    params={
                        "sender": settings.KAVENEGAR_SENDER,
                        "receptor": self.user.username,
                        "message": self.text,
                        "date": unix_time,
                    }
                )
                self.message_id = response[0]["messageid"]
                self.status_code = response[0]["status"]
                self.save()
            return self

        except Exception as e:
            logger.error("[KAVENEGAR] message={}".format(e))
            return self

    def cancel(self):
        try:
            if settings.SEND_SMS:
                response = GetKavenegarAPI.get_api().sms_cancel(params={"messageid": self.message_id})
                self.status_code = response[0]["status"]
                self.save()
            return self

        except Exception as e:
            logger.error("[KAVENEGAR] message={}".format(e))
            return self
