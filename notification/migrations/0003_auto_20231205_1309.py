# Generated by Django 3.2.10 on 2023-12-05 13:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('sites', '0002_alter_domain_unique'),
        ('notification', '0002_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='sms',
            name='bill_item',
        ),
        migrations.CreateModel(
            name='SMSProvider',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('slug', models.SlugField(blank=True, max_length=255, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('priority', models.PositiveIntegerField(default=1)),
                ('sites', models.ManyToManyField(blank=True, related_name='sms_providers', to='sites.Site')),
            ],
        ),
    ]
