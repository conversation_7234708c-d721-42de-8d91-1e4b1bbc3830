# Generated by Django 3.2.10 on 2023-09-17 08:58

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SMS',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='active')),
                ('priority', models.IntegerField(default=1000000)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('type', models.CharField(choices=[('bill', 'Bill'), ('sc', 'Service Center'), ('points', 'Points'), ('returned', 'Returned'), ('ag', 'Agent'), ('payment', 'Payment'), ('gift', 'Gift'), ('sr', 'Service Reminder'), ('admin', 'Admin'), ('dynamics', 'Dynamics'), ('factor', 'Factor'), ('order', 'Order'), ('credit_bill', 'Credit Bill'), ('prepayment', 'Prepayment'), ('validation', 'Validation')], default='bill', max_length=50)),
                ('text', models.TextField()),
                ('send_signal', models.BooleanField(default=False)),
                ('message_id', models.CharField(blank=True, editable=False, max_length=250, null=True)),
                ('status_code', models.IntegerField(default=0, editable=False)),
                ('send_date', models.DateTimeField(blank=True, null=True)),
                ('is_received', models.BooleanField(default=False, editable=False)),
            ],
            options={
                'db_table': 'matrix_notification_sms',
            },
        ),
    ]
