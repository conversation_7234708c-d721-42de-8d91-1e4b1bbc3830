# Generated by Django 3.2.10 on 2025-04-26 06:31

from django.db import migrations, models
import django.db.models.deletion
import tinymce.models


class Migration(migrations.Migration):

    dependencies = [
        ('notification', '0008_auto_20250301_1148'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTranslation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lang_code', models.CharField(max_length=35)),
                ('subject', models.CharField(max_length=256, verbose_name='Subject')),
                ('message', tinymce.models.HTMLField(blank=True, null=True, verbose_name='Message')),
                ('image', models.ImageField(blank=True, null=True, upload_to='notification/')),
                ('notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='translations', to='notification.notification')),
            ],
            options={
                'unique_together': {('lang_code', 'notification')},
            },
        ),
    ]
