"""
Celery Task: Delete expired notifications

This task check notification with expire data and remove them if they were expired

Description:
    - Fetches all expired notifications

"""

import logging
from datetime import timedelta

from django.db.models import Q, F, ExpressionWrapper, fields
from django.utils import timezone

from notification.models import Notification
from settings.celery_app import celery_app

logger = logging.getLogger("quick_book")


@celery_app.task(name="delete_expired_notifications", autoregister=True)
def delete_expired_notifications():
    now = timezone.now()

    expiration_expression = ExpressionWrapper(
        F('send_time') + F('expires_in') * timedelta(seconds=1),
        output_field=fields.DateTimeField()
    )

    Notification.objects.annotate(
        expiration_datetime=expiration_expression
    ).filter(is_otp=True).exclude(
        Q(expires_in__isnull=True) | Q(expiration_datetime__gt=now),
    ).delete()

