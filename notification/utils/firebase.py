import json

import requests
from django.conf import settings

headers = {
    "Content-Type": "application/json",
}


def send_bulk_notification(
    registration_ids: list,
    message_title: str,
    message_body: str,
    click_action: str,
    icon_link: str,
    image_link: str,
    priority="high",
    header_auth=None,
):
    if header_auth:
        headers["Authorization"] = "key={}".format(header_auth)
    else:
        headers["Authorization"] = "key={}".format(settings.FIREBASE_SERVER_KEY)

    url = "https://fcm.googleapis.com/fcm/send"
    response = requests.post(
        url,
        headers=headers,
        data=json.dumps(
            {
                "notification": {
                    "title": message_title,
                    "body": message_body,
                    "click_action": click_action,
                    "icon": icon_link,
                    "image": image_link,
                },
                "registration_ids": registration_ids,
                "priority": priority,
            }
        ),
    )
    if response.status_code == 200:
        return response.json()
    else:
        return None


def send_single_notification(
    token: str,
    message_title: str,
    message_body: str,
    click_action: str,
    icon_link: str,
    image_link: str,
    priority="high",
    header_auth=None,
):
    if header_auth:
        headers["Authorization"] = "key={}".format(header_auth)
    else:
        headers["Authorization"] = "key={}".format(settings.FIREBASE_SERVER_KEY)

    url = "https://fcm.googleapis.com/fcm/send"
    response = requests.post(
        url,
        headers=headers,
        data=json.dumps(
            {
                "notification": {
                    "title": message_title,
                    "body": message_body,
                    "click_action": click_action,
                    "icon": icon_link,
                    "image": image_link,
                },
                "token": token,
                "priority": priority,
            }
        ),
    )
    if response.status_code == 200:
        return response.json()
    else:
        return None
