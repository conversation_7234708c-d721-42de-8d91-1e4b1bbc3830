import logging

from accounts.models import User
from constants import SMSType
from settings.celery_app import celery_app

logger = logging.getLogger("notification")


@celery_app.task(name="create_sms_object", autoregister=True, queue="periodic_queue")
def create_sms_object(result_list):
    from notification.models import SMS

    user_info = dict()

    logger.info("user info dict created")
    sms_list = list()
    for item in result_list:
        try:
            status = int(item["status"])
        except Exception as e:
            status = 0

        try:
            user = User.objects.get_or_create(username=item["receptor"])[0]
            sms_list.append(
                SMS(
                    user_id=user.id,
                    type=SMSType.ADMIN,
                    text=item["message"],
                    message_id=item["messageid"],
                    status_code=item["status"],
                )
            )
        except Exception as e:
            logger.info("creating SMS objects")

    logger.info("creating SMS objects")
    SMS.objects.bulk_create(sms_list)
    logger.info("created")
