import logging

from django.conf import settings
from kavenegar import APIException, HTTPException, KavenegarAPI

logger = logging.getLogger("notification")


class GetKavenegarAPI:
    @staticmethod
    def get_api(key=None):
        try:
            api = KavenegarAPI(key or settings.KAVENEGAR_API)
            return api
        except HTTPException as e:
            logger.error("[KAVENEGAR] message={}".format(e))
            return None
        except APIException as e:
            logger.error("[KAVENEGAR] message={}".format(e))
            return None
