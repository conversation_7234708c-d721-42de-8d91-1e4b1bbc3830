import os
import requests


def send_oman_tel_sms(**kwargs):
    userid = os.getenv('OMANTEL_API_USERID') or 'globali_webs'
    password = os.getenv('OMANTEL_API_PASSWORD') or 'GL<PERSON>@3519!bal!76'
    phone_number = kwargs['phonenumber'].replace("+", "")
    message = kwargs['textmessage']
    url = f"https://www.ismartsms.net/iBulkSMS/HttpWS/SMSDynamicAPI.aspx?UserId={userid}&Password={password}&MobileNo={phone_number}&Message={message}&Lang=0&FlashSMS=N"

    response = requests.request("GET", url).json()
    print(response)
    return response == 1
