import logging

from kavenegar import KavenegarAP<PERSON>
from settings.celery_app import celery_app

logger = logging.getLogger("notification")

api = KavenegarAPI("4F3570545351474733357A3352344F68587531656C6F37616F677A4F41733355")


@celery_app.task(name="send_bulk_sms", autoregister=True)
def send_bulk_sms(final_mobile_numbers, final_messages, final_senders):
    success_count = 0
    result_list = list()
    for index, numbers in enumerate(final_mobile_numbers):
        data = {"receptor": str(numbers), "message": str(final_messages[index]), "sender": str(final_senders[index])}
        try:
            response = api.sms_sendarray(data)
            result_list += response
            success_count += len(numbers)
            logger.info("index {}".format(index))
        except Exception as e:
            logger.info("index {}, {}".format(index, e))
    return result_list
