import logging

from notification.utils import GetKavenegarAPI

logger = logging.getLogger("notification")


def check_sms_by_track_id(track_id):
    try:
        api = GetKavenegarAPI.get_api()
        if api:
            response = api.sms_status({"messageid": track_id})
            return response[0]["status"]
        return None
    except Exception as e:
        logger.error("{}".format(e))
        return None
