from .create_sms_task import create_sms_object
from .date_to_unix import date_to_unix
from .firebase import send_bulk_notification, send_single_notification
from .get_api import GetKavenegarAPI
from .send_sms_task import send_bulk_sms
from .url_shorter import shorting_url
from .smsala import SMSAla

status_codes = {
    1: " در صف ارسال قرار دارد",
    2: " زمان بندی شده (ارسال در تاریخ معین)",
    4: " ارسال شده به مخابرات",
    5: " ارسال شده به مخابرات (همانند وضعیت 4)",
    6: " خطا در ارسال پیام که توسط سر شماره پیش می آید و به معنی عدم رسیدن پیامک می‌باشد (failed)",
    10: "رسیده به گیرنده (Delivered)",
    11: "نرسیده به گیرنده ، این وضعیت به دلایلی از جمله خاموش یا خارج از دسترس بودن گیرنده اتفاق می افتد (Undelivered)",
    13: " ارسال پیام از سمت کاربر لغو شده یا در ارسال آن مشکلی پیش آمده که هزینه آن به حساب برگشت داده می‌شود",
    14: " بلاک شده است، عدم تمایل گیرنده به دریافت پیامک از خطوط تبلیغاتی که هزینه آن به حساب برگشت داده می‌شود",
    100: " شناسه پیامک نامعتبر است"
    " ( به این معنی که شناسه پیام در پایگاه داده کاوه نگار ثبت نشده است یا متعلق به شما نمیباشد )",
}
