import requests
from django.conf import settings
import json
import logging

logger = logging.getLogger("notification")

class SMSAla:
    HEADERS = {"Content-Type": "application/json"}
    API = {
        "post_send_sms": settings.SMSALA_WEB_API,
    }
    DEFAULT_DATA = {
        "api_id": settings.SMSALA_API_ID,
        "api_password": settings.SMSALA_API_PASSWORD,
        "sms_type": "T",
        "encoding": "T",
        "sender_id": "facebook",
        "phonenumber": None, #Phone Number Format is Country Code + Phone Number. Example: ************
        "templateid": None,
        "textmessage": "",
        "V1": None,
        "V2": None,
        "V3": None,
        "V4": None,
        "V5": None,
        "ValidityPeriodInSeconds": 60,
        "uid": "xyz",
        # "callback_url": "",
        "pe_id": None,
        "template_id": None
    }

    @staticmethod
    def _response(response):
        logger.info(msg=str(response.json()))
        return response.status_code == 200, response.json()

    @staticmethod
    def _get(url, params={}):
        return requests.get(url=url, headers=SMSAla.HEADERS, params=params)

    @staticmethod
    def _post(url, data={}, params={}):
        return requests.post(url=url, headers=SMSAla.HEADERS, data=json.dumps(data), params=params)

    @staticmethod
    def _put(url, data={}, params={}):
        return requests.put(url=url, headers=SMSAla.HEADERS, data=json.dumps(data), params=params)

    @staticmethod
    def send_sms(**kwargs):
        default_data = SMSAla.DEFAULT_DATA.copy()
        merged_data = {**default_data, **kwargs}
        logger.info("notification| {}".format(merged_data))
        try:
            return SMSAla._response(SMSAla._post(SMSAla.API["post_send_sms"], data=merged_data))
        except Exception as e:
            logger.error("notification| {}".format(str(e)))
            return False, {"exception": str(e)}
    

    @staticmethod
    def create_sms(user, text):
        from notification.models import SMS
        # logger.info("notification| {}".format)
        SMS.objects.create(user=user, text=text)
