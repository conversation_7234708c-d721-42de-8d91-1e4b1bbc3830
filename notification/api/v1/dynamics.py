import logging

from accounts.models import User
from constants import SMSType
from core.http import JsonResponse
from core.validators import mobile_number_validator
from django.conf import settings
from notification.models import SMS
from notification.utils import GetKavenegarAPI
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAdminUser, IsAuthenticated
from rest_framework.views import APIView

logger = logging.getLogger("notification")
api = GetKavenegarAPI.get_api("4F3570545351474733357A3352344F68587531656C6F37616F677A4F41733355")


class DynamicsAPI(APIView):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (IsAuthenticated, IsAdminUser)

    def post(self, request):
        self.exception_data = {"message": "شماره همراه الزامی است.", "status": 400, "code": 1001}
        mobile_number = request.data["mobile_number"]

        self.exception_data = {"message": "شماره همراه اشتباه است.", "status": 400, "code": 1002}
        mobile_number_validator(mobile_number)

        self.exception_data = {"message": "متن پیام الزامی است.", "status": 400, "code": 1003}
        text = request.data["text"]

        self.exception_data = {"message": "خطا در ایجاد کاربر.", "status": 400, "code": 1004}
        user = User.objects.get_or_create(username=mobile_number)[0]

        self.exception_data = {"message": "خطا در ارسال پیامک.", "status": 400, "code": 1005}
        response = api.sms_send(
            params={
                "sender": settings.KAVENEGAR_SENDER,
                "receptor": mobile_number,
                "message": text,
            }
        )

        self.exception_data = {"message": "پیامک ارسال شد. خطا در ذخیره سازی پیامک.", "status": 400, "code": 1006}
        sms = SMS.objects.create(
            message_id=response[0]["messageid"],
            status_code=response[0]["status"],
            text=text,
            user=user,
            type=SMSType.DYNAMICS,
        )

        return JsonResponse(message="پیامک با موفقیت ارسال شد.")
