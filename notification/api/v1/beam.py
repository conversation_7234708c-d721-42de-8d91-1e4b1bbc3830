import logging
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from core.authentication import NotifCustomAuthentication
from core.notification.beam import BeamsInterface
from saleor.utils import get_email

logger = logging.getLogger("notification")


class BeamAuthAPI(APIView):
    authentication_classes = (NotifCustomAuthentication,)
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        beams_client = BeamsInterface()
        response = beams_client.authenticate_beams(
            request.query_params["beam_id"],
        )
        return JsonResponse(response)
