import logging

from django.http import JsonResponse
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from core.authentication import CustomAuthentication
from core.notification.pusher import PusherInterface

logger = logging.getLogger("notification")


class PusherAuthAPI(APIView):
    authentication_classes = (CustomAuthentication,)
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        pusher_obj = PusherInterface()
        self.exception_data = {"message": "error occurred while authenticating pusher", "status": 400}
        response = pusher_obj.authenticate_pusher(
            channel_name=request.data.get("channel_name"),
            socket_id=request.data.get("socket_id"),
        )
        return JsonResponse(
            data=response
        )
